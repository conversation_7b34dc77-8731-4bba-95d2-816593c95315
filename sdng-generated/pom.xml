<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>sd-parent</artifactId>
		<groupId>ru.naumen</groupId>
		<version>*********</version>
		<relativePath>../sdng-parent/pom.xml</relativePath>
	</parent>

	<artifactId>sdng-generated</artifactId>
	<name>sdng-generated [module contains automatically generated code]</name>

	<dependencies>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>script-api</artifactId>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>commons-util</artifactId>
		</dependency>

		<dependency>
			<groupId>org.antlr</groupId>
			<artifactId>antlr4</artifactId>
		</dependency>

		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
		</dependency>

		<dependency>
			<groupId>jakarta.persistence</groupId>
			<artifactId>jakarta.persistence-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>

		<dependency>
			<groupId>jakarta.inject</groupId>
			<artifactId>jakarta.inject-api</artifactId>
		</dependency>

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<forceLegacyJavacApi>true</forceLegacyJavacApi>
					<source>21</source>
					<target>21</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>jaxb2-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>licensingPolicy</id>
						<goals>
							<goal>xjc</goal>
						</goals>
						<phase>generate-sources</phase>
						<configuration>
							<sources>
								<source>${project.basedir}/src/main/resources/xsd/licensingPolicy.xsd</source>
							</sources>
							<staleFileDirectory>${project.build.directory}/generated-sources/jaxb</staleFileDirectory>
							<outputDirectory>${project.build.directory}/generated-sources/jaxb/licensingPolicy
							</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>ldapSettings</id>
						<goals>
							<goal>xjc</goal>
						</goals>
						<phase>generate-sources</phase>
						<configuration>
							<sources>
								<source>${project.basedir}/src/main/resources/xsd/ldapSettings.xsd</source>
							</sources>
							<staleFileDirectory>${project.build.directory}/generated-sources/jaxb</staleFileDirectory>
							<outputDirectory>${project.build.directory}/generated-sources/jaxb/ldapSettings
							</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>externalSettings</id>
						<goals>
							<goal>xjc</goal>
						</goals>
						<phase>generate-sources</phase>
						<configuration>
							<sources>
								<source>${project.basedir}/src/main/resources/xsd/externalSettings.xsd</source>
							</sources>
							<staleFileDirectory>${project.build.directory}/generated-sources/jaxb</staleFileDirectory>
							<outputDirectory>${project.build.directory}/generated-sources/jaxb/externalSettings
							</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>fileStorageSettings</id>
						<goals>
							<goal>xjc</goal>
						</goals>
						<phase>generate-sources</phase>
						<configuration>
							<sources>
								<source>${project.basedir}/src/main/resources/xsd/fileStorageSettings.xsd</source>
							</sources>
							<xjbSources>
								<xjbSource>${project.basedir}/src/main/resources/xsd/fileStorageSettings.xjb</xjbSource>
							</xjbSources>
							<staleFileDirectory>${project.build.directory}/generated-sources/jaxb</staleFileDirectory>
							<outputDirectory>${project.build.directory}/generated-sources/jaxb/fileStorageSettings
							</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>fileStorageSettingsDeprecated</id>
						<goals>
							<goal>xjc</goal>
						</goals>
						<phase>generate-sources</phase>
						<configuration>
							<sources>
								<source>src/main/resources/xsd/deprecated/fileStorageSettings.xsd</source>
							</sources>
							<xjbSources>
								<xjbSource>src/main/resources/xsd/deprecated/fileStorageSettings.xjb</xjbSource>
							</xjbSources>
							<staleFileDirectory>${project.build.directory}/generated-sources/jaxb</staleFileDirectory>
							<outputDirectory>${project.build.directory}/generated-sources/jaxb/fileStorageSettingsDeprecated
							</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>jmsSettings</id>
						<goals>
							<goal>xjc</goal>
						</goals>
						<phase>generate-sources</phase>
						<configuration>
							<sources>
								<source>${project.basedir}/src/main/resources/xsd/jms/jmsSettings.xsd</source>
							</sources>
							<xjbSources>
								<xjbSource>${project.basedir}/src/main/resources/xsd/jms/jmsSettings.xjb</xjbSource>
							</xjbSources>
							<staleFileDirectory>${project.build.directory}/generated-sources/jaxb</staleFileDirectory>
							<outputDirectory>${project.build.directory}/generated-sources/jaxb/jmsSettings
							</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>kafkaSettings</id>
						<goals>
							<goal>xjc</goal>
						</goals>
						<phase>generate-sources</phase>
						<configuration>
							<sources>
								<source>${project.basedir}/src/main/resources/xsd/kafka/kafkaSettings.xsd</source>
							</sources>
							<xjbSources>
								<xjbSource>${project.basedir}/src/main/resources/xsd/kafka/kafkaSettings.xjb</xjbSource>
							</xjbSources>
							<staleFileDirectory>${project.build.directory}/generated-sources/jaxb</staleFileDirectory>
							<outputDirectory>${project.build.directory}/generated-sources/jaxb/kafkaSettings
							</outputDirectory>
						</configuration>
					</execution>

					<!-- For tests -->
					<execution>
						<id>TestObject</id>
						<goals>
							<goal>testXjc</goal>
						</goals>
						<phase>generate-sources</phase>
						<configuration>
							<packageName>ru.naumen.core.server.utils.jaxb</packageName>
							<testSources>
								<testSource>${project.basedir}/src/test/resources/ru/naumen/core/server/utils/TestObject.xsd</testSource>
							</testSources>
							<staleFileDirectory>${project.build.directory}/generated-sources/jaxb</staleFileDirectory>
							<outputDirectory>${project.build.directory}/generated-sources/jaxb/test-object
							</outputDirectory>
						</configuration>
					</execution>
				</executions>
				<configuration>
					<extension>true</extension>
				</configuration>
			</plugin>
			<plugin>
				<groupId>ru.naumen</groupId>
				<artifactId>generate-plugin</artifactId>
				<executions>
					<execution>
						<id>default</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>generateSnapshotObject</goal>
							<goal>generateSnapshotFactory</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.antlr</groupId>
				<artifactId>antlr4-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>antlr</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>antlr4</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/generated-sources/antlr4</outputDirectory>
							<sourceDirectory>${basedir}/src/main/resources</sourceDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>add-antlr4-generated-sources</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>add-source</goal>
						</goals>
						<configuration>
							<sources>
								<source>${project.build.directory}/generated-sources/antlr4</source>
							</sources>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.antlr</groupId>
										<artifactId>antlr4-maven-plugin</artifactId>
										<versionRange>[4.12.0,)</versionRange>
										<goals>
											<goal>antlr4</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<execute>
											<runOnIncremental>true</runOnIncremental>
										</execute>
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
</project>
