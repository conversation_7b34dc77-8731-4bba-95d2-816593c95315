package ru.naumen.ndap.server.audit.mappers;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.AbstractDtObject;
import ru.naumen.ndap.server.audit.AuditRequestConfig;
import ru.naumen.ndap.server.bo.audit.NDAPAudit;
import ru.naumen.ndap.shared.Constants;
import ru.naumen.ndap.shared.Constants.NDAPAudit.Synchronization.State;

import java.util.HashMap;

/**
 * Мапперы преобразования {@link NDAPAudit}
 * <AUTHOR>
 * @since 11.10.2022
 */
public class NDAPAuditMappers
{
    @Component
    public static class NDAPAuditEntityToDtoMapper extends AbstractMapper<NDAPAudit, AbstractDtObject>
    {
        @Inject
        public NDAPAuditEntityToDtoMapper()
        {
            super(NDAPAudit.class, AbstractDtObject.class);
        }

        @Override
        public void transform(NDAPAudit from, AbstractDtObject to, @Nullable DtoProperties properties)
        {
            to.setUUID(from.getUUID());
            to.setMetainfo(Constants.NDAPAudit.FQN);
            to.setProperty(Constants.NDAPAudit.CREATION_DATE, from.getCreationDate());
            to.setProperty(Constants.NDAPAudit.AUTHOR_LOGIN, from.getAuthorLogin());
            to.setProperty(Constants.NDAPAudit.DURATION, from.getDuration());
            to.setProperty(Constants.NDAPAudit.ERROR, from.getError());
            to.setProperty(Constants.NDAPAudit.AUDIT_STATE, from.getAuditState());
            boolean syncIsExecuting = from.getSynchronizations()
                    .stream()
                    .anyMatch(s -> State.IN_PROGRESS.getCode().equals(s.getSyncState()));
            to.setProperty(Constants.NDAPAudit.SYNCHRONIZATIONS, syncIsExecuting);
        }
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Component
    public static class NDAPAuditRequestConfigMapper extends AbstractMapper<AuditRequestConfig, HashMap>
    {
        @Inject
        public NDAPAuditRequestConfigMapper()
        {
            super(AuditRequestConfig.class, HashMap.class);
        }

        @Override
        public void transform(AuditRequestConfig from, HashMap to, @Nullable DtoProperties properties)
        {
            to.put(Constants.NDAPAudit.RequestConfig.TYPES, from.types());
        }
    }
}