package ru.naumen.ndap.server.spi.dispatch;

import java.util.List;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.metainfo.server.BeforeAttributeDeleteEvent;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.ndap.server.spi.EditAttrRelationService;

/**
 * Обработчик события удаления атрибута до фиксации изменений.
 * Проверка использования атрибута в качестве доступного из системы мониторинга.
 * <AUTHOR>
 * @since 02.10.19
 */
@Component
public class CheckEditRelationsBeforeDeleteAttributeListener implements ApplicationListener<BeforeAttributeDeleteEvent>
{
    private EditAttrRelationService editAttrRelationService;

    @Inject
    public CheckEditRelationsBeforeDeleteAttributeListener(EditAttrRelationService editAttrRelationService)
    {
        this.editAttrRelationService = editAttrRelationService;
    }

    @Override
    public void onApplicationEvent(BeforeAttributeDeleteEvent event)
    {
        List<String> errors = editAttrRelationService.checkRelatedAttrToExport((Attribute)event.getSource());
        if (!errors.isEmpty())
        {
            event.cancel();
            event.addMessage(String.join("\n", errors));
        }
    }
}
