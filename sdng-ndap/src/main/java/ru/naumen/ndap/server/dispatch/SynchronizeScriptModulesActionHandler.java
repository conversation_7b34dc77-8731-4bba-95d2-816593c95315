package ru.naumen.ndap.server.dispatch;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ActionHandler;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.ndap.server.rest.NDAPScriptModuleService;
import ru.naumen.ndap.shared.SynchronizeScriptModulesAction;

/**
 * {@link ActionHandler}, обрабатывающий действие "Синхронизировать" для скриптовых модулей
 *
 * <AUTHOR>
 * @since 23.03.2023
 */
@Component
public class SynchronizeScriptModulesActionHandler extends
        TransactionalReadActionHandler<SynchronizeScriptModulesAction, EmptyResult>
{
    private final NDAPScriptModuleService ndapScriptModuleService;

    @Inject
    public SynchronizeScriptModulesActionHandler(NDAPScriptModuleService ndapScriptModuleService)
    {
        super(SynchronizeScriptModulesAction.class);
        this.ndapScriptModuleService = ndapScriptModuleService;
    }

    @Override
    public EmptyResult executeInTransaction(SynchronizeScriptModulesAction action,
            ExecutionContext context)
    {
        ndapScriptModuleService.synchronize();
        return new EmptyResult();
    }
}
