package ru.naumen.mobile.controllers.forms;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.google.common.annotations.VisibleForTesting;

import ru.naumen.mobile.logging.MobileLogPrepareUtils;
import ru.naumen.mobile.mapping.dto.form.MobileFormExecutionContext;
import ru.naumen.mobile.mapping.dto.form.MobileFormType;
import ru.naumen.mobile.services.forms.MobileFormValuesContext;

/**
 * Контекст для вычисления изменившихся атрибутов на форме (вычислымими на форме
 * скриптами)
 *
 * <AUTHOR>
 * @since Oct 13, 2016
 */
public class MobileFormRecalculateContext implements MobileFormValuesContext
{
    // Идентификатор объекта
    private String uuid;
    // Код изменившегося атрибута
    private String changedAttribute;
    // Значения атрибутов на форме
    private Map<String, Object> attributes;
    /*
     * Словарь зависимостей вычислимых атрибутов. Ключ - код атрибута,
     * значение - список атрибутов, которые зависят от этого атрибута
     */
    private Map<String, Collection<String>> dependencyMap;
    /*
     * Словарь зависимостей кодов атрибутов, участвующих в вычислениях ограничений на "Дату" и "Дату/Время". Ключ - код
     * атрибута, значение - список атрибутов, от которых зависит этот атрибут
     */
    private Map<String, Collection<String>> restrictionDependencyMap;
    /*
     * Словарь зависимостей кодов атрибутов скрипте фильтрации. Ключ - код атрибута,
     * значение - список атрибутов, которые зависят от этого атрибута
     */
    private Map<String, Collection<String>> filtrationDependencyMap;
    /*
     * Код контента
     */
    private String contentCode;
    /*
     * Тип контента
     */
    private String formType;
    /*
     * Доступные типы для выбора на форме
     */
    private MobileFormExecutionContext context;

    @VisibleForTesting
    public void setUuid(String uuid)
    {
        this.uuid = uuid;
    }

    @VisibleForTesting
    public void setAttribute(String key, Object value)
    {
        if (attributes == null)
        {
            attributes = new HashMap<>();
        }
        attributes.put(key, value);
    }

    @Override
    public String getUuid()
    {
        return uuid;
    }

    public String getChangedAttribute()
    {
        return changedAttribute;
    }

    @Override
    public Map<String, Object> getAttributes()
    {
        return attributes;
    }

    @Override
    public MobileFormExecutionContext getExecutionContext()
    {
        if (context == null)
        {
            context = new MobileFormExecutionContext();
        }
        return context;
    }

    public Map<String, Collection<String>> getDependencyMap()
    {
        return dependencyMap;
    }

    public Map<String, Collection<String>> getRestrictionDependencyMap()
    {
        if (restrictionDependencyMap == null)
        {
            restrictionDependencyMap = Collections.emptyMap();
        }
        return restrictionDependencyMap;
    }

    public Map<String, Collection<String>> getFiltrationDependencyMap()
    {
        return filtrationDependencyMap;
    }

    @Override
    public String getContentCode()
    {
        return contentCode;
    }

    @Override
    public MobileFormType getFormType()
    {
        return MobileFormType.of(formType);
    }

    @Override
    public String toString()
    {
        return "MobileFormRecalculateContext [uuid=" + uuid + ", changedAttribute=" + changedAttribute
                + ", attributes=" + MobileLogPrepareUtils.replaceBase64ImagesInValues(attributes)
                + ", dependencyMap=" + dependencyMap + ", filtrationDependencyMap=" + filtrationDependencyMap
                + ", contentCode=" + contentCode + ", context=" + context + "]";
    }
}
