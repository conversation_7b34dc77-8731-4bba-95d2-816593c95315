package ru.naumen.mobile.metainfoadmin.shared.attributes;

import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.mobile.metainfoadmin.shared.MobileContentType;

/**
 * Действие перемещения мобильного атрибута в списке атрибутов списка
 *
 * <AUTHOR>
 * @since 08 мая 2015 г.
 */
@AdminAction
public class MoveMobileAttributeAction extends AbstractMoveMobileAttributeAction
{
    public static MoveMobileAttributeAction down(MobileContentType formType, String formUuid, String attrUuid)
    {
        return new MoveMobileAttributeAction(formType, formUuid, attrUuid, 1);
    }

    public static MoveMobileAttributeAction down(MobileContentType formType, String formUuid, String propertiesListUuid,
            String attrUuid)
    {
        return new MoveMobileAttributeAction(formType, formUuid, propertiesListUuid, attrUuid, 1);
    }

    public static MoveMobileAttributeAction up(MobileContentType formType, String formUuid, String attrUuid)
    {
        return new MoveMobileAttributeAction(formType, formUuid, attrUuid, -1);
    }

    public static MoveMobileAttributeAction up(MobileContentType formType, String formUuid, String propertiesListUuid,
            String attrUuid)
    {
        return new MoveMobileAttributeAction(formType, formUuid, propertiesListUuid, attrUuid, -1);
    }

    public MoveMobileAttributeAction()
    {
    }

    public MoveMobileAttributeAction(MobileContentType formType, String formUuid, String attrUuid, int direction)
    {
        super(formType, formUuid, attrUuid, direction);
    }

    public MoveMobileAttributeAction(MobileContentType formType, String formUuid, String propertiesListUuid,
            String attrUuid, int direction)
    {
        super(formType, formUuid, propertiesListUuid, attrUuid, direction);
    }

}
