package ru.naumen.mobile.services.auth.custom;

import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import ru.naumen.mobile.MobileAuthenticationException;
import ru.naumen.mobile.controllers.auth.contexts.custom.MobileCustomAuthContext;
import ru.naumen.mobile.controllers.auth.contexts.custom.MobileCustomAuthExecContext;
import ru.naumen.mobile.services.auth.custom.session.CustomAuthSession;

/**
 * Сервис с методами для пользовательской аутентификации в МК
 *
 * <AUTHOR>
 * @since 08.09.2021
 */
public interface MobileCustomAuthService
{
    /**
     * Проверяет необходимость использования пользовательской аутентификации.
     * <b>Внимание!</b> Доступно только когда пользовательская аутентификация настроена и включен JWT
     *
     * @return true - да, false - нет
     */
    boolean isCustomAuthenticationAvailable();

    /**
     * Возвращает настройки пользовательской аутентификации
     */
    CustomAuthSettings getSettings();

    /**
     * TODO: написать комментарий в рамках NSDPRD-15623
     */
    Object execute(MobileCustomAuthExecContext authExecContext);

    /**
     * Обрабатывает обратный вызов в ходе пользовательской аутентификации и формирует URL для перенаправления
     *
     * @param request объект запроса
     */
    String redirect(HttpServletRequest request);

    /**
     * Выполняет аутентификацию пользователя по данным полученным в ходе пользовательской аутентификации
     *
     * @param context тело запроса с данными для пользовательской аутентификации
     * @param request объект запроса
     * @param response объект ответа
     */
    CustomAuthentication authenticate(MobileCustomAuthContext context, HttpServletRequest request,
            HttpServletResponse response);

    /**
     * Возвращает сессию пользовательской аутентификации.
     *
     * @param identifier идентификатор
     * @throws MobileAuthenticationException если сессия не найдена
     */
    CustomAuthSession getSession(@Nullable String identifier);
}
