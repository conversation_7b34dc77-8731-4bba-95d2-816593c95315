package ru.naumen.mobile.services;

import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Stream;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.geo.GeoService;
import ru.naumen.metainfo.shared.mobile.LatLng;

/**
 * Сервис вспомогательных методов контроллеров с более сложной логикой
 */
@Component
public class MobileServiceHelper
{
    private final GeoService geoService;

    /**
     * Производит проверку параметров запроса. В REST метод должен передаваться максимум один параметр.
     */
    public static void checkFetchParamsCount(@Nullable String sinceUUID, @Nullable String middleUUID,
            @Nullable String beforeUuid)
    {
        long nonNullParams = Stream.of(sinceUUID, middleUUID, beforeUuid)
                .filter(Objects::nonNull)
                .limit(2)
                .count();
        if (nonNullParams > 1)
        {
            throw new FxException("Only one parameter must be specified.");
        }
    }

    @Inject
    public MobileServiceHelper(GeoService geoService)
    {
        this.geoService = geoService;
    }

    /**
     * Выполняет переданную функцию.
     * На время её выполнения устанавливает значение контекстной переменной geo
     *
     * @param geo местоположение
     * @param callable функция
     */
    public <V> V callWithGeo(@Nullable LatLng geo, final Supplier<V> callable)
    {
        geoService.setGeo(geo);
        V result = callable.get();
        geoService.remove();

        return result;
    }
}
