package ru.naumen.mobile.mapping.dto.form.useractionparams;

import static ru.naumen.mobile.mapping.dto.form.MobileFormType.PARAMETRIZED_USER_ACTION;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.bo.ISimpleBO;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.mobile.actions.ObjectAction;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.mobile.mapping.dto.form.MobileFormAttributesComposer;
import ru.naumen.mobile.mapping.dto.form.MobileFormExecutionContext;
import ru.naumen.mobile.mapping.dto.form.MobileFormMappingHelper;
import ru.naumen.mobile.mapping.dto.form.MobileFormResult;
import ru.naumen.mobile.mapping.dto.form.MobileFormType;
import ru.naumen.mobile.mapping.dto.form.dtos.FormAttributeDtObject;
import ru.naumen.mobile.services.actions.MobileObjectActionsService;
import ru.naumen.mobile.services.actions.MobileUserActionsFormService;
import ru.naumen.mobile.services.attributes.MobileAttributeService;
import ru.naumen.mobile.services.attributes.MobileUserEventFakeMetaClass;
import ru.naumen.mobile.services.forms.MobileAttributeDependencies;
import ru.naumen.mobile.services.forms.MobileFormsService;
import ru.naumen.mobile.services.objects.MobileCardsSettingsService;
import ru.naumen.mobile.services.objects.MobileObjectsRetriever;
import ru.naumen.mobile.services.recalculate.MobileRecalculateService;
import ru.naumen.mobile.services.recalculate.containers.MobileRecalculateAllContainer;

/**
 * Позволяет получить список доступных для заполнения атрибутов на форме заполнения параметров пользовательского ДПС
 * в МК, переводя их в {@link FormAttributeDtObject}, производит маппинг значений в формат понятный МК
 *
 * <AUTHOR>
 * @since 23.03.2022
 */
@Component
public class MobileUserActionParamsFormAttributesComposer implements MobileFormAttributesComposer
{
    private final MobileAttributeService attributeService;
    private final MobileUserActionsFormService userActionsFormService;
    private final MobileRecalculateService recalculateService;
    private final MobileFormMappingHelper formMappingHelper;
    private final MobileFormsService formsService;
    private final MobileCardsSettingsService cardsService;
    private final IPrefixObjectLoaderService loaderService;
    private final MobileObjectActionsService objectActionsService;
    private final MetainfoUtils metainfoUtils;
    private final MobileObjectsRetriever objectsRetriever;

    public MobileUserActionParamsFormAttributesComposer(
            final MobileAttributeService attributeService,
            final MobileUserActionsFormService userActionsFormService,
            final MobileRecalculateService recalculateService,
            final MobileFormMappingHelper formMappingHelper,
            final MobileFormsService formsService,
            final MobileCardsSettingsService cardsService,
            final IPrefixObjectLoaderService loaderService,
            final MobileObjectActionsService objectActionsService,
            final MetainfoUtils metainfoUtils,
            final MobileObjectsRetriever objectsRetriever)
    {
        this.attributeService = attributeService;
        this.userActionsFormService = userActionsFormService;
        this.recalculateService = recalculateService;
        this.formMappingHelper = formMappingHelper;
        this.formsService = formsService;
        this.loaderService = loaderService;
        this.cardsService = cardsService;
        this.objectActionsService = objectActionsService;
        this.metainfoUtils = metainfoUtils;
        this.objectsRetriever = objectsRetriever;
    }

    @Override
    public MobileFormType getFormType()
    {
        return PARAMETRIZED_USER_ACTION;
    }

    @Override
    public MobileFormResult compose(final Map<String, Object> rawAttributes, final @Nullable String eventUuid,
            final MobileFormExecutionContext executionContext)
    {
        Preconditions.checkNotNull(eventUuid, "Event uuid must be specified");

        final String ownerUuid = executionContext.getObjectUuid();
        Preconditions.checkNotNull(ownerUuid, "Object uuid must be specified in execution context");
        final ISimpleBO ownerObject = loaderService.get(ownerUuid);

        // получаем действие, по ходу дела проверяя наличие права на его выполнение
        final String contentCode = executionContext.getContentCode();
        final String cardCode = executionContext.getCardCode();
        final ObjectAction action = getUserAction(eventUuid, ownerObject, cardCode, contentCode);
        final CustomForm form = userActionsFormService.getAssociatedForm(eventUuid);

        // если у ДПС нет параметров, то нужно вернуть пустую форму
        if (form == null)
        {
            return new MobileFormResult(null, Collections.emptyList());
        }

        final DtObject ownerDto = objectsRetriever.loadDtObject(ownerUuid);
        final MobileUserEventFakeMetaClass metaClass = userActionsFormService.buildMetaClass(eventUuid, form, ownerDto);

        // вычисляем атрибуты, доступные на форме и их зависимости друг от друга
        final Collection<String> applicableAttributeCodes = metaClass.getAttributeCodes();
        final Set<Attribute> applicableAttributes = attributeService.getAttributes(metaClass, applicableAttributeCodes);
        final MobileAttributeDependencies dependencies =
                formsService.getDependencies(metaClass, applicableAttributes, PARAMETRIZED_USER_ACTION);

        // выполняем вычисление значений параметров на форме и перевычисление зависимых от них
        final DtObject object = userActionsFormService.buildDtObject(metaClass);
        final MobileRecalculateAllContainer recalculateContainer = recalculateService.recalculateAllAttributes(
                metaClass, object, dependencies, Collections.emptyMap(), applicableAttributeCodes,
                PARAMETRIZED_USER_ACTION, null, false, ownerDto);

        // получаем список атрибутов, доступных только для отображения (в принципе не редактируемые)
        final Collection<String> onlyVisibleAttributeCodes =
                formsService.getOnlyVisibleAttributes(metaClass, PARAMETRIZED_USER_ACTION);
        // добавляем не редактируемые атрибуты с пустым значением в список скрытых атрибутов, чтобы не отображать их
        // на форме
        final DtObject recalculatedObject = recalculateContainer.getObject();
        final String caption = metainfoUtils.getLocalizedValue(action.getCaption());

        // получаем список обязательных для заполнения атрибутов на форме
        final Collection<String> requiredAttributeCodes =
                formsService.getRequiredAttributes(metaClass, PARAMETRIZED_USER_ACTION);
        final Collection<FormAttributeDtObject> attributesOnForm = formMappingHelper.buildFormAttributesMap(
                recalculatedObject, metaClass, applicableAttributeCodes, recalculateContainer.getHiddenAttributes(),
                requiredAttributeCodes, onlyVisibleAttributeCodes, recalculateContainer.getMetadata(), null);
        formMappingHelper.fillValuesAndDependencies(recalculatedObject, metaClass, attributesOnForm, dependencies);
        return new MobileFormResult(caption, attributesOnForm);
    }

    private ObjectAction getUserAction(final String eventUuid, final ISimpleBO ownerObject,
            @Nullable final String cardCode, @Nullable final String contentCode)
    {
        final ObjectCard card =
                Objects.requireNonNull(cardsService.getObjectCard(ownerObject, cardCode), "Object card must be not null");
        final MobilePropertiesListContent content = (contentCode != null)
                ? cardsService.getPropertiesListContent(ownerObject, card, contentCode)
                : null;

        //  в случае когда действие недоступно (выключено, нет прав, ...) - будет выброшена ошибка
        return objectActionsService.getUserAction(ownerObject, card, content, eventUuid);
    }
}
