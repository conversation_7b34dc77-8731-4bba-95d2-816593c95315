package ru.naumen.core.client.widgets;

import jakarta.annotation.Nullable;

import com.google.gwt.event.dom.client.HasClickHandlers;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.HasClickHandlersStub;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.shared.utils.ReadyState;

/**
 * Заглушка для DialogDisplay<br>
 * Имеет заглушки для обработчиков нажатия на кнопки "Применить" и "Отмена"<br>
 * Если где-то будет появляться NullPointerException, то тогда надо будет реализовать возвращение значения-заглушки, пока null
 * <AUTHOR>
 * @since 07.09.2011
 *
 */
public class DialogDisplayStub implements PropertyDialogDisplay
{

    private final HasClickHandlers applyClickHandler = new HasClickHandlersStub();
    private final HasClickHandlers cancelClickHandler = new HasClickHandlersStub();

    @Override
    public <T> PropertyRegistration<T> add(Property<T> property)
    {
        return null;
    }

    @Override
    public <T> PropertyRegistration<T> add(Property<T> property, String debugId)
    {
        return null;
    }

    @Override
    public void addAttentionMessage(SafeHtml message)
    {
    }

    @Override
    public void addErrorMessage(SafeHtml message)
    {
    }

    @Override
    public <T> PropertyRegistration<T> addProperty(Property<T> property, int index)
    {
        return null;
    }

    @Override
    public <T, R> PropertyRegistration<T> addPropertyAfter(Property<T> property, PropertyRegistration<R> after)
    {
        return null;
    }

    @Override
    public Widget asWidget()
    {
        return null;
    }

    @Override
    public void clearProperties()
    {
    }

    @Override
    public void destroy()
    {
    }

    @Override
    public void display()
    {
    }

    @Override
    public HasClickHandlers getApplyButton()
    {
        return applyClickHandler;
    }

    @Override
    public AbstractMessageWidget getAttention()
    {
        return null;
    }

    @Override
    public int getBaseTabIndex()
    {
        return 0;
    }

    @Override
    public HasClickHandlers getCancelButton()
    {
        return cancelClickHandler;
    }

    @Override
    public String getCaptionText()
    {
        return null;
    }

    @Override
    public Focusable getFirstFocusElement()
    {
        return null;
    }

    @Override
    public int getNextBaseTabIndex()
    {
        return 0;
    }

    @Override
    public int getPropertiesCount()
    {
        return 0;
    }

    @Nullable
    @Override
    public <T> PropertyRegistration<T> getPropertyRegistration(Property<T> property)
    {
        return null;
    }

    @Override
    public ReadyState getReadyState()
    {
        return new ReadyState(this);
    }

    @Override
    public void hide()
    {
    }

    @Override
    public int indexOf(Property<?> property)
    {
        return -1;
    }

    @Override
    public int indexOf(PropertyRegistration<?> registration)
    {
        return -1;
    }

    @Override
    public void insertRow(PropertyRegistration<?> registration, boolean createNewRow, @Nullable String debugCode)
    {

    }

    @Override
    public boolean isBlocked()
    {
        return false;
    }

    @Override
    public void ready(IReadyCallback callback)
    {

    }

    @Override
    public SynchronizationCallbackRegistration registerSynchronization(SynchronizationCallback callback)
    {
        return null;
    }

    @Override
    public void resetTabOrder()
    {

    }

    @Override
    public void setApplyButtonText(String text)
    {
        // TODO Auto-generated method stub

    }

    @Override
    public void setBaseTabIndex(int baseTabIndex)
    {

    }

    @Override
    public void setCaptionText(String caption)
    {
    }

    @Override
    public void setFixed(boolean fixed)
    {
        // TODO Auto-generated method stub

    }

    @Override
    public void setDialogWidth(DialogWidth value)
    {
    }

    @Override
    public void setHTML(String html)
    {
    }

    @Override
    public <T> PropertyRegistration<T> setProperty(Property<T> property, int index)
    {
        return null;
    }

    @Override
    public void setPropertyVisible(PropertyRegistration<?> registration, boolean visible)
    {

    }

    @Override
    public void startProcessing()
    {
    }

    @Override
    public void stopProcessing()
    {
    }

    @Override
    public void unregister(PropertyRegistration<?> registration)
    {

    }

    @Override
    public void updateTabOrder()
    {

    }
}
