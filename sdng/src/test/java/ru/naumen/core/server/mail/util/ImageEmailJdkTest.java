package ru.naumen.core.server.mail.util;

import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static ru.naumen.core.server.mail.util.ImageEmail.*;

/**
 * Тестирование {@link ru.naumen.core.server.mail.util.ImageEmail}
 * <AUTHOR>
 * @since 11.04.2019
 */
public class ImageEmailJdkTest
{
    @Test
    public void testCorrectStyles()
    {
        String styles = "<style type=\"text/css\">Тут находятся стили \n"
                + "Следующая строка</style>\n"
                + "<style type=\"text/css\">Тут еще стили </style>";
        String text = "Текст сообщения";
        String msg = styles + text;
        String correctMsg = String.format(HTML_MESSAGE_START, styles) + text + HTML_MESSAGE_END;
        String result = correctStyles(msg, true);
        assertEquals("HTML содержимое скорректировано неверно", correctMsg, result);
    }
}
