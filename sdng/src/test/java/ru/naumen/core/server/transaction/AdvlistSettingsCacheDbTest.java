package ru.naumen.core.server.transaction;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import jakarta.inject.Inject;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.metastorage.impl.userinfo.AdvlistSettingsCache;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.objectlist.shared.advlist.AdvlistSettings;

/**
 * Тестирование транзакционности кеша представлений адвлистов
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class AdvlistSettingsCacheDbTest
{

    @Inject
    private AdvlistSettingsCache cache;

    private ExecutorService threadPool;

    /**
     * Тестирование того, что потоки видят неизменное состояние кеша, пока транзакция инвалидации значения
     * не закоммичена.
     * @throws Exception
     */
    @Test
    public void cacheTransactionalTest() throws Exception
    {
        final String UUID = UUIDGenerator.get().nextUUID();
        AdvlistSettings settings = new AdvlistSettings();

        cache.put(UUID, settings);

        Callable<AdvlistSettings> invalidateTask = new Callable<AdvlistSettings>()
        {

            @Override
            public AdvlistSettings call() throws Exception
            {
                TransactionRunner.call(new Callable<Void>()
                {

                    @Override
                    public Void call() throws Exception
                    {
                        cache.invalidate(UUID);
                        Thread.sleep(3000);
                        return null;
                    }

                });
                return cache.get(UUID);

            }
        };
        Callable<AdvlistSettings> getTask = new Callable<AdvlistSettings>()
        {

            @Override
            public AdvlistSettings call() throws Exception
            {
                Thread.sleep(1000);
                return cache.get(UUID);
            }
        };
        Future<AdvlistSettings> invalidateTaskResult = threadPool.submit(invalidateTask);
        Future<AdvlistSettings> getTaskResult = threadPool.submit(getTask);

        AdvlistSettings settingsInvalidateTask = invalidateTaskResult.get();
        AdvlistSettings settingsGetTask = getTaskResult.get();

        Assert.assertNull(settingsInvalidateTask);
        Assert.assertNotNull(settingsGetTask);

    }

    @Before
    public void startPool()
    {
        threadPool = Executors.newFixedThreadPool(2);
    }

    @After
    public void stopPool()
    {
        threadPool.shutdown();
    }

}
