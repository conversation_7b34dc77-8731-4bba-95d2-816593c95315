#
#Wed Jun 05 12:08:02 YEKT 2013
creationDate=Дата создания
toReportsList=к шаблону ''{0}''
showCapitalLetter=Показать
resetCapitalLetter=Сбросить
addReportFormTitle=Добавление
reportTemplates=Шаблоны отчетов
reportTemplatesAndPrintingFormsDescription=Используется для загрузки и настройки шаблонов отчетов и печатных форм
reportTemplatesAreLoaded=Шаблоны отчетов загружены
parameters=Параметры...
addReport=Добавить отчет
csv=CSV
reportTemplateAdding=Добавление шаблона
html=HTML
helpText=Отчет | {0}
toReportTemplateCard=в карточку шаблона ''{0}''
reportTemplate=Шаблон отчета
reportTemplateEditing=Редактирование шаблона
saveTo=Сохранить в
back=Назад
contentParametersEditing=Редактирование параметров отчета
xlsx=XLSX
docx=DOCX
addTemplate=Добавить шаблон
report=Отчет
reportPF=Отчет, печатная форма
saveReportButton=Сохранить...
exportReportButton=Экспорт
sendReportButton=Отправить
pdf=PDF
exportPDF=Экспорт в PDF
exportXLSX=Экспорт в XLSX
exportDOCX=Экспорт в DOCX
exportEmail=Отправить по почте
rebuildReport=Перестроить
loadReportTemplates=Загрузить шаблоны отчетов
template=Шаблон
editableParameters=Редактируемые параметры
createNewReportWithCurrentParameters=Добавить по текущим параметрам
resetToDefaultValues=Сбросить к значениям по умолчанию
generationInProgress=Выполняется построение. Пожалуйста, подождите.
buildingInProgress=В процессе построения
error=Ошибка
editParamsFormScriptParamsAttention=Скриптом шаблона отчета вычисляются значения по умолчанию для параметров: {0}.
editParamsFormSettingsParamsAttention=В контенте переопределены значения по умолчанию для параметров: {0}.
reset=Сбросить
reportExport=Экспорт отчета ({0})
sendByMail=Отправка на почту отчета ({0})
reportSendDescription=Отчёт "{0}" в формате {1} будет отправлен на указанный ниже адрес. Отправка будет подготовлена и произведена в порядке очереди.
syncExportMemoryLimitExceeded=Превышен максимальный размер памяти, доступный для оперативной выгрузки отчета через интерфейс приложения. Выгрузка будет подготовлена в порядке очереди.
defferedExportMail=Укажите адрес электронной почты. На этот адрес будет отправлена ссылка для скачивания файла с отчетом.
defferedSendByMail=Укажите адрес электронной почты (можно указать несколько адресов через запятую).
selectedReportInstances=выбранные экземпляры отчётов
templateSelected=выбранные шаблоны отчетов