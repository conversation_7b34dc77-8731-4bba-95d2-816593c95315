toReportsList=do szablonu "{0}"
showCapitalLetter=<PERSON><PERSON><PERSON>
resetCapitalLetter=Zresetuj
addReportFormTitle=Dodaj
reportTemplates=Szablony raportów
reportTemplatesAreLoaded=Szablony raportów załadowane
parameters=Parametry…
addReport=Dodaj raport
csv=CSV
reportTemplateAdding=Dodawanie szablonu
html=HTML
helpText=Raport | {0}
toReportTemplateCard=do karty szablonu "{0}"
reportTemplate=Szablon raportu
reportTemplateEditing=Edycja szablonu
saveTo=Zapisz w
back=Powróć
contentParametersEditing=Edycja parametrów raportu
xlsx=XLSX
docx=DOCX
addTemplate=Dodaj szablon
report=Raport
reportPF=Raport, forma drukowana
saveReportButton=Zapisz...
exportReportButton=Eksportowanie
pdf=PDF
exportPDF=Eksport do PDF
exportXLSX=Eksport do XLSX
exportDOCX=Eksport do DOCX
exportEmail=Wyślij pocztą
rebuildReport=Przebuduj
loadReportTemplates=Wgryj szablony raportów
template=Szablon
createNewReportWithCurrentParameters=Dodaj według parametrów bieżących
resetToDefaultValues=Zresetuj do wartości domyślnych
generationInProgress=Trwa budowa. Proszę czekać.
buildingInProgress=W trakcie budowy
error=Błąd
editParamsFormSettingsParamsAttention=Wartości domyślne dla parametrów są predefiniowane w treści: {0}.
reportExport=Eksport raportu ({0})
sendByMail=Wysyłanie raportu na pocztę ({0})
reset=Zresetuj
syncExportMemoryLimitExceeded=Przekroczono maksymalny rozmiar pamięci dostępny do szybkiego wgrywania raportu za pośrednictwem interfejsu aplikacji. Wgrywanie zostanie przygotowane według kolejności.
defferedExportMail=Podaj swój adres e-mail. Na ten adres zostanie wysłany link do pobrania pliku z raportem.
defferedSendByMail=Podaj adres e-mail (dozwolone podać wiele adresów rozdzielanych przecinkami).
selectedReportInstances=wybrane egzemplarzy raportów
templateSelected=wybrane szablony raportów
creationDate=Data utworzenia
reportTemplatesAndPrintingFormsDescription=Służy do załadowania i konfiguracji szablonów raportów i form drukowanych
sendReportButton=Wyślij
editableParameters=Parametry edytowalne
editParamsFormScriptParamsAttention=Wartości domyślne dla parametrów są obliczane przez skrypt szablonu raportu: {0}.
reportSendDescription=Raport "{0}" w formacie {1} zostanie wysłany na poniższy adres. Przesyłka zostanie przygotowana i wykonana według kolejności.
