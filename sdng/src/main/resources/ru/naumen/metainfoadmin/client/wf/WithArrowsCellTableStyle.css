@eval linkColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().linkColor();

@external hint;

.cellTableEvenRow,
.cellTableOddRow {
	cursor: pointer;
	}

.stateTitleColumn {}

.titleColumn,
.codeColumn {
	padding-left: 15px !important;
	padding-right: 10px !important;
	white-space: nowrap;
	}
	.titleColumn > a,
	.stateTitleColumn > a {
		color: linkColor;
		text-decoration: none;
		display: inline !important;
		}
		.titleColumn > a:hover,
		.stateTitleColumn > a:hover {
			text-decoration: underline;
			}

.folderRow > .titleColumn {
	color: #597398;
	}
.folderRowRemoved > .titleColumn {
	color: #808080 !important;
	}
.folderRow > .titleColumn,
.folderRowRemoved > .titleColumn {
	font-weight: 600;
	}
.widthLimit {
	max-width: 35em;
	word-wrap: break-word;
	}

.columnWithLineBreak > div > div {
	white-space: nowrap;
	}

.columnWithLineBreak > div > a {
	color: linkColor;
	text-decoration: none;
	white-space: nowrap;
	}
	.columnWithLineBreak > div > a:hover {
		text-decoration: underline;
		}

.arrowUpDown4Crumbs img {
	margin: 6px 0 0 5px !important;
	}

.arrowUpDownHeader4Crumbs {
	padding: 0;
	}

.firstHeader4Crumbs {
	padding-left: 22px;
	}

.header4Crumbs {
	padding-left: 16px;
	}

.titleCellWithHint > div {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    flex-direction: row;
    }
    .titleCellWithHint > div > span.hint {
        margin-left: 4px;
        cursor: default;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        }
        .titleCellWithHint > div > span.hint:before {
            cursor: default;
            }

.disabledSelectableItem > td {
    color: #999 !important;
    font-style: italic;
    }