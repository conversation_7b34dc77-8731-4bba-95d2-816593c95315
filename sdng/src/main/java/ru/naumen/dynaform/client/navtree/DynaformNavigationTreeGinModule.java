/**
 * 
 */
package ru.naumen.dynaform.client.navtree;

import com.google.common.collect.Lists;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;
import ru.naumen.core.client.menu.NavigationTreePresenter;
import ru.naumen.core.client.menu.subtree.SubNavigationTreeDisplay;
import ru.naumen.core.client.menu.subtree.SubNavigationTreeGinModule;
import ru.naumen.core.client.menu.subtree.SubNavigationTreePresenter;

import jakarta.inject.Inject;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DynaformNavigationTreeGinModule extends AbstractGinModule
{
    static class DynaformNavTreeSubTreeProvider implements Provider<List<SubNavigationTreePresenter<?>>>
    {
        @Inject
        SubNavigationTreePresenter<MainTree> mainTree;

        @Override
        public List<SubNavigationTreePresenter<?>> get()
        {
            return Lists.<SubNavigationTreePresenter<?>>newArrayList(mainTree);
        }
    }

    @Override
    protected void configure()
    {
        //@formatter:off
        bind(new TypeLiteral<List<SubNavigationTreePresenter<?>>>()
        {
        })
                .annotatedWith(Names.named(NavigationTreePresenter.SUB_NAVIGATION_TREES))
                .toProvider(DynaformNavTreeSubTreeProvider.class)
                .in(Singleton.class);

        bind(SubNavigationTreeDisplay.class).to(LeftMenuTreeDisplayImpl.class);

        install(SubNavigationTreeGinModule.create(MainTree.class)
                .setPresenter(LeftMenuNavigationTreePresenter.class));
        //@formatter:on
    }
}


