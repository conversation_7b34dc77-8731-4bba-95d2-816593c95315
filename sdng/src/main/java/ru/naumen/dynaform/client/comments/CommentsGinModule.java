package ru.naumen.dynaform.client.comments;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;

/**
 * 
 * <AUTHOR>
 * @since 3 авг. 2016 г.
 */
public class CommentsGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
                .implement(AddCommentFormPartPresenter.class, AddCommentFormPartPresenterImpl.class)
                .build(AddCommentFormPartPresenterFactory.class));
        //@formatter:on
    }
}
