package ru.naumen.dynaform.client.header;

import jakarta.inject.Provider;

import ru.naumen.core.client.content.toolbar.display.ToolPanelDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.utils.ReadyState;

/**
 * Интерфейс контроллера кнопки-иконки в шапке карточки или формы
 *
 * <AUTHOR>
 * @since 29.09.22
 */
public interface HeaderTabIconController extends Presenter, HasCode
{
    /**
     * Обновить тулбар добавлением или удалением "своей" кнопки-иконки
     * @param toolBar дисплей тулбара
     */
    void updateToolBar(ToolPanelDisplay toolBar);
    /**
     * @param pageNameProvider провайдер названия страницы
     * (в некоторых случаях может быть с учетом выбранной вкладки)
     * @param readyState объект готовности контроллера
     */
    void init(Provider<String> pageNameProvider, ReadyState readyState);

    /**
     * Сделать иконку неактивной
     */
    void disable();
}
