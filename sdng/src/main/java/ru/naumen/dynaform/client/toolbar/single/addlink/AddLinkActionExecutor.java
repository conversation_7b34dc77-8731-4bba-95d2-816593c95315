package ru.naumen.dynaform.client.toolbar.single.addlink;

import static ru.naumen.core.shared.utils.CommonUtils.PARENT_CONTEXT;
import static ru.naumen.core.shared.utils.CommonUtils.assertNotNull;
import static ru.naumen.core.shared.utils.CommonUtils.assertObjectNotNull;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.common.collect.Collections2;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextUtils;
import ru.naumen.core.client.utils.FormUtils;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.actioncommand.FormContext;
import ru.naumen.dynaform.client.toolbar.single.ActionSingleExecutor;
import ru.naumen.dynaform.client.toolbar.single.addlink.AddLinkToolBarGinModule.AddLinkFormFactory;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.client.MetainfoSyncUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.objectlist.client.ObjectListContext;

/**
 * <AUTHOR>
 * @since 12.01.2012
 */
@Singleton
public class AddLinkActionExecutor extends ActionSingleExecutor
{
    @Inject
    private FormUtils formUtils;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private AddLinkFormFactory formFactory;
    @Inject
    private MetainfoSyncUtils metainfoSyncUtils;
    @Inject
    private MetainfoServiceSync metainfoServiceSync;

    @Override
    protected void execute(Content content, DynaContext context, DtObject object)
    {
        RelObjectList objectList = (RelObjectList)content;
        AddLinkActionContext actionContext = createActionContext(objectList, context);
        bindFormPresenter(objectList, actionContext);
    }

    private void bindFormPresenter(RelObjectList objectList, AddLinkActionContext actionContext)
    {
        AddLinkFormPresenter formPresenter = formFactory.create(actionContext);
        formPresenter.bind();
        formUtils.wrapFormContent(formPresenter.getDisplay());
        if (!objectList.getFormCaption().isEmpty())
        {
            formPresenter.setCaption(metainfoUtils.getLocalizedValue(objectList.getFormCaption()));
        }
        formPresenter.getDisplay().display();
    }

    private AddLinkActionContext createActionContext(RelObjectList content, DynaContext context)
    {
        String attributeCode = content.getAttributesChain().get(0).getAttrCode();
        Attribute attribute = context.getMetainfo().getAttribute(attributeCode);
        FormContext formContext = null;
        if (context instanceof ObjectListContext)
        {
            Context parentContext = ContextUtils.getDecoratedContext(((ObjectListContext)context).getParentContext());
            if (parentContext instanceof FormContext)
            {
                formContext = (FormContext)parentContext;
            }
        }
        AddLinkActionContext actionContext = new AddLinkActionContext(context, content, attribute, formContext);
        actionContext.setPossibleCases(metainfoSyncUtils.casesIntersection(getContentPermittedFqns(content), attribute
                .getType().getPermittedTypes()));

        DynaContext parentContext = actionContext.getParentContext();
        assertNotNull(parentContext, PARENT_CONTEXT);
        DtObject object = parentContext.getObject();
        assertObjectNotNull(object);
        actionContext.setRelatedMetaClass(metainfoServiceSync.getMetaClassLite(actionContext.getAttribute().getType()
                .<ObjectAttributeType> cast().getRelatedMetaClass()));
        actionContext.setObject(object);
        return actionContext;
    }

    private Collection<ClassFqn> getContentPermittedFqns(RelObjectList content)
    {
        if (content.getClazz() == null)
        {
            return content.getCase();
        }
        return Collections2.transform(
                metainfoServiceSync.getDescendantClasses(content.getClazz(), content.getClazz().isCase()),
                MetaClassLite.FQN_EXTRACTOR);
    }

}
