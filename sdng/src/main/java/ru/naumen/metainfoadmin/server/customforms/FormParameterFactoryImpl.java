package ru.naumen.metainfoadmin.server.customforms;

import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;
import static ru.naumen.core.shared.Constants.Employee.TEAMS;
import static ru.naumen.core.shared.Constants.PARENT_ATTR;
import static ru.naumen.metainfo.shared.Constants.AggregateAttributeType.*;

import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.bo.permittedtyperelation.PermittedTypeRelationUtils;
import ru.naumen.core.server.customforms.FormParameter;
import ru.naumen.core.server.customforms.ParameterPresentation;
import ru.naumen.core.server.customforms.ParameterType;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.metainfo.server.spi.dispatch.HandlerUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.AttributeDescription;
import ru.naumen.metainfo.shared.elements.ComplexRelationType;
import ru.naumen.metainfo.shared.elements.HasDateTimeRestriction.RestrictionType;
import ru.naumen.metainfoadmin.shared.customforms.SaveFormParameterAction;

/**
 * 
 * <AUTHOR>
 * @since 20 апр. 2016 г.
 */
@Component
public class FormParameterFactoryImpl implements FormParameterInfoFactory
{
    private final ResolverUtils resolverUtils;
    private final HandlerUtils handlerUtils;
    private final ILocaleInfo localeInfo;
    private final PermittedTypeRelationUtils permittedTypeRelationUtils;

    @Inject
    public FormParameterFactoryImpl(ResolverUtils resolverUtils, HandlerUtils handlerUtils, ILocaleInfo localeInfo,
            PermittedTypeRelationUtils permittedTypeRelationUtils)
    {
        this.resolverUtils = resolverUtils;
        this.handlerUtils = handlerUtils;
        this.localeInfo = localeInfo;
        this.permittedTypeRelationUtils = permittedTypeRelationUtils;
    }

    @Override
    public FormParameterInfo create(SaveFormParameterAction action)
    {
        //@formatter:off
        FormParameter parameter = new FormParameter()
            .setCode(action.getCode())
            .setTitle(getTitle(action))
            .setDescription(action.getDescription());
        //@formatter:on
        parameter.setSettingsSet(action.getSettingsSet());

        parameter.setType(createType(action, parameter));

        parameter.setEditable(action.isEditable());
        parameter.setRequired(action.isRequired());
        parameter.setHideAttrCaption(action.isHiddenAttrCaption());

        parameter.setViewPresentation(getPresentation(action.getViewPresentation()));
        parameter.setEditPresenation(getPresentation(action.getEditPresentation()));

        parameter.setHiddenWhenNoPossibleValues(action.isHiddenWhenNoPossibleValues());
        parameter.setEditOnComplexFormOnly(action.isEditOnComplexFormOnly());

        parameter.setDefaultValue(getDefaultValue(action, parameter));

        parameter.setComplexRelation(action.getComplexRelation());
        parameter.setComplexRelationType(action.getComplexRelationType());
        if (ComplexRelationType.HIERARCHY.getCode().equals(action.getComplexRelationType()))
        {
            parameter.setComplexStructuredObjectsViewCode(action.getComplexStructuredObjectsViewCode());
        }
        else
        {
            parameter.setComplexAttrGroup(action.getComplexAttrGroupCode());
        }
        parameter.setComplexRelationAggrAttrGroups(action.getComplexRelationAggrAttrGroups());
        FormParameterInfo parameterInfo = new FormParameterInfo(parameter);

        if (Boolean.TRUE.equals(action.isFilteredByScript()))
        {
            parameterInfo.setFiltrationScript(action.getScriptForFiltration());
        }

        if (Boolean.TRUE.equals(action.isComputableOnForm()))
        {
            parameterInfo.setComputableOnFormScript(action.getComputableOnFormScript());
        }

        if (Boolean.TRUE.equals(action.isDefaultByScript()))
        {
            parameterInfo.setDefaultValueScript(action.getScriptForDefault());
        }

        parameter.setDateTimeRestrictionAttribute(action.getDateTimeRestrictionAttribute());
        parameter.setDateTimeRestrictionCondition(action.getDateTimeRestrictionCondition());
        parameter.setDateTimeRestrictionType(StringUtilities.isNotEmpty(action.getDateTimeRestrictionType())
                ? RestrictionType.valueOf(action.getDateTimeRestrictionType())
                : null);
        parameter.setDateTimeCommonRestrictions(action.getDateTimeCommonRestrictions());
        boolean isRestrictedByScript = RestrictionType.RESTRICTION_BY_SCRIPT.name()
                .equals(action.getDateTimeRestrictionType());
        parameterInfo.setDateTimeRestrictionScript(isRestrictedByScript ? action.getDateTimeRestrictionScript() : null);

        parameterInfo.setComputeAnyCatalogElementsScript(action.getComputeAnyCatalogElementsScript());
        return parameterInfo;
    }

    private ParameterType createType(SaveFormParameterAction action, FormParameter parameter)
    {
        ParameterType type = new ParameterType(action.getTypeCode(), action.getTypeProperties());
        type.setAttribute(parameter);

        if (AggregateAttributeType.CODE.equals(action.getTypeCode())
                || Constants.LINK_ATTRIBUTE_TYPES.contains(action.getTypeCode()))
        {
            type.setComplexRelation(action.getComplexRelation());
            if (Constants.LINK_ATTRIBUTE_TYPES.contains(action.getTypeCode()))
            {
                if (action.getComplexRelation() != null)
                {
                    type.setComplexRelationType(action.getComplexRelationType());
                    if (ComplexRelationType.HIERARCHY.getCode().equals(action.getComplexRelationType()))
                    {
                        type.setComplexStructuredObjectsViewCode(action.getComplexStructuredObjectsViewCode());
                    }
                    else
                    {
                        type.setComplexAttrGroupCode(action.getComplexAttrGroupCode());
                    }
                    type.setQuickAddFormCode(action.getQuickAddForm());
                    type.setQuickEditFormCode(action.getQuickEditForm());
                }
                type.setPermittedTypes(permittedTypeRelationUtils.getPermittedTypes(type));
            }
            if (AggregateAttributeType.CODE.equals(action.getTypeCode()))
            {
                type.setComplexRelationAttrGroups(action.getComplexRelationAggrAttrGroups());
            }
        }

        if (AggregateAttributeType.CODE.equals(action.getTypeCode()))
        {
            Collection<ClassFqn> fqns = action.getTypeProperties().getProperty(AGGREGATE_CLASSES);

            List<AttributeDescription> attrs = fqns == null ? emptyList() : fqns.stream().map(fqn ->
            {
                String code = action.getCode();
                if (Employee.FQN.equals(fqn))
                {
                    List<String> parents = Lists.newArrayList();
                    if (fqns.contains(OU.FQN))
                    {
                        parents.add(PARENT_ATTR);
                    }
                    if (fqns.contains(Team.FQN))
                    {
                        parents.add(TEAMS);
                    }
                    return new AttributeDescription(code + EMPLOYEE_POSTFIX, parents, fqn);
                }
                else if (OU.FQN.equals(fqn))
                {
                    return new AttributeDescription(code + OU_POSTFIX, emptyList(), fqn);
                }
                else if (Team.FQN.equals(fqn))
                {
                    return new AttributeDescription(code + TEAM_POSTFIX, emptyList(), fqn);
                }
                throw new IllegalArgumentException("Unknown fqn");
            }).collect(toList());

            type.setProperty(PREPARED_ATTRIBUTES, attrs);
        }

        return type;
    }

    private Object getDefaultValue(SaveFormParameterAction action, FormParameter parameter)
    {
        if (!action.hasDefaultValue())
        {
            return null;
        }
        return handlerUtils
                .transferDefaultValue(resolverUtils.resolv(new ResolverContext(parameter, action.getDefaultValue())));
    }

    private ParameterPresentation getPresentation(IProperties prsProperties)
    {
        ParameterPresentation presentation = new ParameterPresentation(
                prsProperties.getProperty(Presentations.ATTR_CODE), prsProperties);
        return presentation;
    }

    private IProperties getTitle(SaveFormParameterAction action)
    {
        IProperties title = new MapProperties();
        title.setProperty(localeInfo.getCurrentLang(), action.getTitle());
        return title;
    }
}
