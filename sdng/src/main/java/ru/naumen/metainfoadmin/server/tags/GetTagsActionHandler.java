package ru.naumen.metainfoadmin.server.tags;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.tags.TagDtoFactory;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.tags.dispatch.GetTagsAction;
import ru.naumen.metainfo.shared.tags.dispatch.GetTagsResult;

/**
 * Обработчик действия получения списка доступных в системе меток.
 * <AUTHOR>
 * @since Sep 22, 2017
 */
@Component
public class GetTagsActionHandler extends TransactionalReadActionHandler<GetTagsAction, GetTagsResult>
{
    @Inject
    private TagService tagService;
    @Inject
    private TagDtoFactory dtoFactory;

    @Override
    public GetTagsResult executeInTransaction(GetTagsAction action, ExecutionContext context) throws DispatchException
    {
        List<DtObject> dtos = new ArrayList<>();
        if (null != action.getTagCodes())
        {
            action.getTagCodes().stream().map(tagService::getTag).filter(Objects::nonNull).map(dtoFactory::createLite)
                    .forEach(dtos::add);
        }
        else
        {
            dtos.addAll(dtoFactory.createLite(tagService.getAllTags()));
        }
        dtos.sort(ITitled.IGNORE_CASE_COMPARATOR);
        return new GetTagsResult(dtos);
    }
}
