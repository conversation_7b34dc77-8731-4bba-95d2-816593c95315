package ru.naumen.metainfoadmin.client.eventaction.template;

import jakarta.inject.Inject;

import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;

/**
 * Реализация панели предварительного просмотра оповещения.
 * <AUTHOR>
 * @since Jan 24, 2017
 */
public class PreviewPanelDisplayImpl implements PreviewPanelDisplay
{
    private FlowPanel container;
    private FlowPanel inner;
    private Label titleLabel;
    private FlowPanel toolPanel;
    private Anchor sourceModeTool;
    private RichTextPreviewWigdet textView;
    private Widget closeIcon;

    private HandlerRegistration sourceClickHandlerRegistration = null;

    @Inject
    private EventActionMessages messages;
    @Inject
    private FontIconFactory<?> iconFactory;

    @Override
    public Widget asWidget()
    {
        return container;
    }

    @Override
    public void destroy()
    {
        removeHandlers();
        container.removeFromParent();
    }

    @Override
    public Widget getCloseIcon()
    {
        return closeIcon;
    }

    @Override
    public RichTextPreviewWigdet getTextView()
    {
        return textView;
    }

    @Override
    public void startProcessing()
    {
        textView.setEnabled(false);
    }

    @Override
    public void stopProcessing()
    {
        textView.setEnabled(true);
    }

    protected FlowPanel getInner()
    {
        return inner;
    }

    protected Label getTitleLabel()
    {
        return titleLabel;
    }

    protected FlowPanel getToolPanel()
    {
        return toolPanel;
    }

    @Inject
    protected void init(RichTextPreviewWigdet textView, WidgetResources resources)
    {
        resources.form().ensureInjected();
        resources.all().ensureInjected();

        container = new FlowPanel();
        container.addStyleName(resources.form().templatePreviewContainer());
        container.asWidget().getElement().getStyle().setProperty("right", 120, Unit.PCT);

        textView.addStyleName(resources.form().templatePreviewRtfView());
        this.textView = textView;
        titleLabel = new Label(messages.templatePreview());
        titleLabel.addStyleName(resources.form().templatePreviewTitle());
        toolPanel = new FlowPanel();
        toolPanel.addStyleName(resources.form().templatePreviewToolPanel());
        sourceModeTool = new Anchor();
        sourceModeTool.addStyleName(resources.buttons().defaultLink());
        updateSourceToolText();
        toolPanel.add(sourceModeTool);

        closeIcon = iconFactory.create(IconCodes.CLOSE2).asWidget();
        closeIcon.setTitle(messages.closePreview());

        inner = new FlowPanel();
        inner.addStyleName(resources.form().templatePreviewInner());
        inner.add(titleLabel);
        inner.add(toolPanel);
        inner.add(textView);
        inner.add(closeIcon);

        container.add(inner);
        textView.setVisible(true);

        addClickHandler();
        ensureDebugIds();
    }

    private void addClickHandler()
    {
        sourceClickHandlerRegistration = sourceModeTool.addClickHandler(event ->
        {
            textView.setSourceMode(!textView.isSourceMode());
            updateSourceToolText();
        });
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(textView, "previewText");
        DebugIdBuilder.ensureDebugId(sourceModeTool, "sourceMode");
        DebugIdBuilder.ensureDebugId(closeIcon, "previewCloseIcon");
    }

    private void removeHandlers()
    {
        if (null != sourceClickHandlerRegistration)
        {
            sourceClickHandlerRegistration.removeHandler();
        }
    }

    private void updateSourceToolText()
    {
        sourceModeTool.setText(textView.isSourceMode() ? messages.showPreviewRender() : messages.showPreviewSource());
    }
}
