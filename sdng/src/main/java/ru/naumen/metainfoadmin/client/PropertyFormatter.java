package ru.naumen.metainfoadmin.client;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonHtmlTemplates;

/**
 * Компонент для форматирования значения свойства
 *
 * <AUTHOR>
 * @since 11.06.2024
 */
@Singleton
public class PropertyFormatter
{
    @Inject
    private CommonHtmlTemplates templates;

    public SafeHtml formatDtObjectsToAnchors(Map<String, String> dtObjects, String placePrefix)
    {
        List<String> items = dtObjects.entrySet().stream()
                .map(dto -> templates
                        .historyAnchor(dto.getValue(), placePrefix + ':' + dto.getKey(), dto.getKey(),
                                SafeHtmlUtils.EMPTY_SAFE_HTML)
                        .asString())
                .collect(Collectors.toList()); //NOSONAR
        return SafeHtmlUtils.fromSafeConstant(StringUtilities.join(items));
    }
}
