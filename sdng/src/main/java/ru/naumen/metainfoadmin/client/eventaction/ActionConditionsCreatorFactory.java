package ru.naumen.metainfoadmin.client.eventaction;

import jakarta.annotation.Nullable;

import ru.naumen.metainfo.shared.eventaction.ActionConditionType;
import ru.naumen.metainfo.shared.eventaction.ActionConditionWithScript;

/**
 * <AUTHOR>
 *
 */
public interface ActionConditionsCreatorFactory
{
    ActionConditionCreator create(ActionConditionType type, @Nullable ActionConditionWithScript condition);
}
