package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications.columns;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.columns.ClickableTextColumn;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;

/**
 * <AUTHOR>
 * @since 07.07.16
 */
public class EmbeddedApplicationTypeColumn extends ClickableTextColumn<DtObject>
{
    @Inject
    protected EmbeddedApplicationMessages messages;

    @Override
    public String getValue(DtObject appDto)
    {
        EmbeddedApplication application = appDto
                .getProperty(ru.naumen.metainfo.shared.embeddedapplication.Constants.Application.ORIGINAL_APPLICATION);

        if (application.getApplicationType() == EmbeddedApplicationType.ExternalApplication)
        {
            return messages.applicationHostedOnExternalServer();
        }
        else if (application.getApplicationType() == EmbeddedApplicationType.InternalApplication)
        {
            return messages.applicationHostedOnInternalServer();
        }
        else if (application.getApplicationType() == EmbeddedApplicationType.ClientSideApplication)
        {
            return messages.applicationNoServer();
        }
        else if (application.getApplicationType() == EmbeddedApplicationType.CustomLoginFormApplication)
        {
            return messages.applicationCustomLoginForm();
        }

        return application.getApplicationType().toString();
    }

    @Inject
    protected void initColumn(EmbeddedApplicationFieldUpdater<String> fieldUpdater, WidgetResources resources)
    {
        setFieldUpdater(fieldUpdater);
        setCellStyleNames(new StringBuilder(resources.additional().tableCell()).append(' ')
                .append(resources.additional().cursorPointer()).toString());

    }
}
