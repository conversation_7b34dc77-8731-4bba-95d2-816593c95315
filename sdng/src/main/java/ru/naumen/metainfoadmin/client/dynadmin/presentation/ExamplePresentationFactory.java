package ru.naumen.metainfoadmin.client.dynadmin.presentation;

import ru.naumen.core.client.attr.presentation.PresentationContext;

import com.google.gwt.user.client.ui.IsWidget;
import com.google.inject.ImplementedBy;

/**
 * <AUTHOR>
 * @since Jun 23, 2015
 */
@ImplementedBy(ExamplePresentationFactoryImpl.class)
public interface ExamplePresentationFactory
{
    <T, W extends IsWidget> W createExampleWidget(PresentationContext context);
}
