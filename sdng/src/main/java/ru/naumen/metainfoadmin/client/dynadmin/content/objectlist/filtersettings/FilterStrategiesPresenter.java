package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filtersettings;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.FlowPanel;

import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.components.block.TitledBlockDisplay;
import ru.naumen.core.client.components.block.TitledBlockPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.metainfo.shared.ui.HasFilterRestrictionStrategy;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.ListSettingsButtonCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.template.CopyFromTemplateFormPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.template.CopyFromTemplateFormPresenterFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.template.CopyFromTemplateMessages;
import ru.naumen.metainfoadmin.shared.Constants.FilterSettingsCommandCode;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionEvent;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionHandler;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Представление области настройки стратегий ограничения возможных значений фильтрации.
 * <AUTHOR>
 * @since Aug 31, 2018
 */
public class FilterStrategiesPresenter extends TitledBlockPresenter implements ObjectListActionHandler
{
    @Inject
    private FilterSettingsAdvlistFactory advlistFactory;
    @Inject
    private FilterSettingsMessages filterSettingsMessages;
    @Inject
    private CommandFactory commandFactory;
    @Inject
    private CopyFromTemplateFormPresenterFactory copyFromTemplateFormPresenterFactory;
    @Inject
    private CopyFromTemplateMessages copyFromTemplateMessages;
    @Inject
    private ListSettingsButtonCreator buttonCreator;
    private HasFilterRestrictionStrategy contentOwner;
    private UIContext parentContext;
    private ListPresenter<CustomList> listPresenter;

    @Inject
    public FilterStrategiesPresenter(TitledBlockDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(HasFilterRestrictionStrategy contentOwner, UIContext parentContext)
    {
        this.contentOwner = contentOwner;
        this.parentContext = parentContext;
    }

    @Override
    public void init(ListComponents components)
    {
    }

    @Override
    public void onObjectListAction(ObjectListActionEvent event)
    {
        if (event.getAction().equals(FilterSettingsCommandCode.EDIT_FILTER_SETTINGS))
        {
            BasicCallback<Void> refreshCallback = new OnStartBasicCallback<Void>()
            {
                @Override
                protected void handleSuccess(Void value)
                {
                    listPresenter.refreshDisplay();
                    listPresenter.getListComponents().getSelectionModel().clear();
                }
            };
            String commandCode = event.getAction();
            FilterSettingsCommandParam commandParam = new FilterSettingsCommandParam(event.getValue().getUUID(),
                    event.getValues(), contentOwner, parentContext, refreshCallback);
            BaseCommand<?, ?> command = commandFactory.create(commandCode, commandParam);
            command.execute((CommandParam)commandParam);
        }
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaption(filterSettingsMessages.filterRestrictions());

        ButtonPresenter<?> copyActionsFromTemplate = buttonCreator.createButton(ButtonCode.COPY_FROM_TEMPLATE,
                copyFromTemplateMessages.iconHint());
        copyActionsFromTemplate.getDisplay().asWidget()
                .addStyleName(WidgetResources.INSTANCE.buttons().buttonBothSide());
        copyActionsFromTemplate.getDisplay().asWidget().removeStyleName(WidgetResources.INSTANCE.all().pullRight());

        copyActionsFromTemplate.addClickHandler(event ->
        {
            CopyFromTemplateFormPresenter presenter = copyFromTemplateFormPresenterFactory
                    .create((ObjectListBase)contentOwner, parentContext, null);
            presenter.setFormCaller(this);
            presenter.bind();
        });
        FlowPanel container = new FlowPanel();
        container.add(copyActionsFromTemplate.getDisplay());
        listPresenter = advlistFactory.create(parentContext, contentOwner);
        registerChildPresenter(listPresenter, true);
        container.add(listPresenter.getDisplay());
        getDisplay().setControlledWidget(container);
        listPresenter.refreshDisplay();

        registerHandler(eventBus.addHandler(ObjectListActionEvent.getType(), this));
    }
}
