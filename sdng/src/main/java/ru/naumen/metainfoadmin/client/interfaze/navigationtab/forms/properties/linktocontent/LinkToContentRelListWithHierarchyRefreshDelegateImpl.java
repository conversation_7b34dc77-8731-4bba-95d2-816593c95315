package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления свойства "Объекты иерархии связаны с объектами списка через атрибут"
 *
 * <AUTHOR>
 * @since 03.11.2020
 */
public class LinkToContentRelListWithHierarchyRefreshDelegateImpl implements PropertyDelegateRefresh<RelationsAttrTreeObject,
        PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<?, RelationsAttrTreeObject, ?>>>
{
    @Override
    public void refreshProperty(PropertyContainerContext context,
            PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<?, RelationsAttrTreeObject, ?>> property,
            AsyncCallback<Boolean> callback)
    {
        boolean isRelObjectListBlockVisible =
                LinkToContentMetaClassPropertiesProcessor.getRelObjectListFragmentVisibility(context);

        final Boolean showHierarchy = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.SHOW_HIERARCHY);

        callback.onSuccess(isRelObjectListBlockVisible && showHierarchy != null && showHierarchy);
    }
}