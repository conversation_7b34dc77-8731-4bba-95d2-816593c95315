package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject;

import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Делегат обновления свойства "Класс объектов".
 * <AUTHOR>
 * @since 08.08.18
 */
public class RelatedObjectMetaclassRefreshDelegateEdit<F extends ObjectForm>
        implements
        AttributeFormPropertyDelegateRefresh<F, String, TextBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context,
            TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        callback.onSuccess(false);
    }
}
