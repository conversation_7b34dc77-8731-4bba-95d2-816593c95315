package ru.naumen.metainfoadmin.client.toolbar;

import jakarta.inject.Inject;

import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.content.toolbar.actions.ActionHandler;

import com.google.inject.assistedinject.Assisted;

/**
 * Отображаем смену ответственного только на карточках объектов с поддержкой ответственного
 * 
 * <AUTHOR>
 *
 */
public class EditResponsibleActionHandler implements ActionHandler
{
    private final ActionToolContext context;

    @Inject
    public EditResponsibleActionHandler(@Assisted ActionToolContext context)
    {
        this.context = context;
    }

    @Override
    public void execute()
    {
    }

    @Override
    public boolean isEnabled()
    {
        return context.getActionTool().isVisible();
    }
}
