package ru.naumen.objectlist.client.toolbar;

import java.util.HashSet;
import java.util.TreeSet;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.content.toolbar.ActionHandlerRegistry;
import ru.naumen.core.client.content.toolbar.ChangeStateToolPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.shared.Constants.HasState;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.mode.active.ObjectListActive;
import ru.naumen.objectlist.shared.advlist.AdvlistToolPanelContext;
import ru.naumen.objectlist.shared.advlist.AdvlistToolPanelContext.WfTransition;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.Widget;

/**
 * Презентер кнопки смены статуса в панели массовых действий адвлиста
 * <AUTHOR>
 */
public class AdvlistChangeStateToolPresenter<T extends ButtonToolDisplay, R extends ObjectListContext>
        extends ChangeStateToolPresenter<T, R>
{
    @Inject
    public AdvlistChangeStateToolPresenter(T display, EventBus eventBus, ActionHandlerRegistry registry)
    {
        super(display, eventBus, registry);
    }

    @Override
    public void refreshDisplay()
    {
        String title = getPossibleStateTitles();

        super.refreshDisplay();

        getDisplay().setText(title);
        if (title.isEmpty())
        {
            getDisplay().setVisible(false);
        }
        //Поскольку  тулпанель скрыта, если ее элементы невидимы, то 
        //если хоть один элемент стал видим, то и всю панель тоже нужно показать.
        Widget toolPanel = ((Widget)getDisplay()).getParent();
        if (getDisplay().isVisible() && null != toolPanel)
        {
            toolPanel.setVisible(true);
        }
    }

    private String getPossibleStateTitles()
    {
        if (!(context.getMode() instanceof ObjectListActive))
        {
            return StringUtilities.EMPTY;
        }

        ObjectListActive mode = (ObjectListActive)context.getMode();
        HashSet<Pair<ClassFqn, String>> objStates = new HashSet<>();
        for (DtObject dto : mode.getSelectedObjects())
        {
            objStates.add(Pair.create(dto.getMetainfo(), dto.<String> getProperty(HasState.STATE)));
        }

        AdvlistToolPanelContext toolPanelContext = mode.getMassOperationsToolPanelContext();

        String targetState = getContent().getTargetStateCode();

        TreeSet<String> titles = new TreeSet<>(String.CASE_INSENSITIVE_ORDER);
        for (WfTransition transition : toolPanelContext.getTargetStateTransitions(targetState))
        {
            Pair<ClassFqn, String> transitionKey = Pair.create(transition.fqn, transition.fromState);

            if (objStates.contains(transitionKey))
            {
                titles.add(transition.title);
            }
        }
        return StringUtilities.join(titles, "/");
    }
}
