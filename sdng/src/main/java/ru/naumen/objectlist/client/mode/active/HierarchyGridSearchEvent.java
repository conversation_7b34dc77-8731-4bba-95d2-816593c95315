package ru.naumen.objectlist.client.mode.active;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие, информирующее о начале поиска в иерархическом списке
 * <AUTHOR>
 * @since 22.01.2020
 */
public class HierarchyGridSearchEvent extends GwtEvent<HierarchyGridSearchEventHandler>
{
    private static final Type<HierarchyGridSearchEventHandler> TYPE = new Type<>();

    public static Type<HierarchyGridSearchEventHandler> getType()
    {
        return TYPE;
    }

    private final String searchString;

    public HierarchyGridSearchEvent(String searchString)
    {
        this.searchString = searchString;
    }

    @Override
    public Type<HierarchyGridSearchEventHandler> getAssociatedType()
    {
        return TYPE;
    }

    public String getSearchString()
    {
        return searchString;
    }

    @Override
    protected void dispatch(HierarchyGridSearchEventHandler handler)
    {
        handler.onSearch(this);
    }
}
