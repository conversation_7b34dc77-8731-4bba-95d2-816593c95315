package ru.naumen.objectlist.client.mode.active.extended.advlist.features.massoperation;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.collect.Sets;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.view.client.SetSelectionModel;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.content.AbstractContentPresenter;
import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.events.EditObjectEvent;
import ru.naumen.core.client.events.ModifyMassCallEvent;
import ru.naumen.core.client.events.ModifyMassCallHandler;
import ru.naumen.core.client.events.SetRemovedEvent;
import ru.naumen.core.client.events.SetRemovedHandler;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.HasReadyState.SynchronizationCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.ListComponentsHolder;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.mode.active.ObjectListActive;
import ru.naumen.objectlist.client.mode.active.data.AdvListReloadedEvent;
import ru.naumen.objectlist.client.toolbar.ObjectListToolFactoryImpl;
import ru.naumen.objectlist.shared.advlist.GetAdvlistToolsPermissionsAction;
import ru.naumen.objectlist.shared.advlist.GetAdvlistToolsPermissionsResponse;

/**
 * Презентер массовых операций в адвлисте - получает права для выбранных объектов в адвлисте, кеширует их в контексте и
 * заполняет тулбар в дисплее согласно соответствию прав
 *
 * Биндинг панели массовых действий отложенный, так как просмотров списков на порядок больше, чем кликов по чекбоксам.
 *
 * <AUTHOR>
 * @since 16.12.2011
 */
public class AdvListMassOperationPresenterImpl
        extends AbstractContentPresenter<AdvListMassOperationDisplay, ObjectListBase, ObjectListContext>
        implements AdvListMassOperationPresenter, SetRemovedHandler, ModifyMassCallHandler
{
    private class GetPermissionsCallback extends BasicCallback<GetAdvlistToolsPermissionsResponse>
    {
        public GetPermissionsCallback()
        {
            super(permissionSynchronzation);
        }

        @Override
        protected void handleSuccess(GetAdvlistToolsPermissionsResponse response)
        {
            getMode().getObjectPermissions().putAll(response.getObjectPermissions());

            if (getMode().getMassOperationsToolPanelContext() == null && response.getToolPanelContext() != null)
            {
                getMode().setMassOperationsToolPanelContext(response.getToolPanelContext());
                bindToolPanel();
            }
            else
            {
                refreshDisplay();
            }
        }
    }

    @Inject
    private AdvListMassOperationMessages messages;
    @Inject
    private ObjectListToolFactoryImpl contentFactory;
    @Inject
    private DispatchAsync service;

    private SetSelectionModel<DtObject> selectionModel;

    private ListComponents components;
    @Inject
    private ListComponentsHolder listComponentsHolder;

    private ContentPresenter<ToolPanel, ObjectListContext> toolPanelPresenter;

    private final ReadyState permissionSynchronzation = new ReadyState(this);

    @Inject
    public AdvListMassOperationPresenterImpl(AdvListMassOperationDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "AdvListMassOperation");
    }

    @Override
    public void init(ObjectListBase content, ObjectListContext context)
    {
        super.init(content, context);

        this.components = listComponentsHolder.get(content);
        this.selectionModel = components.getSelectionModel();
    }

    @Override
    public void initMetainfo()
    {
        int selectedNumber = selectionModel.getSelectedSet().size();
        getDisplay().setLabel(messages.itemsSelected(selectedNumber));
    }

    @Override
    public void onAdvListReload(AdvListReloadedEvent event)
    {
    }

    @Override
    public void onMassCallChanged(ModifyMassCallEvent event)
    {
        //очищаем кэш
        getMode().getObjectPermissions().clear();
    }

    @Override
    public void onMassOperationRefresh(AdvListMassOperationRefreshEvent event)
    {
        GetAdvlistToolsPermissionsAction action = new GetAdvlistToolsPermissionsAction();
        components.getDataProvider().getUnsavedObjects().stream().filter(dto -> UuidHelper.isTempUuid(dto.getUUID()))
                .forEach(dto -> action.getCreatedObjects().put(dto.getUUID(), dto));
        action.setToolPanelContext(getMode().getMassOperationsToolPanelContext());
        action.setFormCode(context.getFormCode());
        DtObject formObject = context.getFormObject();
        action.setFormObjectUuid(null == formObject ? null : formObject.getUUID());
        if (getMode().getMassOperationsToolPanelContext() == null)
        {
            //сформируем панель массовых действий на сервере, так как идет первое обращение и панель еще не готова
            ObjectListBase objectList = (ObjectListBase)getContent().clone();
            objectList.setParent(null);
            action.setObjectList(objectList);
        }

        Map<String, PermissionHolder> permissions = getMode().getObjectPermissions();
        Set<String> alreadyChecked = permissions.keySet();
        Set<DtObject> selectedObjects = selectionModel.getSelectedSet();

        HashSet<String> needCheck = Sets.newHashSet(CommonUtils.extractUuids(selectedObjects));
        needCheck.removeAll(alreadyChecked);

        //если выбран один и для него не все тулы были проверены, то проверим
        if (needCheck.isEmpty() && selectedObjects.size() == 1
                && !isFullToolsChecked(selectedObjects.iterator().next().getUUID()))
        {
            needCheck.add(selectedObjects.iterator().next().getUUID());
        }

        if (!needCheck.isEmpty() && getMode().isCheckObjectPermissions())
        {
            action.setObjectUuids(needCheck);
        }

        if (getMode().getMassOperationsToolPanelContext() != null && action.getObjectUuids().isEmpty())
        {
            refreshDisplay();
            return;
        }

        service.execute(action, new GetPermissionsCallback());
    }

    @Override
    public void onObjectEdited(EditObjectEvent event)
    {
        Map<String, PermissionHolder> permissions = getMode().getObjectPermissions();
        for (DtObject dto : event.getDtObjects())
        {
            permissions.remove(dto.getUUID());
        }
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        initMetainfo();
        resetContainer();
    }

    @Override
    public void setRemoved(SetRemovedEvent event)
    {
        //при переключении режимов очищаем кэш
        getMode().getObjectPermissions().clear();
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(EditObjectEvent.getType(), this));
        registerHandler(getContext().getEventBus().addHandler(AdvListReloadedEvent.getType(), this));
        registerHandler(getContext().getEventBus().addHandler(AdvListMassOperationRefreshEvent.getType(), this));
        registerHandler(getContext().getEventBus().addHandler(SetRemovedEvent.getType(), this));
        registerHandler(getContext().getEventBus().addHandler(ModifyMassCallEvent.getType(), this));

        permissionSynchronzation.registerSynchronization(new SynchronizationCallback(this)
        {
            @Override
            public void error()
            {
                ready();
            }

            @Override
            public void notReady()
            {
                if (toolPanelPresenter != null)
                {
                    toolPanelPresenter.getDisplay().startProcessing();
                }
            }

            @Override
            public void ready()
            {
                if (toolPanelPresenter != null)
                {
                    toolPanelPresenter.getDisplay().stopProcessing();
                }
                refreshDisplay();
            }
        });
    }

    /**
     * Выполняет отложенную инициализацию панели при первом выборе объектов в списке
     */
    private void bindToolPanel()
    {
        getDisplay().clearTools();
        ObjectListContext listToolBarContext = components.getListToolBarContext();
        contentFactory.build(getMode().getMassOperationsToolPanel(), listToolBarContext,
                new BasicCallback<ContentPresenter<ToolPanel, ObjectListContext>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(ContentPresenter<ToolPanel, ObjectListContext> value)
                    {
                        toolPanelPresenter = value;
                        registerChildPresenter(toolPanelPresenter);
                        IsWidget tool = toolPanelPresenter.getDisplay();
                        tool.asWidget().addStyleName(WidgetResources.INSTANCE.buttons().actLinkLite());
                        getDisplay().addTool(tool);
                        refreshDisplay();
                    }
                });
    }

    private ObjectListActive getMode()
    {
        return (ObjectListActive)getContext().getMode();
    }

    private boolean isFullToolsChecked(String uuid)
    {
        PermissionHolder permissions = getMode().getObjectPermissions().get(uuid);
        return permissions.hasPermission(uuid, Constants.FULL_TOOLS_LIST_CHECKED);
    }

    private void resetContainer()
    {
        if (toolPanelPresenter != null)
        {
            toolPanelPresenter.refreshDisplay();
        }
        getDisplay().setVisible(!selectionModel.getSelectedSet().isEmpty());
    }
}