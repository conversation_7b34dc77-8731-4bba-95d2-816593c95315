package ru.naumen.objectlist.server.validate;

import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.objectlist.shared.advlist.AdvlistSettingsClient;
import ru.naumen.objectlist.shared.advlist.SaveAdvlistSettingsAction;

import jakarta.annotation.Nullable;

/**
 * Валидатор настроек вида сложного списка. Проверяет наличие прав на сохранение общего вида
 *
 * <AUTHOR>
 * @since 30.03.2021
 */
public interface AdvListViewSettingsValidator
{
    boolean validate(SaveAdvlistSettingsAction advlistSettingsAction, @Nullable AdvlistSettingsClient savedSettings) throws DispatchException;
}
