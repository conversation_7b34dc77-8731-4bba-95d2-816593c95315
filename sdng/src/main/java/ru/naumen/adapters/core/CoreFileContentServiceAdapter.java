package ru.naumen.adapters.core;

import java.io.IOException;

import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.bo.CoreFile;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileContentStorage;
import ru.naumen.core.services.CoreFileContentService;

/**
 * Адаптер для сервиса {@link CoreFileContentService}
 * Делегирует вызовы внутреннему сервису модуля sdng
 */
@Component
public class CoreFileContentServiceAdapter implements CoreFileContentService
{
    private final FileContentStorage fileContentStorage;

    public CoreFileContentServiceAdapter(FileContentStorage fileContentStorage)
    {
        this.fileContentStorage = fileContentStorage;
    }

    @Override
    public byte[] getContent(CoreFile file)
    {
        if (!(file instanceof File))
        {
            throw new IllegalArgumentException();
        }
        try
        {
            return IOUtils.toByteArray(fileContentStorage.getContent((File)file));
        }
        catch (IOException e)
        {
            throw new FxException(e);
        }

    }
}
