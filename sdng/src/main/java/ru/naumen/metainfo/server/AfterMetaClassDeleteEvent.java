package ru.naumen.metainfo.server;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Событие посылается после удалением Класса/Типа, но до фиксации транзакции. Послылка этого сообщения не означает что
 * транзакция будет закоммичена. Цель посылки - проверить возможность фиксации транзакции.
 * <p>
 * Удаление может быть отменено если вызвать {@link #cancel()}. Так же можно сообщить о причине отмены сообщения методом
 * {@link #addMessage(String)}
 * 
 * <AUTHOR>
 * 
 */
public class AfterMetaClassDeleteEvent extends AbstractCancelMessage
{
    private static final long serialVersionUID = 4775085121543652902L;

    private final ClassFqn fqn;

    /**
     * Конструктор.
     * 
     * @param source
     *            удаляемый Класс/Тип
     */
    public AfterMetaClassDeleteEvent(MetaClass source, ClassFqn fqn)
    {
        super(source);
        this.fqn = fqn;
    }

    public ClassFqn getClassFqn()
    {
        return fqn;
    }
}
