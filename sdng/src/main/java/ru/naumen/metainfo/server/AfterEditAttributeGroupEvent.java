package ru.naumen.metainfo.server;

import org.springframework.context.ApplicationEvent;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.EditAttributeGroupAction;

import java.util.Collection;
import java.util.List;

/**
 * Сообщение об изменении группа атрибутов
 * <AUTHOR>
 * @since 18 мар. 2019 г.
 */
public class AfterEditAttributeGroupEvent extends ApplicationEvent
{
    private static final long serialVersionUID = 7769383075256342084L;
    private final ClassFqn fqn;
    private final Collection<String> attributeGroupCodes;

    public AfterEditAttributeGroupEvent(ClassFqn fqn, String attributeGroupCode)
    {
        this(fqn, List.of(attributeGroupCode));
    }

    public AfterEditAttributeGroupEvent(ClassFqn fqn, Collection<String> attributeGroupCodes)
    {
        super(attributeGroupCodes);
        this.fqn = fqn;
        this.attributeGroupCodes = attributeGroupCodes;
    }

    public AfterEditAttributeGroupEvent(EditAttributeGroupAction action)
    {
        this(action.getFqn(), action.getCode());
    }

    public Collection<String> getAttributeGroupCodes()
    {
        return attributeGroupCodes;
    }

    public ClassFqn getFqn()
    {
        return fqn;
    }
}
