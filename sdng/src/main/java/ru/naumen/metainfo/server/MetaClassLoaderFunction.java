/**
 * 
 */
package ru.naumen.metainfo.server;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

import com.google.common.base.Function;

/**
 * <AUTHOR>
 * @since 02.10.2012
 *
 */
@Component
public class MetaClassLoaderFunction implements Function<ClassFqn, MetaClassLite>
{
    @Inject
    private MetainfoService metainfoService;

    @Override
    public MetaClassLite apply(ClassFqn input)
    {
        if (input == null)
        {
            return null;
        }
        return metainfoService.getMetaClass(input);
    }
}