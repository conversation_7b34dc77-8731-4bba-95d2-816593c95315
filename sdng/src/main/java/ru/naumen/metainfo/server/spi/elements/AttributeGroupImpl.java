package ru.naumen.metainfo.server.spi.elements;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import jakarta.persistence.Transient;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * Группа атрибутов
 * 
 * <AUTHOR>
 *
 */
public class AttributeGroupImpl implements AttributeGroup
{
    protected final String code;
    protected final MetaClassImpl metaClass;

    public AttributeGroupImpl(MetaClassImpl metaClass, String code)
    {
        this.code = code;
        this.metaClass = metaClass;
    }

    @Override
    public Attribute getAttribute(String code)
    {
        if (!hasAttribute(code))
        {
            return null;
        }
        return metaClass.getAttribute(code);
    }

    @Override
    public List<String> getAttributeCodes()
    {
        List<String> result = new ArrayList<>();
        collectAttrCodes(result);
        return result;
    }

    @Override
    public List<Attribute> getAttributes()
    {
        return CollectionUtils.transformList(getAttributeCodes(), new Function<String, Attribute>()
        {
            @Override
            public Attribute apply(String input)
            {
                return metaClass.getAttribute(input);
            }
        });
    }

    @Override
    public String getCode()
    {
        return code;
    }

    @Override
    public ClassFqn getDeclaredMetaClass()
    {
        return getDeclaredMC().getFqn();
    }

    @Override
    public MetaClassImpl getMetaClass()
    {
        return metaClass;
    }

    @Override
    @Transient
    public MetaClassLite getMetaClassLite()
    {
        return getMetaClass();
    }

    @Override
    public String getTitle()
    {
        AttributeGroupDeclarationImpl override = metaClass.getAttributeGroupOverride(getCode());
        AttributeGroupDeclarationImpl sysOverride = metaClass.getAttributeGroupSystemOverride(getCode());

        IProperties overrideTitleAsProp = override != null ? override.getTitleAsProperties() : null;
        IProperties sysOverrideTitleAsProp = sysOverride != null ? sysOverride.getTitleAsProperties() : null;

        String title = ElementHelper.getLocalizedValue(sysOverrideTitleAsProp, overrideTitleAsProp);

        if (null != title)
        {
            return title;
        }

        AttributeGroupDeclarationImpl declaration = metaClass.getAttributeGroupDeclaration(getCode());
        if (null == declaration)
        {
            return metaClass.getParentMetaClass().getAttributeGroup(getCode()).getTitle();
        }
        return declaration.getTitle();
    }

    public boolean hasAttribute(String code)
    {
        return getAttributeCodes().contains(code);
    }

    @Override
    public boolean isHardcoded()
    {
        return getDeclaredMC().getAttributeGroupDeclaration(getCode()).isHardcoded();
    }

    @Override
    public String toString()
    {
        return "AttributeGroupImpl { metaclass: " + metaClass.getFqn() + ", code: " + getCode() + "}";
    }

    protected void collectAttrCodes(List<String> result)
    {
        MetaClassImpl parent = metaClass.getParentMetaClass();

        if (parent != null)
        {
            Collection<String> codes = parent.getAttributeGroupCodes();
            if (codes.contains(getCode()))
            {
                AttributeGroupImpl parentGroup = parent.getAttributeGroupInt(getCode());
                parentGroup.collectAttrCodes(result);
            }
        }
        process(metaClass.getAttributeGroupDeclaration(getCode()), result);
        process(metaClass.getAttributeGroupSystemOverride(getCode()), result);
        process(metaClass.getAttributeGroupOverride(getCode()), result);
    }

    protected MetaClassImpl getDeclaredMC()
    {
        MetaClassImpl current = metaClass;
        while (null == current.getAttributeGroupDeclaration(getCode()))
        {
            current = current.getParentMetaClass();
            if (null == current)
            {
                throw new ClassMetainfoServiceException("Can't find group with code: " + getCode());
            }
        }
        return current;
    }

    protected void process(AttributeGroupDeclarationImpl declaration, List<String> result)
    {
        if (null != declaration)
        {
            for (String attrCode : declaration.getAttributeCodes())
            {
                if (!result.contains(attrCode))
                {
                    result.add(attrCode);
                }
            }
            if (!CollectionUtils.isEmpty(declaration.getAttributesOrder()))
            {
                sort(result, declaration.getAttributesOrder());
            }
        }
    }

    private void sort(List<String> result, List<String> order)
    {
        Map<String, Integer> indexes = new HashMap<>();
        int idx = order.size();
        for (String str : result)
        {
            indexes.put(str, idx++);
        }
        idx = 0;
        for (String str : order)
        {
            indexes.put(str, idx++);
        }
        result.sort(Comparator.comparingInt(indexes::get));
    }

    @Override
    public @Nullable String getSettingsSet()
    {
        String groupCode = getCode();
        AttributeGroupDeclarationImpl override = metaClass.getAttributeGroupOverride(groupCode);
        AttributeGroupDeclarationImpl sysOverride = metaClass.getAttributeGroupSystemOverride(groupCode);
        AttributeGroupDeclarationImpl declaration = metaClass.getAttributeGroupDeclaration(groupCode);
        if (override != null)
        {
            return override.getSettingsSet();
        }

        if (sysOverride != null)
        {
            return sysOverride.getSettingsSet();
        }
        if (null == declaration)
        {
            return metaClass.getParentMetaClass().getAttributeGroup(groupCode).getSettingsSet();
        }
        return declaration.getSettingsSet();
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet) //NOSONAR
    {
    }
}
