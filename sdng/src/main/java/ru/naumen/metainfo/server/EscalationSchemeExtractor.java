/**
 * 
 */
package ru.naumen.metainfo.server;

import java.util.function.Function;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.escalation.EscalationSchemeValue;

/**
 * <AUTHOR>
 * @since 01.11.2012
 *
 */
@Component
public class EscalationSchemeExtractor implements Function<String, EscalationSchemeValue>
{
    @Inject
    MetainfoService metainfoService;

    @Override
    public EscalationSchemeValue apply(String input)
    {
        if (input == null)
        {
            return null;
        }
        return metainfoService.getEscalationScheme(input);
    }
}