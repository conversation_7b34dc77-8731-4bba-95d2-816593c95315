package ru.naumen.metainfo.server;

import ru.naumen.commons.shared.FxException;

/**
 * Исключение возникающее в сервисе метаинформации
 * 
 * <AUTHOR>
 * 
 */
public class ClassMetainfoServiceException extends FxException
{
    public ClassMetainfoServiceException(String msg)
    {
        super(msg, true);
    }

    public ClassMetainfoServiceException(String msg, Throwable cause)
    {
        super(msg, cause);
    }

    public ClassMetainfoServiceException(Throwable cause)
    {
        super(cause);
    }
}
