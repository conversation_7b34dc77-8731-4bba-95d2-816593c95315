package ru.naumen.metainfo.server;

import org.springframework.context.ApplicationEvent;

import ru.naumen.metainfo.server.spi.elements.AttributeImpl;

/**
 * Событие посылается после создания атрибута.
 * Может быть использовано для поддержки целостности данных,
 * связанных с наличием атрибутов.
 * 
 * <AUTHOR>
 */
public class AfterAttributeAddEvent extends ApplicationEvent
{
    private static final long serialVersionUID = -1946216051663017610L;

    public AfterAttributeAddEvent(AttributeImpl attribute)
    {
        super(attribute);
    }

    public AttributeImpl getAttribute()
    {
        return (AttributeImpl)getSource();
    }
}
