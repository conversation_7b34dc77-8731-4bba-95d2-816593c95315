package ru.naumen.metainfo.server.spi.attr;

import static ru.naumen.metainfo.shared.Constants.SystemObjectAttrType.CODE;

import java.lang.reflect.AnnotatedElement;

import org.springframework.stereotype.Component;

import ru.naumen.metainfo.server.annotations.Attribute;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.AttributeTypeImpl;
import ru.naumen.metainfo.shared.elements.SystemObjectAttributeType;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
public class SystemObjectInitializer extends AttrInitializer
{
    @Override
    public void init(MetainfoServiceBean metainfoService, AttributeTypeImpl type, AnnotatedElement element,
            Attribute attrAnnot)
    {
        type.setCode(CODE);
        String relatedObject = attrAnnot.props()[0].value();
        type.<SystemObjectAttributeType> cast().setRelatedObject(relatedObject);
    }

    @Override
    public boolean isApplicable(AnnotatedElement element, String typeCode)
    {
        return CODE.equals(typeCode);
    }
}
