package ru.naumen.metainfo.server;

import ru.naumen.metainfo.server.spi.elements.wf.StateImpl;
import ru.naumen.metainfo.shared.elements.wf.State;

/**
 * Событие посылается перед удалением {@link State статуса}.
 * Цель - проверить возможность удаления атрибута.
 * <p>
 * Удаление может быть отменено {@link #cancel()}.
 * Собщение о причине отмены {@link #addMessage(String)}
 * 
 * <AUTHOR>
 */
public class BeforeDeleteStateEvent extends AbstractCancelMessage
{
    private static final long serialVersionUID = 8968343523637527265L;

    public BeforeDeleteStateEvent(StateImpl state)
    {
        super(state);
    }

    public StateImpl getState()
    {
        return (StateImpl)getSource();
    }
}
