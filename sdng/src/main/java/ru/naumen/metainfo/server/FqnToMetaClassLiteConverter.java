/**
 * 
 */
package ru.naumen.metainfo.server;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

import com.google.common.base.Function;

/**
 * <AUTHOR>
 * @since 02.10.2012
 *
 */
@Component
public class FqnToMetaClassLiteConverter implements Function<ClassFqn, MetaClassLite>
{
    @Inject
    MetainfoService metainfoService;
    @Inject
    SnapshotService snapshotService;

    @Override
    public MetaClassLite apply(ClassFqn input)
    {
        if (input == null)
        {
            return null;
        }
        return snapshotService.prepareStrictType(metainfoService.getMetaClass(input), MetaClassLite.class);
    }
}