package ru.naumen.metainfo.server;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.ApplicationEvent;

/**
 * Сообщение которое может быть отменено. Такое сообщение посылается перед выполнением действия для проверки 
 * возможности его выполнения.
 * 
 * <AUTHOR>
 *
 */
public abstract class AbstractCancelMessage extends ApplicationEvent
{
    private boolean canceled;
    private final List<String> messages = new ArrayList<>();

    public AbstractCancelMessage(Object source)
    {
        super(source);
    }

    /**
     * Добавляет сообщение которое будет добавлено в описание почему Класс/Тип не был удален (если удаление не будет
     * завершено)
     * 
     * @param message сообщение
     */
    public void addMessage(String message)
    {
        messages.add(message);
    }

    /**
     * Отменяет удаление
     */
    public void cancel()
    {
        canceled = true;
    }

    public List<String> getMessages()
    {
        return messages;
    }

    public boolean isCanceled()
    {
        return canceled;
    }
}
