package ru.naumen.metainfo.server.spi.store;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Параметры объектов в состоянии
 * 
 * <p>Java class for StateSetting complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="StateSetting">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="title" type="{http://www.naumen.ru/metaclass-srv}LocalizedString" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *       &lt;attribute name="canView" type="{http://www.w3.org/2001/XMLSchema}boolean" />
 *       &lt;attribute name="canEdit" type="{http://www.w3.org/2001/XMLSchema}boolean" />
 *       &lt;attribute name="preFill" type="{http://www.w3.org/2001/XMLSchema}int" />
 *       &lt;attribute name="postFill" type="{http://www.w3.org/2001/XMLSchema}int" />
 *       &lt;attribute name="isRequiredInState" type="{http://www.w3.org/2001/XMLSchema}boolean" />
 *       &lt;attribute name="code" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "StateSetting", propOrder = { "title" })
public class StateSetting implements HasCode, Comparable<StateSetting>
{
    protected ArrayList<LocalizedString> title;
    @XmlAttribute
    protected Boolean canView;
    @XmlAttribute
    protected Boolean canEdit;
    @XmlAttribute
    protected Boolean requredInState = false;
    @XmlAttribute
    protected Integer preFill;
    @XmlAttribute
    protected Integer postFill;
    @XmlAttribute(required = true)
    protected String code;

    @Override
    public int compareTo(StateSetting o)
    {
        return code.compareTo(o.code);
    }
    @Override
    public boolean equals(Object obj)
    {
        if (!(obj instanceof StateSetting))
        {
            return false;
        }
        if (this == obj)
        {
            return true;
        }
        StateSetting other = (StateSetting)obj;
        return EqualsBuilder.reflectionEquals(this, other);
    }

    /**
     * Gets the value of the code property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    @Override
    public String getCode()
    {
        return code;
    }

    /**
     * Gets the value of the postFill property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPostFill()
    {
        return postFill;
    };

    /**
     * Gets the value of the preFill property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPreFill()
    {
        return preFill;
    }

    /**
     * Gets the value of the title property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the title property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTitle().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link LocalizedString }
     * 
     * 
     */
    public List<LocalizedString> getTitle()
    {
        if (title == null)
        {
            title = new ArrayList<LocalizedString>();
        }
        return this.title;
    }

    @Override
    public int hashCode()
    {
        return HashCodeBuilder.reflectionHashCode(this);
    }

    /**
     * Gets the value of the canEdit property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isCanEdit()
    {
        return canEdit;
    }

    /**
     * Gets the value of the canView property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isCanView()
    {
        return canView;
    }

    public Boolean isRequredInState()
    {
        return requredInState;
    }

    /**
     * Sets the value of the canEdit property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setCanEdit(Boolean value)
    {
        this.canEdit = value;
    }

    /**
     * Sets the value of the canView property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setCanView(Boolean value)
    {
        this.canView = value;
    }

    /**
     * Sets the value of the code property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCode(String value)
    {
        this.code = value;
    }

    /**
     * Sets the value of the postFill property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPostFill(Integer value)
    {
        this.postFill = value;
    }

    /**
     * Sets the value of the preFill property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPreFill(Integer value)
    {
        this.preFill = value;
    }

    public void setRequredInState(Boolean requredInState)
    {
        this.requredInState = requredInState;
    }

}
