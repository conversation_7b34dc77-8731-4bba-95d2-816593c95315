package ru.naumen.metainfo.server;

import ru.naumen.metainfo.shared.elements.AttributeGroup;

/**
 * Событие посылается после удаления {@link AttributeGroup группы атрибутов}, но до фиксации транзакции. Послылка этого
 * сообщения не означает что транзакция будет выполненно. Цель посылки - проверить возможность фиксации транзакции.
 * <p>
 * Удаление может быть отменено если вызвать {@link #cancel()}. Так же можно сообщить о причине отмены сообщения методом
 * {@link #addMessage(String)}
 * 
 * <AUTHOR>
 */
public class AfterAttributeGroupDeleteEvent extends AbstractCancelMessage
{
    private static final long serialVersionUID = 4775085121545652902L;

    /**
     * Конструктор.
     * 
     * @param source
     *            удаляемый Класс/Тип
     */
    public AfterAttributeGroupDeleteEvent(AttributeGroup source)
    {
        super(source);
    }
}
