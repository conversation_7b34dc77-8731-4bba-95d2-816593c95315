package ru.naumen.metainfo.server;

import ru.naumen.metainfo.shared.elements.AttributeGroup;

/**
 * Событие посылается перед удалением {@link AttributeGroup группы атрибутов}. Послылка этого сообщения не означает
 * что действие будет выполненно. Цель посылки - проверить возможность удаления группы атрибутов.
 * <p>
 * Удаление может быть отменено если вызвать {@link #cancel()}. Так же можно сообщить о причине отмены сообщения методом
 * {@link #addMessage(String)}
 * 
 * <AUTHOR>
 */
public class BeforeAttributeGroupDeleteEvent extends AbstractCancelMessage
{
    private static final long serialVersionUID = 4775085121545652902L;

    /**
     * Конструктор.
     * 
     * @param source
     *            удаляемый Класс/Тип
     */
    public BeforeAttributeGroupDeleteEvent(AttributeGroup source)
    {
        super(source);
    }
}
