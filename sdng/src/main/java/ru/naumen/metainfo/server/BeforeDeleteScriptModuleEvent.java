package ru.naumen.metainfo.server;

import java.io.Serial;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Set;

import ru.naumen.core.server.script.conf.ScriptModule;

/**
 * Событие посылается перед удалением {@link ScriptModule скриптового модуля}.
 * Цель - проверить возможность удаления.
 * <p>
 * Удаление может быть отменено {@link #cancel()}.
 * Сообщение о причине отмены {@link #addMessage(String)}
 * 
 * <AUTHOR>
 * @since 24.09.2021
 */
public class BeforeDeleteScriptModuleEvent extends AbstractCancelMessage
{
    @Serial
    private static final long serialVersionUID = 8968343523637527265L;

    public BeforeDeleteScriptModuleEvent(Collection<String> moduleCode)
    {
        super(new LinkedHashSet<>(moduleCode));
    }

    public Set<String> getScriptModuleCodes()
    {
        //noinspection unchecked
        return (Set<String>)getSource();
    }
}
