package ru.naumen.metainfo.server;

import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Событие посылается перед физическим удалением Класса/Типа. Послылка этого сообщения не означает что действие будет
 * выполненно. Цель посылки - проверить возможность удаления Класса/Типа.
 * <p>
 * Удаление может быть отменено если вызвать {@link #cancel()}. Так же можно сообщить о причине отмены сообщения методом
 * {@link #addMessage(String)}
 *
 * <AUTHOR>
 */
public class BeforeMetaClassDeleteEvent extends AbstractCancelMessage
{
    private static final long serialVersionUID = 4775085121543652902L;

    private final boolean isChild;
    private final MetaClass initMetaclass;
    private boolean manyFoundReasons;

    /**
     * Конструктор.
     *
     * @param source удаляемый Класс/Тип
     */
    public BeforeMetaClassDeleteEvent(MetaClass source, MetaClass initMetaclass, boolean isChild)
    {
        super(source);
        this.initMetaclass = initMetaclass;
        this.isChild = isChild;
    }

    /**
     * @return the isChild
     */
    public boolean isChild()
    {
        return isChild;
    }
    
    /**
     * @return the isChild
     */
    public MetaClass getInittMetaclass()
    {
        return initMetaclass;
    }


    /**
     * @return the isManyFoundReasons
     */
    public boolean isManyFoundReasons()
    {
        return manyFoundReasons;
    }

    /**
     * @param manyFoundReasons the isManyFoundReasons to set
     */
    public void setManyFoundReasons(boolean manyFoundReasons)
    {
        this.manyFoundReasons = manyFoundReasons;
    }
}
