package ru.naumen.metainfo.server;

import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Событие посылается перед удалением {@link Attribute атрибута}. Послылка этого сообщения не означает
 * что действие будет выполненно. Цель посылки - проверить возможность удаления атрибута.
 * <p>
 * Удаление может быть отменено если вызвать {@link #cancel()}. Так же можно сообщить о причине отмены сообщения методом
 * {@link #addMessage(String)}
 * 
 * <AUTHOR>
 */
public class BeforeAttributeDeleteEvent extends AbstractCancelMessage
{
    private static final long serialVersionUID = 4775085121545652902L;

    /**
     * Конструктор.
     * 
     * @param source
     *            удаляемый Класс/Тип
     */
    public BeforeAttributeDeleteEvent(Attribute source)
    {
        super(source);
    }
}
