/**
 * 
 */
package ru.naumen.metainfo.server;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import com.google.common.base.Function;

import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * <AUTHOR>
 * @since 18 дек. 2013 г.
 *
 */
@Component
public class MetaClassTitleRemovedExtractor implements Function<MetaClassLite, String>
{
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private MessageFacade messages;

    @Override
    public String apply(MetaClassLite metaClass)
    {
        String title = metaClass.getTitle();
        return metainfoUtils.isRemoved(metaClass) ? messages.getMessage("removedShort", title) : title;
    }
}