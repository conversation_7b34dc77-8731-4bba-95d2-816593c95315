package ru.naumen.metainfo.shared.ui;

import java.util.ArrayDeque;
import java.util.Queue;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Выбор типа запроса
 *
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "SelectScCase")
public class SelectScCase extends EditablePropertiesContentBase
{
    /**
     * Возвращает true если в иерархии контентов присутствует контент 
     * типа "Выбор типа запроса"
     * 
     * @param content - корень иерархии.
     */
    public static boolean hasSelectScCaseContent(Content content)
    {
        return hasSelectScCaseContent(content, false);
    }

    public static boolean hasSelectScCaseContent(Content content, boolean checkVisibility)
    {
        Queue<Content> queue = new ArrayDeque<>();
        queue.add(content);

        while (!queue.isEmpty())
        {
            Content current = queue.poll();
            for (Content child : current.getChilds())
            {
                boolean isVisible = !checkVisibility || child.isVisible();
                if (child instanceof SelectScCase && isVisible)
                {
                    return true;
                }
                queue.add(child);
            }
        }
        return false;
    }

    @Override
    protected Content newInstance()
    {
        return new SelectScCase();
    }
}
