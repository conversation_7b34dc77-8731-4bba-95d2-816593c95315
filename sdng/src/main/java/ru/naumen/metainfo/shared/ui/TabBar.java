package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.base.Predicates;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;

import ru.naumen.core.shared.ui.ITab;
import ru.naumen.core.shared.ui.ITabBar;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.segment.MetainfoSegmentNames.ContentSegments;

/**
 * Tab-bar (панель вкладок)
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "TabBar", propOrder = { "hideSingleTab", "hideTabBorder", "tab" })
public class TabBar extends FlowContent implements ITabBar
{
    public static final String PREFIX = "tabs_";

    protected ArrayList<Tab> tab;

    /**
     * Главная панель вкладок карточки: hasHead = true
     * Обычный контент: hasHead = false
     */
    protected boolean hasHead;

    /**
     * Скрывать корешок вкладки, если выведена одна вкладка.
     */
    protected boolean hideSingleTab = false;

    /**
     * Скрывать рамку панели вкладок, если выведена одна вкладка.
     */
    protected boolean hideTabBorder = false;

    @Override
    @XmlTransient
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public Collection<Content> getChilds()
    {
        return (Collection)Collections2.filter(getTab(), Predicates.notNull());
    }

    /**
     * @return вкладки расположенные на панели
     */
    @XmlElement(required = true)
    public List<Tab> getTab()
    {
        if (tab == null)
        {
            tab = Lists.newArrayList();
        }
        return this.tab;
    }

    @XmlTransient
    @Override
    public ArrayList<? extends ITab> getTabs()
    {
        return tab;
    }

    @XmlAttribute
    public boolean isHasHead()
    {
        return hasHead;
    }

    /**
     * Скрывать единственную вкладку
     * @return true, если требуется скрыть единственную вкладку. false в противном случае
     */
    @XmlElement
    public boolean isHideSingleTab()
    {
        return hideSingleTab;
    }

    @XmlElement
    public boolean isHideTabBorder()
    {
        return hideTabBorder;
    }

    @Override
    public void removeChild(Content content)
    {
        getTab().remove(content);
    }

    public void setHasHead(boolean hasHead)
    {
        this.hasHead = hasHead;
    }

    public void setHideSingleTab(boolean hideSingleTab)
    {
        this.hideSingleTab = hideSingleTab;
    }

    public void setHideTabBorder(boolean hideTabBorder)
    {
        this.hideTabBorder = hideTabBorder;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof TabBar)
        {
            TabBar tabBar = (TabBar)content;
            tabBar.hasHead = hasHead;
            ObjectUtils.cloneCollection(tab, tabBar.tab = new ArrayList<>());
            tabBar.hideSingleTab = hideSingleTab;
            tabBar.hideTabBorder = hideTabBorder;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new TabBar();
    }

    @Override
    public String getSegmentType()
    {
        return ContentSegments.TAB_BARS;
    }
}
