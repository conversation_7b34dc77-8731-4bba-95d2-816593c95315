package ru.naumen.metainfo.shared.ui;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlEnumValue;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.google.common.base.Predicates;
import com.google.common.collect.Sets;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.spi.store.jxb.IPropertiesJaxbAdapter;

/**
 * Действие на панели действий
 * 
 */
@SuppressWarnings("serial")
@XmlType(name = "Tool")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlSeeAlso({ ActionTool.class, ChangeStateTool.class, ShowRemovedTool.class, RefreshObjectListTool.class,
        AddFromObjectListTool.class, AddFileTool.class, EditFromObjectListTool.class, MassEditFromObjectListTool.class,
        EventFilterTool.class, AdvlistPrsSelectTool.class, SearchBoxTool.class,
        ScaleSelectTool.class, FilterModeSelectTool.class })

public class Tool extends SystemContentBase
{
    /**
     * Тип сущности, к которой применяется действие
     */
    public interface AppliedToType
    {
        String CURRENT_OBJECT = "currentObject";
        String RELATED_OBJECT = "relatedObject";
        String LIST_OBJECTS = "listObjects";
    }

    public interface ButtonCodes
    {
        String ADVLIST_FILTER = "showAdvlistFilter";
        String ADVLIST_SORT = "showAdvlistSort";
        String REFRESH = "refresh";
        String REFRESH_PUSH = "refreshPushableButton";
    }

    public enum PresentationType
    {
        @XmlEnumValue("default")
        DEFAULT("default"),
        @XmlEnumValue("defaultTextOnly")
        DEFAULT_TEXT_ONLY("defaultTextOnly"),
        @XmlEnumValue("defaultIconOnly")
        DEFAULT_ICON_ONLY("defaultIconOnly"),
        @XmlEnumValue("push")
        PUSH("push"),
        @XmlEnumValue("pushTextOnly")
        PUSH_TEXT_ONLY("pushTextOnly"),
        @XmlEnumValue("pushImageOnly")
        PUSH_ICON_ONLY("pushImageOnly"),
        @XmlEnumValue("link")
        LINK("link"),
        @XmlEnumValue("icon")
        ICON("icon"),
        @XmlEnumValue("iconText")
        ICON_TEXT("iconText"),
        @XmlEnumValue("none")
        NONE("none");

        public static final PresentationType[] VALUES = PresentationType.values();

        public static boolean isIconOnlyButton(PresentationType presentation)
        {
            return DEFAULT_ICON_ONLY.equals(presentation)
                    || PUSH_ICON_ONLY.equals(presentation)
                    || ICON.equals(presentation);
        }

        public static boolean isTextOnlyButton(PresentationType presentation)
        {
            return DEFAULT_TEXT_ONLY.equals(presentation)
                    || PUSH_TEXT_ONLY.equals(presentation)
                    || LINK.equals(presentation);
        }

        private final String value;

        PresentationType(String value)
        {
            this.value = value;
        }

        public String value()
        {
            return value;
        }

        public static PresentationType fromValue(String value)
        {
            for (PresentationType presentation: VALUES)
            {
                if (presentation.value.equals(value))
                {
                    return presentation;
                }
            }
            throw new IllegalArgumentException(value);
        }
    }

    public static final Set<String> TOOLS_WITH_PUSH_PRESENTATION = Sets.newHashSet(ButtonCodes.ADVLIST_FILTER,
            ButtonCodes.ADVLIST_SORT, ButtonCodes.REFRESH, ButtonCodes.REFRESH_PUSH);

    public static final Set<PresentationType> PUSH_PRESENTATIONS = Sets.newHashSet(PresentationType.PUSH,
            PresentationType.PUSH_ICON_ONLY, PresentationType.PUSH_TEXT_ONLY);

    public static final Set<PresentationType> DEFAULT_PRESENTATIONS = Sets.newHashSet(PresentationType.DEFAULT,
            PresentationType.DEFAULT_ICON_ONLY, PresentationType.DEFAULT_TEXT_ONLY);

    public static final Set<PresentationType> INLINE_PRESENTATIONS = Sets.newHashSet(PresentationType.LINK,
            PresentationType.ICON, PresentationType.ICON_TEXT);

    /**
     * Метод позволяет склонировать действия панелей с сохранением значений контентов, 
     * на которых будут располагаться {@link ToolPanel панели инструментов}.
     * Вызывается при передаче контента с клиента на сервер
     * @param tools исходный набор контентов действий для клонирования
     * @return склонированный контент действий
     */
    public static HashSet<Tool> cloneToolsWithAssociatedContent(List<Tool> tools)
    {
        // В общем случае tools могут иметь различный associated content.        
        Set<Content> contents = Sets.newHashSet();
        for (Tool tool : tools)
        {
            if (contents.contains(tool.getAssociatedContent()))
            {
                continue;
            }

            Content associatedContentClone = tool.getAssociatedContent().clone();
            associatedContentClone.setParent(null);
            if (associatedContentClone instanceof ObjectListBase && !ObjectUtils
                    .equals(((ObjectListBase)associatedContentClone).getToolPanel(), tool.getParent().getParent()))
            {
                // В Advlist'е после активации массовых операций
                // свойство ToolPanel у ObjectListBase указывает на другую панель инструментов.
                // Поэтому явно присваиваем панель, привязанную к tool
                ToolPanel actualToolPanelClone = (ToolPanel)tool.getParent().getParent().clone();
                actualToolPanelClone.setParent(associatedContentClone);
                ((ObjectListBase)associatedContentClone).setToolPanel(actualToolPanelClone);
            }
            contents.add(associatedContentClone);
        }

        HashSet<Tool> clonedTools = Sets.newHashSet();
        HashSet<Tool> sourceTools = Sets.newHashSet(tools);

        for (Content content : contents)
        {
            // associatedContent имеет один child элемент - это ToolPanel
            ToolPanel toolPanelClone = (ToolPanel)content.getChilds().iterator().next();
            Set<Tool> panelToolsClone = Sets.newHashSet(toolPanelClone.getTools());
            clonedTools.addAll(Sets.filter(panelToolsClone, Predicates.in(sourceTools)));
        }

        return clonedTools;
    }

    private PresentationType presentationType;

    private String appliedToType;

    private String iconCode;
    /**
     * Признак того, что действие является массовой операцией
     */
    private boolean massOperation = false;

    /**
     * Признак того, что действие необходимо сохранять в базу,
     * оно не генерируется в автоматически в коде 
     */
    private boolean persistent = true;

    private MapProperties params = new MapProperties();

    public Tool()
    {
        this(null);
    }

    public Tool(@Nullable ToolBar parent)
    {
        setParent(parent);
        presentationType = PresentationType.DEFAULT;
        appliedToType = AppliedToType.CURRENT_OBJECT;
    }

    @XmlElement
    public String getAppliedToType()
    {
        return appliedToType;
    }

    /**
     * Метод возвращает контент, на котором расположена
     * {@link ToolPanel панель инструментов}, содержащая данный tool
     */
    @XmlTransient
    public Content getAssociatedContent()
    {
        Content content = getParent();
        if (content == null || (content = content.getParent()) == null)
        {
            return null;
        }
        return content.getParent();
    }

    @Override
    public Collection<Content> getChilds()
    {
        return Collections.emptyList();
    }

    @XmlElement
    public String getIconCode()
    {
        return iconCode;
    }

    /**
     * Параметры инструмента
     */
    @XmlJavaTypeAdapter(IPropertiesJaxbAdapter.class)
    @XmlElement(name = "params")
    public IProperties getParams()
    {
        return params;
    }

    @Override
    @XmlTransient
    public ToolBar getParent()
    {
        return (ToolBar)super.getParent();
    }

    @XmlElement
    public PresentationType getPresentationType()
    {
        return presentationType;
    }

    @XmlTransient
    public boolean isMassOperation()
    {
        return massOperation;
    }

    @XmlTransient
    public boolean isPersistent()
    {
        return persistent;
    }

    @Override
    public void removeChild(Content content)
    {

    }

    public void setAppliedToType(String appliedToType)
    {
        this.appliedToType = appliedToType;
    }

    public void setIconCode(@Nullable String iconCode)
    {
        this.iconCode = iconCode;
    }

    public void setMassOperation(boolean massOperation)
    {
        this.massOperation = massOperation;
    }

    public void setParams(IProperties params)
    {
        this.params = new MapProperties(params);
    }

    public void setPersistent(boolean persistent)
    {
        this.persistent = persistent;
    }

    public void setPresentationType(PresentationType presentationType)
    {
        this.presentationType = presentationType;
    }

    public Tool setToolBar(ToolBar toolBar)
    {
        super.setParent(toolBar);
        return this;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof Tool)
        {
            Tool tool = (Tool)content;
            tool.setParent(tool.getParent());
            tool.setParams(getParams());
            tool.setPresentationType(getPresentationType());
            tool.setAppliedToType(getAppliedToType());
            tool.setProfiles(getProfiles());
            tool.setVersProfiles(getVersProfiles());
            tool.setUuid(getUuid());
            tool.setIconCode(getIconCode());
            tool.setMassOperation(massOperation);
            tool.setPersistent(persistent);
        }
    }

    @Override
    protected Content newInstance()
    {
        return new Tool();
    }
}