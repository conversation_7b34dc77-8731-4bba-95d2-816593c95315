package ru.naumen.metainfo.shared.ui;

import java.util.List;
import java.util.Set;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

/**
 * Константы относыщиеся к {@link ToolBar}
 * 
 * <AUTHOR>
 *
 */
public final class Constants
{
    public static final class Titles
    {
        public static final String ADD = "add";
        public static final String ADD_DELETE_FROM_RELOBJLIST = "addDeleteObjButton";
        public static final String ADD_COMMENT = "addComment";
        public static final String ADD_FILE = "addFile";
        public static final String ADD_RELATION = "addRelation";
        public static final String ADD_SC = "addSc";
        public static final String CHANGE_ASSOCIATION = "changeAssociation";
        public static final String CHANGE_PASSWORD = "changePassword";
        public static final String CHANGE_STATE = "changeState";
        public static final String CHANGE_TYPE = "changeType";
        public static final String COPY = "copy";
        public static final String COPY_LINK_TO_LIST = "copyLinkToList";
        public static final String CREATE_NEW_REPORT_WITH_CURRENT_PARAMETERS = "createNewReportWithCurrentParameters";
        public static final String DELETE = "delete";
        public static final String EDIT = "edit";
        public static final String EDIT_RESPONSIBLE = "editResponsible";
        public static final String MASS_EDIT = "massEdit";
        public static final String EXPORT_ADVLIST = "exportAdvlist";
        public static final String EXPORT_REPORT_BUTTON = "exportReportButton";
        public static final String EXPORT_EMAIL = "exportReportButton";
        public static final String EXTENDED_SEARCH_PARAMS = "extendedSearchParams";
        public static final String FILTER = "filter";
        public static final String FILTRATION = "filtration";
        public static final String MOVE = "move";
        public static final String OPEN_MASS_SERVICE_CALL_FORM = "openMassServiceCallForm";
        public static final String PARAMETERS = "parameters";
        public static final String PRINT = "print";
        public static final String REBUILD_REPORT = "rebuildReport";
        public static final String REFRESH = "refresh";
        public static final String REFRESH_LIST = "refreshList";
        public static final String REMOVE = "remove";
        public static final String RESTORE = "restore";
        public static final String SAVE_PRESENTATION = "savePresentation";
        public static final String SHOW_RELATED = "showRelated";
        public static final String SHOW_REMOVED = "showRemoved";
        public static final String SORT = "sort";
        public static final String TEST_METRIC = "testMetric";
        public static final String CALC_STATUS = "calcStatus";
        public static final String RUN_IT_NOW = "runItNow";
        public static final String SYNCHRONIZE_SCRIPT_MODULES = "synchronizeScriptModules";

        private Titles()
        {
        }
    }

    public static final String ADD = "add";

    public static final String ADD_COMMENT = "addComment";

    public static final String ADD_FILE = "addFile";

    public static final String ADD_SC = "addSC";

    public static final String ADD_FORMS_HIERARCHIES = "addFormsHierarchies";

    public static final String APPLY_ADVLIST_FILTER = "applyAdvlistFilter";

    public static final String APPLY_AND_HIDE_ADVLIST_FILTER = "applyAndHideAdvlistFilter";

    public static final String AVAILABLE_TAGS = "availableTags";

    public static final String PRESET_ADVLIST_FILTER = "presetAdvlistFilter";

    public static final String CANCEL_ADVLIST_FILTER = "cancelAdvlistFilter";

    public static final String CHANGE_ASSOCIATION = "changeAssociation";

    public static final String CHANGE_CASE = "changeCase";

    public static final String CHANGE_PASSWORD = "changePasswd";

    public static final String CHANGE_STATE = "changeState";

    public static final String SHARE = "share";

    public static final String COPY_OBJECT = "copy";

    public static final String DELETE = "del";

    public static final String TEST_METRIC = "testMetric";

    public static final String CALC_STATUS = "calcStatus";

    public static final String RUN_IT_NOW = "runItNow";

    public static final String SYNCHRONIZE_SCRIPT_MODULES = "synchronizeScriptModules";

    public static final String FQN_OF_CLASS = "fqnOfClass";

    public static final String CASES = "cases";

    public static final String TAGS = "tags";

    public static final String CLASSES_HIERARCHY = "classesHierarchy";

    //@formatter:off
    public static final Set<String> SINGLE_OBJECT_ACTIONS = Sets.newHashSet(
            Constants.CHANGE_CASE,
            Constants.CHANGE_ASSOCIATION,
            Constants.COPY_OBJECT,
            Constants.EDIT,
            Constants.OPEN_MASS_SERVICE_CALL_FORM_FOR_MASS_CALL,
            Constants.OPEN_MASS_SERVICE_CALL_FORM_FOR_REGULAR_CALL,
            Constants.ADD_SC,
            Constants.TEST_METRIC,
            Constants.CALC_STATUS,
            Constants.RUN_IT_NOW);

    public static final Set<String> ADVLIST_ACTIONS = Sets.newHashSet(
            Constants.SAVE_ADVLIST_PRS,
            Constants.SHOW_ADVLIST_FILTER,
            Constants.SHOW_ADVLIST_SORT,
            Constants.EXPORT_ADVLIST,
            Constants.REFRESH,
            Constants.RESET_GLOBAL_DEFAULT_SETTINGS,
            Constants.COPY_LINK_TO_LIST);

    /**
     * Кнопки адвлиста, которые можно нажимать даже при свернутом модальном окне
     */
    public final static Set<String> FORCE_ENABLED_ADVLIST_BUTTONS = Sets.newHashSet(
            Constants.SHOW_ADVLIST_FILTER,
            Constants.SHOW_ADVLIST_SORT,
            Constants.SAVE_ADVLIST_PRS,
            Constants.REFRESH,
            Constants.SHOW_REMOVED);
    //@formatter:on

    /**
     * Перечень кодов действий доступных в МК
     * 
     * При добавлении новых необходимо добавить их и в
     * {@link ru.naumen.mobile.services.MobileObjectActionsService#getSystemActionCaption(String))
     * 
     */
    //@formatter:off
    public static final List<String> MOBILE_AVAILABLE_ACTIONS = Lists.newArrayList(
            Constants.EDIT,
            Constants.CHANGE_STATE,
            Constants.CHANGE_CASE,
            Constants.CHANGE_ASSOCIATION,
            Constants.EDIT_RESPONSIBLE);
    //@formatter:on

    public static final String DELETE_LINK = "delLink";

    public static final String OPEN_FILE_PREVIEW = "openFilePreview";

    public static final String DOWNLOAD_FILE = "downloadFile";

    public static final String DOWNLOAD_SINGLE_FILE = "downloadSingleFile";

    public static final String EDIT = "edit";

    public static final String EDIT_COMMENT = "editComment";

    public static final String SHOW_MORE_COMMENT_ATTRS = "showMoreCommentAttrs";

    public static final String SHOW_ALL_COMMENT_FILES = "showAllCommentFiles";

    public static final String EDIT_PROPERTIES = "editProperties";

    public static final String EDIT_PROPERTY = "editProperty";

    public static final String EDIT_RESPONSIBLE = "editResponsible";

    public static final String EXPORT_ADVLIST = "exportAdvlist";

    public static final String MASS_EDIT = "massEdit";

    public static final String ADD_DELETE_OBJS = "addDeleteObjs";

    public static final String DELETE_ACTION_TOOL = "deleteActionTool";

    public static final String EDIT_FORMS = "editForms";

    /**
     * Признак, что используется форма массового редактирования по умолчанию
     */
    public static final String DEFAULT_MASS_EDIT_FORM = "defaultMassEditFormIsUsed";

    /**
     * {@link ToolBar#getFeatures() Признак} {@link ToolBar}'а, позволяющий заполнить его кнопками быстрой смены статуса
     */
    public static final String FAST_CHANGE_STATE_TB_FEATURE = "fastChangeState";

    public static final String WINDOW_ACTIONS_TB_FEATURE = "windowActions";

    public static final String WINDOW_ACTIONS_META_TOOL = "meta#windowActionsTool";

    public static final String FIRE_USER_EVENT = "fireUserEvent";

    public static final String LINK = "link";

    public static final String LINK_QUEUE = "addLinkToJMSQueue";

    public static final String MOVE_OBJECT = "move";

    public static final String OPEN_MASS_SERVICE_CALL_FORM_FOR_MASS_CALL = "openMassServiceCallFormForMassCall";

    public static final String OPEN_MASS_SERVICE_CALL_FORM_FOR_REGULAR_CALL = "openMassServiceCallFormForRegularCall";

    public static final String REFRESH = "refresh";

    public static final String REMOVE_OBJECT = "remove";

    public static final String REPROCESS_MAIL = "reprocessMail";

    public static final String RESET_ADVLIST_FILTER = "resetAdvlistFilter";

    public static final String RESTORE_OBJECT = "restore";

    public static final String SAVE_ADVLIST_PRS = "saveAdvlistPrs";

    public static final String SAVE_REPORT = "saveReport";

    public static final String CREATE_NEW_REPORT = "createNewReport";

    public static final String SEARCH_PARAMS = "searchParams";

    public static final String SHOW_ADVLIST_FILTER = "showAdvlistFilter";

    public static final String SHOW_ADVLIST_SORT = "showAdvlistSort";

    public static final String SHOW_REMOVED = "showRemoved";

    public static final String RESET_GLOBAL_DEFAULT_SETTINGS = "resetGlobalDefaultSettings";

    public static final String TITLE_CODE = "titleCode";

    public static final String COPY_LINK_TO_LIST = "copyLinkToList";

    /**
     * Смена состояния в соотв. с конкретным переходом
     */
    public static final String TRANSITION_CHANGE_STATE = "transitionChangeState";

    public static final String TYPE_NAME_AND_TITLE_CODE = "type_name_and_title";

    /**
     * Таббар на всю строку. Следующий с новой строки
     */
    public static final String WHOLE_LINE_TB_FEATURE = "wholeLine";

    /**
     * Дополнительные коды для отображения заголовка на карточке объекта
     */
    public static final String WITHOUT_CAPTION_CODE = "without_caption";
    // Заголовок карточки
    public static final String CARD_CAPTION_CODE = "card_caption";
    // Строка
    public static final String STRING_CAPTION_CODE = "string_caption";

    /**
     * Код свойства, помещаемого в контекст проверки прав на тулы при массовой операции
     * Свойство нужно для того чтобы уметь отличать проверку прав на тулы при массовой операции
     * от проверки прав на тулы из тулбара списка объектов
     */
    public static final String CHECKING_FOR_MASS_OPERATIONS = "#checkingForMassOperation";

    /**
     * Код свойства для обозначения, что полный список тулов был проверен 
     */
    public static final String FULL_TOOLS_LIST_CHECKED = "#fullToolsListChecked";

    public static final String ATTRIBUTE_GROUP = "attributeGroup";
    public static final String ATTRIBUTE_FQN = "attributeFqn";

    public static final String TAB_DELIMITER = ",";

    public static final String TAB_PARAM_KEY = "contentTab";
    public static final String HIGHLIGHT_KEY = "highLightContent";

    public static final String CURRENT_OBJECT = "currentObject";

    /**
     * Перечень кодов системных действий МК, с признаком "Передавать геопозицию устройства"
     */
    //@formatter:off
    public static final List<String> MOBILE_SYSTEM_ACTIONS_WITH_GEO_REQUIRED = Lists.newArrayList(
            Constants.EDIT,
            Constants.CHANGE_STATE,
            Constants.CHANGE_CASE,
            Constants.CHANGE_ASSOCIATION,
            Constants.EDIT_RESPONSIBLE);
    //@formatter:on

    private Constants()
    {
    }
}
