package ru.naumen.metainfo.shared.ui.customform;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 29.04.2016
 *
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "CustomFormContainer")
@XmlRootElement
public class CustomFormContainer
{
    public static final Function<CustomFormContainer, CustomForm> EXTRACTOR = new Function<CustomFormContainer, CustomForm>()
    {
        @edu.umd.cs.findbugs.annotations.SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
        @Override
        public CustomForm apply(CustomFormContainer input)
        {
            return input.getCustomForm();
        }
    };

    private CustomForm customForm;

    public CustomFormContainer()
    {

    }

    public CustomFormContainer(CustomForm customForm)
    {
        this.customForm = customForm;
    }

    @XmlElement(required = true)
    public CustomForm getCustomForm()
    {
        return customForm;
    }

    public void setCustomForm(CustomForm customForm)
    {
        this.customForm = customForm;
    }

}
