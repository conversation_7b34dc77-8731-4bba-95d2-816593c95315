package ru.naumen.metainfo.shared.ui;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.segment.MetainfoSegmentNames.ContentSegments;

/**
 * Базовый класс для контентов размещаемых на Layout
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "FlowContent", propOrder = { "position", "visibilityCondition"})
// @formatter:off
@XmlSeeAlso({
    TabBar.class,
    ShowCaptionContent.class })
//@formatter:on
public abstract class FlowContent extends Content implements HasVisibilityCondition, HasCanBeCollapsed
{
    protected Position position = Position.FULL;
    protected ListFilter visibilityCondition;

    // Это должно быть в ShowCaptionContent, но из-за неразберихи с TabBar временно здесь
    //опция контента, показывающая может ли он сворачиваться
    private boolean canBeCollapsed = true;

    /**
     * @return позицию на {@link Layout}
     */
    @XmlElement(required = true)
    public Position getPosition()
    {
        return position;
    }

    @XmlElement
    @Override
    public ListFilter getVisibilityCondition()
    {
        if (null == visibilityCondition)
        {
            visibilityCondition = new ListFilter();
        }
        return visibilityCondition;
    }

    public void setPosition(Position value)
    {
        this.position = value;
    }

    public void setVisibilityCondition(ListFilter visibilityCondition)
    {
        this.visibilityCondition = visibilityCondition;
    }

    @Override
    public String getSegmentID()
    {
        return getUuid();
    }

    @Override
    public String getSegmentType()
    {
        return ContentSegments.CONTENTS;
    }

    @Override
    public boolean isDetachableSegment()
    {
        return true;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof FlowContent)
        {
            FlowContent flowContent = (FlowContent)content;
            flowContent.position = position;
            flowContent.visibilityCondition = ObjectUtils.clone(visibilityCondition);
            fillParent(flowContent.visibilityCondition, flowContent);
        }
    }

    @Override
    @XmlTransient
    public boolean isCanBeCollapsed()
    {
        return canBeCollapsed;
    }

    @Override
    public void setCanBeCollapsed(boolean canBeCollapsed)
    {
        this.canBeCollapsed = canBeCollapsed;
    }
}
