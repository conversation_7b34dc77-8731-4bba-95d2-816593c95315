package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.base.Predicates;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.core.server.plainlistuuidsfilter.CountObjectsCacheService;
import ru.naumen.core.shared.internalscrolling.InternalScrollingType;
import ru.naumen.core.shared.ui.ITab;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * "Вкладка" TabBar-а на которой может быть отображен другой контент
 *
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "Tab", propOrder = { "caption", "countObjectsContentUuids", "considerListFilter",
        "cacheNumberObjectsOnTab", "toolPanel", "layout", "internalScrolling", "visibilityCondition" })
public class Tab extends Content implements IsSerializable, ITab, HasToolPanel, HasVisibilityCondition, HasLayout
{
    protected ArrayList<LocalizedString> caption;
    protected Layout layout;
    protected boolean additional;
    protected ToolPanel toolPanel;
    protected ListFilter visibilityCondition;
    /**
     * uuids контентов-списков, кол-во объектов в которых нужно отображать в названии вкладки
     */
    private List<String> countObjectsContentUuids;
    /**
     * учитывать ли настройки фильтрации в списке
     */
    private boolean considerListFilter;
    /**
     * кэшировать ли количество объектов на вкладке. По умолчанию - кешировать при
     * {@link CountObjectsCacheService#getTimeLiveCacheOnTabs()} > 0
     */
    private Boolean cacheNumberObjectsOnTab = true;
    /**
     * Использовать внутренний скроллинг элементов страницы
     */
    private String internalScrolling = InternalScrollingType.INHERITED;

    public boolean captionIsNull()
    {
        return caption == null;
    }

    /**
     * @return название вкладки
     */
    @Override
    @XmlElement(required = true)
    @Nonnull
    public List<LocalizedString> getCaption()
    {
        if (caption == null)
        {
            caption = new ArrayList<>();
        }
        return this.caption;
    }

    @Override
    @XmlTransient
    public Collection<Content> getChilds()
    {
        return Collections2.filter(Arrays.asList(getToolPanel(), getLayout()), Predicates.notNull());
    }

    @Override
    @XmlElement(name = "countObjectsContentUuid")
    public List<String> getCountObjectsContentUuids()
    {
        if (countObjectsContentUuids == null)
        {
            countObjectsContentUuids = new ArrayList<>();
        }
        return countObjectsContentUuids;
    }

    @Override
    @XmlElement
    @Nullable
    public String getInternalScrolling()
    {
        return internalScrolling;
    }

    /**
     * @return {@link Layout} вкладки
     */
    @Override
    @XmlElement(required = true)
    public Layout getLayout()
    {
        return layout;
    }

    @Override
    @XmlElement
    public ToolPanel getToolPanel()
    {
        if (null == toolPanel && isHasToolPanel())
        {
            toolPanel = new ToolPanel();
            toolPanel.setParent(this);
            toolPanel.setUseSystemSettings(true);
        }
        return toolPanel;
    }

    @XmlElement
    @Override
    public ListFilter getVisibilityCondition()
    {
        if (null == visibilityCondition)
        {
            visibilityCondition = new ListFilter();
        }
        return visibilityCondition;
    }

    /**
     * Gets the value of the additional property.
     * 
     */
    @XmlAttribute(required = true)
    public boolean isAdditional()
    {
        return additional;
    }

    @Override
    @XmlElement
    public boolean isConsiderListFilter()
    {
        return considerListFilter;
    }

    /**
     * Проверить кэшируется ли количество объектов на вкладке.
     * @return {@link Boolean} Nullable-тип для того, чтобы отсутствующее значение в метаинфе воспринималось по
     * умолчанию как true. True - количество кешируется на вкладке, false - не кешируется.
     */
    @Override
    @XmlElement
    @Nullable
    public Boolean isCacheNumberObjectsOnTab()
    {
        return cacheNumberObjectsOnTab == null || cacheNumberObjectsOnTab;
    }

    @Override
    public boolean isHasToolPanel()
    {
        return null != getParent() && getParent().getParent() instanceof Window; //NOPMD
    }

    /**
     * @return true если выбрано свойство "Отображать количество объектов на вкладке"
     */
    @Override
    @XmlTransient
    public boolean isShowObjectCount()
    {
        return !ObjectUtils.isEmpty(getCountObjectsContentUuids());
    }

    @Override
    public void removeChild(Content content)
    {
        if (toolPanel == content)
        {
            toolPanel = null;
        }
        if (layout == content)
        {
            layout = null;
        }
    }

    public void setAdditional(boolean value)
    {
        this.additional = value;
    }

    public void setConsiderListFilter(boolean considerListFilter)
    {
        this.considerListFilter = considerListFilter;
    }

    public void setCacheNumberObjectsOnTab(Boolean cacheNumberObjectsOnTab)
    {
        this.cacheNumberObjectsOnTab = cacheNumberObjectsOnTab;
    }

    public void setCountObjectsContentUuids(Collection<String> countObjectsContentUuids)
    {
        this.countObjectsContentUuids = Lists.newArrayList(countObjectsContentUuids);
    }

    public void setInternalScrolling(String internalScrolling)
    {
        this.internalScrolling = internalScrolling;
    }

    public void setLayout(Layout value)
    {
        this.layout = value;
    }

    @Override
    public void setToolPanel(ToolPanel toolPanel)
    {
        this.toolPanel = toolPanel;
    }

    public void setVisibilityCondition(ListFilter visibilityCondition)
    {
        this.visibilityCondition = visibilityCondition;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof Tab)
        {
            Tab tab = (Tab)content;
            tab.additional = additional;
            ObjectUtils.cloneCollection(caption, tab.caption = new ArrayList<>());
            tab.layout = ObjectUtils.clone(layout);
            tab.considerListFilter = considerListFilter;
            tab.cacheNumberObjectsOnTab = cacheNumberObjectsOnTab;
            tab.countObjectsContentUuids =
                    countObjectsContentUuids != null ? new ArrayList<>(countObjectsContentUuids) : null;
            tab.toolPanel = ObjectUtils.clone(toolPanel);
            tab.internalScrolling = internalScrolling;
            tab.visibilityCondition = null == visibilityCondition ? null : ObjectUtils.clone(visibilityCondition);
            fillParent(tab.visibilityCondition, tab);
        }
    }

    @Override
    protected Content newInstance()
    {
        return new Tab();
    }
}
