package ru.naumen.metainfo.shared.ui.customform;

import java.util.ArrayList;

import ru.naumen.metainfo.shared.elements.MetaClassLite;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * Хранит список типов, для которых применима пользовательская форма
 * <AUTHOR>
 * @since 16.05.16
 */
public class CustomFormTargets implements IsSerializable
{
    @edu.umd.cs.findbugs.annotations.SuppressWarnings(justification = "No bug", value = "SE_BAD_FIELD")
    private ArrayList<MetaClassLite> targetCases;

    public CustomFormTargets()
    {
        this.targetCases = Lists.newArrayList();
    }

    public CustomFormTargets(ArrayList<MetaClassLite> targetCases)
    {
        this.targetCases = targetCases;
    }

    public ArrayList<MetaClassLite> getTargets()
    {
        return this.targetCases;
    }
}
