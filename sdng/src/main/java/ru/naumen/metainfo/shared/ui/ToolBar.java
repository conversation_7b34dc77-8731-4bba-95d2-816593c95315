package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElements;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.base.Predicates;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;

import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Панель действий (action bar)
 *
 */
@SuppressWarnings("serial")
@XmlType(name = "ToolBar")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class ToolBar extends SystemContentBase
{
    protected ArrayList<Tool> tools = Lists.newArrayList();
    private String code;
    private ArrayList<String> features = Lists.newArrayList();

    @XmlTransient
    private boolean clearedAfterPermissionCheck = false;

    public ToolBar()
    {
        this(null);
    }

    public ToolBar(@Nullable ToolPanel toolPanel)
    {
        setParent(toolPanel);
    }

    public ToolBar addTool(Tool tool)
    {
        tools.add(tool);
        tool.setParent(this);
        return this;
    }

    @Override
    @XmlTransient
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public Collection<Content> getChilds()
    {
        return (Collection)Collections2.filter(getTools(), Predicates.notNull());
    }

    @XmlElement
    public String getCode()
    {
        return code;
    }

    /**
     * Список признаков тулбара, являющихся дополнительными модификаторами его поведения
     * @return
     */
    @XmlElement(name = "feature")
    public List<String> getFeatures()
    {
        return features;
    }

    @Override
    @XmlTransient
    public ToolPanel getParent()
    {
        return (ToolPanel)super.getParent();
    }

    @XmlTransient
    public List<Tool> getPersistentTools()
    {
        List<Tool> tools = new ArrayList<>();
        for (Tool tool : getTools())
        {
            if (tool.isPersistent())
            {
                tools.add(tool);
            }
        }
        return tools;
    }

    @Override
    public String getSegmentID()
    {
        return getCode();
    }

    /**
     * @return true, если все тулы в тулбаре стали невидимыми в ходе проверки прав, false иначе. По умолчанию - false.
     */
    public boolean isClearedAfterPermissionCheck()
    {
        return clearedAfterPermissionCheck;
    }

    @Override
    public void removeChild(Content content)
    {
        getTools().remove(content);
    }


    /**
     * Устанавливает флаг clearedAfterPermissionCheck в необходимое значение.
     * True, если все тулы в тулбаре стали невидимыми в ходе проверки прав, false иначе.
     */
    public void setClearedAfterPermissionCheck(boolean clearedAfterPermissionCheck)
    {
        this.clearedAfterPermissionCheck = clearedAfterPermissionCheck;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);
        if (content instanceof ToolBar)
        {
            ToolBar toolBar = (ToolBar)content;
            toolBar.code = code;
            toolBar.features = features != null ? new ArrayList<>(features) : null;
            ObjectUtils.cloneCollection(tools, toolBar.tools = new ArrayList<>());
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ToolBar();
    }

    /**
     * @return список действий расположенных на панели
     */
    // @formatter:off
    @XmlElements({
        @XmlElement(name = "action" ,                       type = ActionTool.class),
        @XmlElement(name = "change-state" ,                 type = ChangeStateTool.class),
        @XmlElement(name = "show-removed",                  type = ShowRemovedTool.class),
        @XmlElement(name = "refresh-list",                  type = RefreshObjectListTool.class),
        @XmlElement(name = "add-from-list",                 type = AddFromObjectListTool.class),
        @XmlElement(name = "add-file",                      type = AddFileTool.class),
        @XmlElement(name = "add-delete-from-list",          type = AddDeleteFromObjectListTool.class),
        @XmlElement(name = "advlist-presentation-select",   type = AdvlistPrsSelectTool.class),
        @XmlElement(name = "user-event-tool",               type = UserEventTool.class),
        @XmlElement(name = "edit-from-list",                type = EditFromObjectListTool.class),
        @XmlElement(name = "mass-edit-from-list",           type = MassEditFromObjectListTool.class),
        @XmlElement(name = "copy-link-to-list",             type = CopyLinkToListTool.class),
        @XmlElement(name = "tools")
    })
    // @formatter:off
    public List<Tool> getTools()
    {
        return tools;
    }
}