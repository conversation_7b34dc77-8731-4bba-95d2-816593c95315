package ru.naumen.metainfo.shared.ui;

import static java.util.stream.Collectors.toList;
import static ru.naumen.commons.shared.utils.FunctionUtils.newType;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Lists;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * <AUTHOR>
 * @since 19.09.2011
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ListFilter", propOrder = { "elements", "title", "toolPanel" })
public class ListFilter extends SystemContentBase implements AbstractContentContainer<ListFilterAndElement>
{
    private String title;
    private List<ListFilterAndElement> elements;
    private ToolPanel toolPanel;

    @Override
    public Collection<Content> getChilds()
    {
        return getElements().stream()
                .filter(Objects::nonNull)
                .map(newType(Content.class))
                .collect(toList());
    }

    @Override
    @XmlElement
    public List<ListFilterAndElement> getElements()
    {
        if (elements == null)
        {
            elements = Lists.newArrayList();
        }
        return this.elements;
    }

    @XmlElement
    public String getTitle()
    {
        return title;
    }

    @XmlElement
    public ToolPanel getToolPanel()
    {
        return toolPanel;
    }

    @Override
    public void removeChild(Content content)
    {
        getElements().remove(content);
    }

    /**
     * Исключить пустые фильтры
     */
    public void removeEmptyElements()
    {
        ArrayList<ListFilterAndElement> elementsForDel = Lists.newArrayList();
        for (ListFilterAndElement element : elements)
        {
            element.removeEmptyChilds();
            if (element.getElements().isEmpty())
            {
                elementsForDel.add(element);
            }
        }
        elements = Lists.newArrayList(CollectionUtils.subtract(elements, elementsForDel));
    }

    public void setElements(List<ListFilterAndElement> elements)
    {
        this.elements = elements;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public void setToolPanel(ToolPanel toolPanel)
    {
        this.toolPanel = toolPanel;
    }

    public void setWithSemanticAllowed(boolean isWithSemanticAllowed)
    {
        getElements().forEach(element -> element.setWithSemanticAllowed(isWithSemanticAllowed));
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof ListFilter)
        {
            final ListFilter list = (ListFilter)content;
            list.title = title;
            ObjectUtils.cloneCollection(elements, list.elements = new ArrayList<>());
            list.toolPanel = ObjectUtils.clone(toolPanel);
            fillParent(list.toolPanel, list);
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ListFilter();
    }

    @Override
    public String toString()
    {
        return getElements().toString();
    }
}
