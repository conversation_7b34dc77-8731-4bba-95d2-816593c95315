package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.base.Predicates;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;

import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Панель действий (action bar)
 * 
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ActionBar", propOrder = { "elements" })
public class ActionBar extends SystemContentBase
{
    protected ArrayList<ActionBarElement> elements;

    @Override
    @XmlTransient
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public Collection<Content> getChilds()
    {
        return (Collection)Collections2.filter(getElements(), Predicates.notNull());
    }

    /**
     * @return список действий расположенных на панеле
     */
    @XmlElement
    public List<ActionBarElement> getElements()
    {
        if (elements == null)
        {
            elements = Lists.newArrayList();
        }
        return this.elements;
    }

    @Override
    public void removeChild(Content content)
    {
        getElements().remove(content);
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof ActionBar)
        {
            ObjectUtils.cloneCollection(elements, ((ActionBar)content).getElements());
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ActionBar();
    }
}
