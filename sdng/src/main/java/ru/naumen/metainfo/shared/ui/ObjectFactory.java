//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-833
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2011.01.25 at 03:29:48 PM YEKT
//

package ru.naumen.metainfo.shared.ui;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlElementDecl;
import jakarta.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;

import ru.naumen.metainfo.shared.ClassFqn;

/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the ru.naumen.metainfo.shared.ui package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 *
 */
@XmlRegistry
public class ObjectFactory
{
    private final static QName _Case_QNAME = new QName(null, "case");
    private final static QName _Class_QNAME = new QName(null, "class");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: ru.naumen.metainfo.shared.ui
     *
     */
    public ObjectFactory()
    {
    }

    /**
     * Create an instance of {@link ActionBar }
     *
     */
    public ActionBar createActionBar()
    {
        return new ActionBar();
    }

    /**
     * Create an instance of {@link ActionBarElement }
     *
     */
    public ActionBarElement createActionBarElement()
    {
        return new ActionBarElement();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ClassFqn }{@code >}}
     *
     */
    @XmlElementDecl(name = "case")
    public JAXBElement<ClassFqn> createCase(ClassFqn value)
    {
        return new JAXBElement<ClassFqn>(_Case_QNAME, ClassFqn.class, null, value);
    }

    /**
     * Create an instance of {@link ChildObjectList }
     *
     */
    public ChildObjectList createChildObjectList()
    {
        return new ChildObjectList();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ClassFqn }{@code >}}
     *
     */
    @XmlElementDecl(name = "class")
    public JAXBElement<ClassFqn> createClass(ClassFqn value)
    {
        return new JAXBElement<ClassFqn>(_Class_QNAME, ClassFqn.class, null, value);
    }

    /**
     * Create an instance of {@link ClientInfo }
     *
     */
    public ClientInfo createClientInformation()
    {
        return new ClientInfo();
    }

    /**
     * Create an instance of {@link EditablePropertyList }
     *
     */
    public EditablePropertyList createEditablePropertyList()
    {
        return new EditablePropertyList();
    }

    /**
     * Create an instance of {@link Form }
     *
     */
    public Form createForm()
    {
        return new Form();
    }

    /**
     * Create an instance of {@link Layout }
     *
     */
    public Layout createLayout()
    {
        return new Layout();
    }

    /**
     * Create an instance of {@link MassProblems }
     *
     */
    public MassProblems createMassProblems()
    {
        return new MassProblems();
    }

    public ObjectGraph createObjectGraph()
    {
        return new ObjectGraph();
    }

    /**
     * Create an instance of {@link ObjectList }
     *
     */
    public ObjectList createObjectList()
    {
        return new ObjectList();
    }

    /**
     * Create an instance of {@link ObjectListBase }
     *
     */
    public ObjectListBase createObjectListBase()
    {
        return new ObjectListBase();
    }

    /**
     * Create an instance of {@link PropertyList }
     *
     */
    public PropertyList createPropertyList()
    {
        return new PropertyList();
    }

    /**
     * Create an instance of {@link PropertyListBase }
     *
     */
    public PropertyListBase createPropertyListBase()
    {
        return new PropertyListBase();
    }

    /**
     * Create an instance of {@link RelObjPropertyList }
     *
     */
    public RelObjPropertyList createRelObjPropertyList()
    {
        return new RelObjPropertyList();
    }

    /**
     * Create an instance of {@link SelectCase }
     *
     */
    public SelectCase createSelectCase()
    {
        return new SelectCase();
    }

    /**
     * Create an instance of {@link SelectContacts }
     *
     */
    public SelectContacts createSelectContacts()
    {
        return new SelectContacts();
    }

    /**
     * Create an instance of {@link SelectParent }
     *
     */
    public SelectParent createSelectParent()
    {
        return new SelectParent();
    }

    /**
     * Create an instance of {@link SelectScCase }
     *
     */
    public SelectScCase createSelectScCase()
    {
        return new SelectScCase();
    }

    /**
     * Create an instance of {@link Tab }
     *
     */
    public Tab createTab()
    {
        return new Tab();
    }

    /**
     * Create an instance of {@link TabBar }
     *
     */
    public TabBar createTabBar()
    {
        return new TabBar();
    }

    /**
     * Create an instance of {@link Form }
     *
     */
    public UIContainer createUIContainer()
    {
        return new UIContainer();
    }

    /**
     * Create an instance of {@link Window }
     *
     */
    public Window createWindow()
    {
        return new Window();
    }

    /**
     * Create an instance of {@link WorkflowContent }
     *
     */
    public WorkflowContent createWorkflowContent()
    {
        return new WorkflowContent();
    }

    public Tool createTool()
    {
        return new Tool();
    }

    public ToolBar createToolBar()
    {
        return new ToolBar();
    }

    public ToolPanel createToolPanel()
    {
        return new ToolPanel();
    }
}
