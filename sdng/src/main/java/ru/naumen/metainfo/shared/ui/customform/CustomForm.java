package ru.naumen.metainfo.shared.ui.customform;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElements;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfo.shared.ui.Layout;

/**
 * Настройки специальных форм
 * 
 * <AUTHOR>
 * @since 22.04.2016
 *
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "CustomForm")
@XmlSeeAlso({ CustomFormBase.class })
public abstract class CustomForm extends Form
{
    private String attrGroupCode;
    private CustomFormType formType;
    @Nullable
    private CustomFormTargets customFormTargets;

    public CustomForm()
    {
        this.layout = new Layout();
    }

    @XmlElement(required = true)
    public String getAttrGroup()
    {
        return attrGroupCode;
    }

    @XmlElements({
            @XmlElement(name = "change-case-form", type = ChangeCaseForm.class),
            @XmlElement(name = "quick-form", type = QuickForm.class),
            @XmlElement(name = "mass-edit-form", type = MassEditForm.class),
            @XmlElement(name = "custom-form")
    })
    public CustomFormType getFormType()
    {
        return formType;
    }

    /**
     * Список классов, для которых настроена форма перехода
     * В хранилище метаинформации не помещается, предназначен только для передачи на сторону
     * клиента.
     * @return customFormTargets
     */
    @Nullable
    @XmlTransient
    public CustomFormTargets getTargetCases()
    {
        return customFormTargets;
    }

    public void setAttrGroup(String attrGroup)
    {
        this.attrGroupCode = attrGroup;
    }

    public void setFormType(CustomFormType formType)
    {
        this.formType = formType;
    }

    public void setTargetCases(@Nullable CustomFormTargets formTargets)
    {
        this.customFormTargets = formTargets;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof CustomForm)
        {
            CustomForm form = (CustomForm)content;
            form.attrGroupCode = this.attrGroupCode;
            form.formType = this.formType;
        }
    }
}
