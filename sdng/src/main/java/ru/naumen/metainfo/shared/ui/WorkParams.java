
package ru.naumen.metainfo.shared.ui;

import static com.google.common.collect.Lists.newArrayList;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.core.shared.HasClone;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Содержит параметры работ для {@link GanttContent}
 * <AUTHOR>
 * @since 19.11.2013 г.
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "WorkParams")
public class WorkParams implements IsSerializable, Serializable, HasClone
{
    /**
     * Параметр позволяющий отображать только связанные работы по цепи ссылок на атрибуты {@link WorkParams#linkedAttrChain}
     */
    private boolean linkedWithCurrentObject;

    /**
     * Цепь ссылок на атрибуты, представляющая связь с целевой сущностью
     */
    protected List<AttrReference> linkedAttrChain;

    /**
     * Ссылка на атрибут. Оставлена для совместимости с предыдущими версиями.
     */
    @Deprecated
    private String linkedAttribute;

    /**
     * Класс объектов которые необходимо отображать в контенте в качестве работ
     */
    private ClassFqn workMetaClass;

    /**
     * Типы объектов которые необходимо отображать в контенте в качестве работ
     */
    private List<ClassFqn> workMetaClasses;

    /**
     * Атрибут по которому определяется ресурс
     */
    private String resource;

    /**
     * Атрибут по которому определяется дата начала задачи
     */
    private String startDate;

    /**
     * Атрибут по которому дата завершения задачи
     */
    private String endDate;

    /**
     * Атрибут по которому определяются предшествующие работы
     */
    private String previousWork;

    /**
     * Группа атрибутов ресурса использующаяся для редатирования работ
     */
    private String attributeGroup;

    /**
     * Атрибут - дедлайн работы 
     */
    private String deadline;

    /**
     * Использование цвета статуса работ
     */
    private String stateColorPrs;
    
    private ClassFqn currentContextClassFqn;

    public WorkParams()
    {
    }

    @Override
    public Object clone()
    {
        WorkParams clone = new WorkParams();
        clone.setAttributeGroup(attributeGroup);
        clone.setEndDate(endDate);
        clone.setLinkedAttribute(linkedAttribute);
        ObjectUtils.cloneCollection(linkedAttrChain, clone.linkedAttrChain = new ArrayList<>());
        clone.setLinkedWithCurrentObject(linkedWithCurrentObject);
        clone.setPreviousWork(previousWork);
        clone.setResource(resource);
        clone.setStartDate(startDate);
        clone.setWorkMetaClass(workMetaClass);
        if (workMetaClasses != null)
        {
            clone.setWorkMetaClasses(new ArrayList<>(workMetaClasses));
        }
        clone.setDeadline(deadline);
        clone.setStateColorPrs(stateColorPrs);
        return clone;
    }

    @XmlElement
    public String getAttributeGroup()
    {
        return attributeGroup;
    }

    @XmlElement
    public String getDeadline()
    {
        return deadline;
    }

    @XmlElement
    public String getEndDate()
    {
        return endDate;
    }

    @XmlElement(name = "linked-attr-chain")
    public List<AttrReference> getLinkedAttrChain()
    {
        if (linkedAttrChain == null)
        {
            linkedAttrChain = Lists.newArrayList();
        }
        if (linkedAttrChain.isEmpty() && linkedAttribute != null && currentContextClassFqn != null)
        {
            linkedAttrChain.add(new AttrReference(currentContextClassFqn, linkedAttribute));
        }
        return linkedAttrChain;
    }

    @XmlElement
    @Deprecated
    public String getLinkedAttribute()
    {
        return linkedAttribute;
    }

    @XmlElement
    public String getPreviousWork()
    {
        return previousWork;
    }

    @XmlElement
    public String getResource()
    {
        return resource;
    }

    @XmlElement
    public String getStartDate()
    {
        return startDate;
    }

    @XmlElement
    public String getStateColorPrs()
    {
        return stateColorPrs;
    }

    @XmlElement
    public ClassFqn getWorkMetaClass()
    {
        return workMetaClass;
    }

    @XmlElement
    public List<ClassFqn> getWorkMetaClasses()
    {
        if (workMetaClasses == null)
        {
            workMetaClasses = newArrayList();
        }
        return workMetaClasses;
    }

    @XmlElement
    public boolean isLinkedWithCurrentObject()
    {
        return linkedWithCurrentObject;
    }

    /**
     * Код атрибута связи класса-источника
     * 
     * @return код атрибута класса сущности-источника или null, если связь не определена. 
     */
    public String getSrcClassAttrCode()
    {
        return !getLinkedAttrChain().isEmpty() ? getLinkedAttrChain().get(0).getAttrCode() : getLinkedAttribute();
    }
    
    public ClassFqn getCurrentContextClassFqn()
    {
        return currentContextClassFqn;
    }

    public void setAttributeGroup(String attributeGroup)
    {
        this.attributeGroup = attributeGroup;
    }

    public void setDeadline(String deadline)
    {
        this.deadline = deadline;
    }

    public void setEndDate(String endDate)
    {
        this.endDate = endDate;
    }

    @Deprecated
    public void setLinkedAttribute(String linkedAttribute)
    {
        this.linkedAttribute = linkedAttribute;
    }

    public void setLinkedAttrChain(ArrayList<AttrReference> attrChain)
    {
        this.linkedAttrChain = attrChain;
        if(attrChain != null && !attrChain.isEmpty())
        {
            setLinkedAttribute(null);
        }
    }

    public void setLinkedWithCurrentObject(boolean linkedWithCurrentObject)
    {
        this.linkedWithCurrentObject = linkedWithCurrentObject;
    }

    public void setPreviousWork(String previousWork)
    {
        this.previousWork = previousWork;
    }

    public void setResource(String resource)
    {
        this.resource = resource;
    }

    public void setStartDate(String startDate)
    {
        this.startDate = startDate;
    }

    public void setStateColorPrs(String stateColorPrs)
    {
        this.stateColorPrs = stateColorPrs;
    }

    public void setWorkMetaClass(ClassFqn workMetaClass)
    {
        this.workMetaClass = workMetaClass;
    }

    public void setWorkMetaClasses(List<ClassFqn> workMetaClasses)
    {
        this.workMetaClasses = workMetaClasses;
    }

    public void setCurrentContextClassFqn(ClassFqn currentContextClassFqn)
    {
        this.currentContextClassFqn = currentContextClassFqn;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        //@formatter:off
        WorkParams that = (WorkParams)o;
        return isLinkedWithCurrentObject() == that.isLinkedWithCurrentObject()
                && Objects.equals(getLinkedAttrChain(), that.getLinkedAttrChain())
                && Objects.equals(getLinkedAttribute(), that.getLinkedAttribute())
                && Objects.equals(getWorkMetaClass(), that.getWorkMetaClass())
                && Objects.equals(getWorkMetaClasses(), that.getWorkMetaClasses())
                && Objects.equals(getResource(), that.getResource())
                && Objects.equals(getStartDate(), that.getStartDate())
                && Objects.equals(getEndDate(), that.getEndDate())
                && Objects.equals(getPreviousWork(), that.getPreviousWork())
                && Objects.equals(getAttributeGroup(), that.getAttributeGroup())
                && Objects.equals(getDeadline(), that.getDeadline())
                && Objects.equals(getStateColorPrs(), that.getStateColorPrs())
                && Objects.equals(getCurrentContextClassFqn(), that.getCurrentContextClassFqn());
        //@formatter:on
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(isLinkedWithCurrentObject(), getLinkedAttrChain(), getLinkedAttribute(),
                getWorkMetaClass(), getWorkMetaClasses(), getResource(), getStartDate(), getEndDate(),
                getPreviousWork(), getAttributeGroup(), getDeadline(), getStateColorPrs(),
                getCurrentContextClassFqn());
    }
}
