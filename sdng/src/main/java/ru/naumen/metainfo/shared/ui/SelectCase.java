package ru.naumen.metainfo.shared.ui;

import java.util.ArrayDeque;
import java.util.Deque;
import java.util.List;

import com.google.common.collect.Lists;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Выбор типа объекта
 *
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "SelectCase")
public class SelectCase extends EditablePropertiesContentBase
{
    /**
     * Возвращает список контентов типа SelectCase, вложенных в toLookIn
     */
    public static List<SelectCase> getSelectCaseContents(Content container)
    {
        List<SelectCase> result = Lists.newArrayList();
        Deque<Content> contents = new ArrayDeque<>();
        contents.add(container);
        while (!contents.isEmpty())
        {
            Content content = contents.poll();
            if (content instanceof SelectCase)
            {
                result.add((SelectCase)content);
            }
            contents.addAll(content.getChilds());
        }
        return result;
    }

    public static boolean hasSelectCaseContent(Content container)
    {
        return !getSelectCaseContents(container).isEmpty();
    }

    @Override
    protected Content newInstance()
    {
        return new SelectCase();
    }
}