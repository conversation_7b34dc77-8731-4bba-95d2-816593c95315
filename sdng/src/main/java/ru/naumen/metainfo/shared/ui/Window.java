package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nonnull;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.base.Predicates;
import com.google.common.collect.Collections2;

import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Карточка отображения объекта.
 * Карточка обязательно содержит TabBar непосредственно в котором
 * отображается контент.
 */
@SuppressWarnings("serial")
@XmlRootElement
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "Window")
public class Window extends SystemContentBase implements HasToolPanel, HasObjectCardCaption
{
    protected ArrayList<LocalizedString> caption;
    protected TabBar tabBar;
    protected ToolPanel toolPanel;
    protected String objectCardCaptionAttributeCode;
    protected List<LocalizedString> objectCardCaptionString;

    /**
     * @return название контента
     */
    @Override
    @XmlElement(required = true)
    public List<LocalizedString> getCaption()
    {
        if (caption == null)
        {
            caption = new ArrayList<>();
        }
        return this.caption;
    }

    @Override
    @XmlTransient
    public Collection<Content> getChilds()
    {
        return Collections2.filter(Arrays.asList(getTabBar(), getToolPanel()), Predicates.notNull());
    }

    @Override
    @XmlElement
    public String getObjectCardCaptionAttributeCode()
    {
        return objectCardCaptionAttributeCode;
    }

    @Override
    @XmlElement
    public List<LocalizedString> getObjectCardCaptionString()
    {
        if (objectCardCaptionString == null)
        {
            objectCardCaptionString = new ArrayList<>();
        }
        return objectCardCaptionString;
    }

    /**
     * @return панель вкладок 
     */
    @XmlElement(required = true)
    public TabBar getTabBar()
    {
        return tabBar;
    }

    @Override
    @XmlElement
    @Nonnull
    public ToolPanel getToolPanel()
    {
        if (null == toolPanel)
        {
            toolPanel = new ToolPanel();
        }
        return toolPanel;
    }

    public boolean isToolPanelExist()
    {
        return toolPanel != null;
    }

    @Override
    public void removeChild(Content content)
    {
        getTabBar().removeChild(content);
        if (toolPanel == content)
        {
            toolPanel = null;
        }
    }

    public void setObjectCardCaptionAttributeCode(String objectCardCaptionAttributeCode)
    {
        this.objectCardCaptionAttributeCode = objectCardCaptionAttributeCode;
    }

    public void setObjectCardCaptionString(List<LocalizedString> objectCardCaptionString)
    {
        this.objectCardCaptionString = new ArrayList<>(objectCardCaptionString);
    }

    public void setTabBar(TabBar value)
    {
        this.tabBar = value;
        tabBar.setParent(this);
    }

    @Override
    public void setToolPanel(ToolPanel toolPanel)
    {
        this.toolPanel = toolPanel;
    }

    @Override
    public String toString()
    {
        return "Window " + getCaption();
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);
        if (content instanceof Window)
        {
            ObjectUtils.cloneCollection(caption, ((Window)content).getCaption());
            ((Window)content).tabBar = ObjectUtils.clone(tabBar);
            ((Window)content).toolPanel = ObjectUtils.clone(toolPanel);
            ((Window)content).objectCardCaptionAttributeCode = objectCardCaptionAttributeCode;
            ((Window)content).objectCardCaptionString = objectCardCaptionString;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new Window();
    }
}
