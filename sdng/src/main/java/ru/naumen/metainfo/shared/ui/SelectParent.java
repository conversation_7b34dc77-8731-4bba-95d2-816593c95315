/**
 * 
 */
package ru.naumen.metainfo.shared.ui;

import java.util.function.Predicate;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 * @since 28.03.2013
 *
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "SelectParent")
public class SelectParent extends EditablePropertiesContentBase
{
    public static final Predicate<FlowContent> IS_SELECT_PARENT = content -> content instanceof SelectParent;

    @Override
    protected Content newInstance()
    {
        return new SelectParent();
    };
}