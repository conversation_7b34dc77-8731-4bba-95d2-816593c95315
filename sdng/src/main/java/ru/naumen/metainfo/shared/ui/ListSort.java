package ru.naumen.metainfo.shared.ui;

import static ru.naumen.commons.shared.utils.FunctionUtils.newType;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Lists;

import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * <AUTHOR>
 * @since 30.09.2011
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ListOrder", propOrder = { "elements" })
public class ListSort extends SystemContentBase implements AbstractContentContainer<ListSortElement>
{
    private List<ListSortElement> elements;

    private boolean originalSort;

    @Override
    public Collection<Content> getChilds()
    {
        return getElements().stream().filter(Objects::nonNull).map(newType(Content.class)).collect(Collectors.toList());
    }

    @Override
    @XmlElement
    public List<ListSortElement> getElements()
    {
        if (elements == null)
        {
            elements = Lists.newArrayList();
        }
        return elements;
    }

    @Override
    public void removeChild(Content content)
    {
        getElements().remove(content);
    }

    public void setElements(List<ListSortElement> elements)
    {
        this.elements = elements;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof ListSort)
        {
            ListSort sort = (ListSort)content;
            ObjectUtils.cloneCollection(elements, sort.elements = new ArrayList<>());
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ListSort();
    }

    @XmlTransient
    public boolean isDefault()
    {
        return elements == null;
    }

    public void setOriginalSort(boolean isOriginalSort)
    {
        this.originalSort = isOriginalSort;
    }

    @XmlTransient
    public boolean isOriginalSort()
    {
        return originalSort;
    }
}
