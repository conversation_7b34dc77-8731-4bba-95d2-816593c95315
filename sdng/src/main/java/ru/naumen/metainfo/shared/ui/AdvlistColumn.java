package ru.naumen.metainfo.shared.ui;

import java.io.Serializable;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.core.shared.HasClone;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.autobean.wrappers.IListColumn;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Колонка адвлиста с представлением
 * <AUTHOR>
 * @since 14.12.2012
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "AdvlistColumn", propOrder = { "attrCode", "presentationCode", "titleColumn", "widthColumn" })
public class AdvlistColumn implements IsSerializable, Serializable, IListColumn, HasClone, HasCode
{
    private static final long serialVersionUID = -6317381767809646341L;
    @CheckForNull
    private String attrCode;
    private String titleColumn;
    @CheckForNull
    private String presentationCode;
    /**
     * Ширина колонки заданная пользователем.
     * По умолчанию, если размер расчитывается автоматически тогда width = 0.
     */
    private int widthColumn = 0;

    public AdvlistColumn()
    {
    }

    public AdvlistColumn(String attrCode)
    {
        this.attrCode = attrCode;
    }

    public AdvlistColumn(@Nullable String attrCode, @Nullable String presentationCode, int widthColumn)
    {
        this.attrCode = attrCode;
        this.presentationCode = presentationCode;
        this.widthColumn = widthColumn;
    }

    public AdvlistColumn(@Nullable String attrCode, @Nullable String presentationCode, @Nullable String titleColumn,
            int widthColumn)
    {
        this.attrCode = attrCode;
        this.presentationCode = presentationCode;
        this.titleColumn = titleColumn;
        this.widthColumn = widthColumn;
    }

    @Override
    public Object clone()
    {
        return new AdvlistColumn(attrCode, presentationCode, titleColumn, widthColumn);
    }

    @Override
    public boolean equals(Object o)
    {
        if (!(o instanceof AdvlistColumn))
        {
            return false;
        }
        AdvlistColumn column = (AdvlistColumn)o;
        return ObjectUtils.equals(attrCode, column.attrCode)
                && ObjectUtils.equals(presentationCode, column.presentationCode)
                && ObjectUtils.equals(titleColumn, column.titleColumn);
    }

    @CheckForNull
    @Override
    @XmlElement(name = "attr-code")
    public String getAttrCode()
    {
        return attrCode;
    }

    @Override
    public String getCode()
    {
        return attrCode;
    }

    @CheckForNull
    @Override
    @XmlElement(name = "presentation-code")
    public String getPresentationCode()
    {
        return presentationCode;
    }

    public String getTitleColumn()
    {
        return titleColumn;
    }

    @Override
    public int getWidthColumn()
    {
        return widthColumn;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(attrCode, presentationCode);
    }

    public void setAttrCode(String attrCode)
    {
        this.attrCode = attrCode;
    }

    public void setPresentationCode(String presentationCode)
    {
        this.presentationCode = presentationCode;
    }

    public void setTitleColumn(String titleColumn)
    {
        this.titleColumn = titleColumn;
    }

    public void setWidthColumn(int widthColumn)
    {
        this.widthColumn = widthColumn;
    }
}
