package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Lists;

import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.HasDataToken;
import ru.naumen.metainfo.shared.HasIntegrityHash;

/**
 * Иерархическая таблица
 *
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "HierarchyGrid")
public class HierarchyGrid extends ShowCaptionContent implements HasToolPanel, HasMassOperationsPanel,
        HasListFilters, HasObjectFilters, HasDataToken, HasIntegrityHash
{
    /**
     * Параметр "Фокусироваться на текущем объекте"
     */
    public enum CardObjectFocus
    {
        /**
         * Выключено (по умолчанию)
         */
        OFF,

        /**
         * Фокусировка со скрытием
         */
        FOCUS_WITH_HIDDEN,

        /**
         * Фокусировка без скрытия
         */
        FOCUS_WITHOUT_HIDDEN
    }

    private String focusOnCardObject = CardObjectFocus.OFF.name();
    private boolean buildHierarchyFromCurrentObject = false;

    private String structuredObjectsViewCode;
    private ToolPanel toolPanel;
    private ToolPanel massOperationsPanel;
    private Map<String, ListFilter> objectFilters;
    private Map<String, HierarchyGridDefaultViewSettings> defaultSettings;

    private String dataToken;
    private HierarchyGridMassOperationSettings hierarchyGridMassOperationSettings;

    @Override
    public Collection<ListFilter> getAllObjectFilters()
    {
        return Collections.unmodifiableList(new ArrayList<>(getObjectFilters().values()));
    }

    @Nullable
    @Override
    public String getDataToken()
    {
        return dataToken;
    }

    @XmlElement(name = "focusOnCardObject")
    public String getFocusOnCardObject()
    {
        return focusOnCardObject;
    }

    @Override
    @XmlTransient
    public List<ListFilter> getGlobalListFilters()
    {
        return getObjectFilters().values().stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    @XmlTransient
    public List<ListFilter> getUserEditableListFilters()
    {
        return new ArrayList<>();
    }

    @XmlElement(name = "objectFilters")
    public Map<String, ListFilter> getObjectFilters()
    {
        if (null == objectFilters)
        {
            objectFilters = new HashMap<>();
        }
        return objectFilters;
    }

    @XmlElement(name = "defaultSettings")
    public Map<String, HierarchyGridDefaultViewSettings> getDefaultSettings()
    {
        if (null == defaultSettings)
        {
            defaultSettings = new HashMap<>();
        }
        return defaultSettings;
    }

    @XmlElement(name = "structuredObjectsViewCode")
    public String getStructuredObjectsViewCode()
    {
        return structuredObjectsViewCode;
    }

    @Override
    public int integrityHashCode()
    {
        return Objects.hash(getUuid(), getStructuredObjectsViewCode(), isBuildHierarchyFromCurrentObject());
    }

    @XmlElement(name = "buildHierarchyFromCurrentObject")
    public boolean isBuildHierarchyFromCurrentObject()
    {
        return buildHierarchyFromCurrentObject;
    }

    public void setBuildHierarchyFromCurrentObject(boolean buildHierarchyFromCurrentObject)
    {
        this.buildHierarchyFromCurrentObject = buildHierarchyFromCurrentObject;
    }

    @Override
    public void setDataToken(@Nullable String token)
    {
        this.dataToken = token;
    }

    public void setFocusOnCardObject(String focusOnCardObject)
    {
        this.focusOnCardObject = focusOnCardObject;
    }

    public void setObjectFilters(Map<String, ListFilter> objectFilters)
    {
        this.objectFilters = objectFilters;
    }

    public void setDefaultSettings(Map<String, HierarchyGridDefaultViewSettings> defaultSettings)
    {
        this.defaultSettings = defaultSettings;
    }

    public void setStructuredObjectsViewCode(String structuredObjectsViewCode)
    {
        this.structuredObjectsViewCode = structuredObjectsViewCode;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof HierarchyGrid)
        {
            HierarchyGrid grid = (HierarchyGrid)content;
            grid.structuredObjectsViewCode = structuredObjectsViewCode;
            grid.buildHierarchyFromCurrentObject = buildHierarchyFromCurrentObject;
            grid.focusOnCardObject = focusOnCardObject;
            grid.toolPanel = ObjectUtils.clone(toolPanel);
            grid.massOperationsPanel = ObjectUtils.clone(massOperationsPanel);
            if (null != grid.massOperationsPanel)
            {
                grid.massOperationsPanel.setParent(grid);
            }
            if (null != objectFilters)
            {
                grid.objectFilters = new HashMap<>();
                objectFilters.forEach((code, filter) -> grid.objectFilters.put(code, ObjectUtils.clone(filter)));
            }
            if (null != defaultSettings)
            {
                grid.defaultSettings = new HashMap<>();
                defaultSettings.forEach((code, settings) -> grid.defaultSettings.put(
                        code, new HierarchyGridDefaultViewSettings(settings)));
            }
            grid.dataToken = dataToken;
            grid.hierarchyGridMassOperationSettings = ObjectUtils.clone(hierarchyGridMassOperationSettings);
        }
    }

    @Override
    protected Content newInstance()
    {
        return new HierarchyGrid();
    }

    @Override
    @XmlElement
    public ToolPanel getMassOperationsPanel()
    {
        if (null == massOperationsPanel)
        {
            massOperationsPanel = new ToolPanel(this);
            massOperationsPanel.setUseSystemSettings(true);
        }
        return massOperationsPanel;
    }

    @Override
    public void setMassOperationsPanel(ToolPanel massOperationsPanel)
    {
        this.massOperationsPanel = massOperationsPanel;
    }

    @Override
    @XmlElement
    public ToolPanel getToolPanel()
    {
        if (null == toolPanel)
        {
            toolPanel = new ToolPanel(this);
        }
        return toolPanel;
    }

    @Override
    public void setToolPanel(ToolPanel toolPanel)
    {
        this.toolPanel = toolPanel;
    }

    @Override
    @XmlTransient
    public Collection<Content> getChilds()
    {
        Collection<Content> result = Lists.newArrayList();
        result.add(getToolPanel());
        return result;
    }

    @XmlElement
    public HierarchyGridMassOperationSettings getHierarchyGridMassOperationSetting()
    {
        return hierarchyGridMassOperationSettings != null ? hierarchyGridMassOperationSettings:
                new HierarchyGridMassOperationSettings();
    }

    public void setHierarchyGridMassOperationSetting(
            HierarchyGridMassOperationSettings hierarchyGridMassOperationSettings)
    {
        this.hierarchyGridMassOperationSettings = hierarchyGridMassOperationSettings;
    }
}
