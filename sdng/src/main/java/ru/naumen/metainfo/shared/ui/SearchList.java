package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Lists;

import ru.naumen.core.shared.RemovedMode;
import ru.naumen.fts.shared.SearchFilters.SearchFilter;

/**
 * Список объектов - результат поиска
 * Хоть этот список и наследуется от Контента Список объектов, в реальности он Контентом не является!
 * Это динамический список. Никакие его собственные настройки в метаинфе не хранятся.
 *
 * <AUTHOR>
 * @since 31.01.2012
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "SearchList")
public class SearchList extends ObjectListBase
{
    public static final String NO_SEARCH_RESULTS = "noSearchResults";

    private String searchString;
    private SearchFilter searchFilter;
    private RemovedMode removedMode;
    /**
     * Список uuid'ов объектов, которые надо показывать в списке, если они были определены ранее
     * (чтобы открыть список результатов поиска, надо сначала выполнить поиск и убедиться, что найдено больше двух объектов)
     * Устанавливается в null, если этот список изменяется в результате удаления или архивирования\возвращения из архива объекта,
     * что является сигналом к тому, что надо снова выполнить поиск при обновлении списка
     */
    private ArrayList<String> searchResult;

    private boolean full = true;

    public SearchList()
    {
    }

    @XmlElement
    public RemovedMode getRemovedMode()
    {
        return removedMode;
    }

    @XmlTransient
    public SearchFilter getSearchFilter()
    {
        return searchFilter;
    }

    @XmlTransient
    @CheckForNull
    public List<String> getSearchResult()
    {
        return searchResult;
    }

    @XmlElement
    public String getSearchString()
    {
        return searchString;
    }

    @Override
    public int integrityHashCode()
    {
        return Objects.hash(super.integrityHashCode(), getSearchResult());
    }

    @XmlElement
    public boolean isFull()
    {
        return full;
    }

    public void setFull(boolean full)
    {
        this.full = full;
    }

    public void setRemovedMode(RemovedMode removedMode)
    {
        this.removedMode = removedMode;
    }

    public void setSearchFilter(SearchFilter searchFilter)
    {
        this.searchFilter = searchFilter;
    }

    public void setSearchResult(@Nullable List<String> searchResult)
    {
        if (searchResult == null)
        {
            this.searchResult = null;
        }
        else
        {
            this.searchResult = Lists.newArrayList(searchResult);
        }
    }

    public void setSearchString(String searchString)
    {
        this.searchString = searchString;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof SearchList)
        {
            final SearchList list = (SearchList)content;
            list.searchString = searchString;
            list.searchFilter = searchFilter;
            if (searchResult != null)
            {
                list.setSearchResult(Lists.<String> newArrayList());
                list.getSearchResult().addAll(this.searchResult);
            }
            list.removedMode = removedMode;
            list.full = full;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new SearchList();
    }
}
