package ru.naumen.metainfo.shared.ui;

import java.util.Comparator;
import java.util.function.Function;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElements;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.base.Preconditions;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.segment.MetainfoSegment;
import ru.naumen.metainfo.shared.segment.MetainfoSegmentNames.TopLevelSegments;
import ru.naumen.metainfo.shared.ui.customform.ChangeCaseForm;
import ru.naumen.metainfo.shared.ui.customform.MassEditForm;
import ru.naumen.metainfo.shared.ui.customform.QuickForm;

/**
 *
 * <AUTHOR>
 *
 */
@XmlRootElement
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "UIContainer", propOrder = { "fqn", "code", "content" })
public class UIContainer extends MetainfoSegment
{
    @XmlTransient
    public static final Comparator<UIContainer> EXPORT_COMPARATOR = new Comparator<UIContainer>()
    {
        @Override
        public int compare(UIContainer u1, UIContainer u2)
        {
            int i = ClassFqn.SIMPLE_COMPARATOR.compare(u1.getFqn(), u2.getFqn());
            return i != 0 ? i : order(u1.getCode()) - order(u2.getCode());
        }

        private int order(String code)
        {
            //@formatter:off
            return  UI.WINDOW_KEY.equals(code)                  ? 0 :
                    UI.Form.NEW.equals(code)                    ? 1 :
                    UI.Form.EDIT.equals(code)                   ? 2 :
                    UI.Form.CHANGE_CASE_FORM.equals(code)            ? 3 :
                    UI.Form.CHANGE_RESPONSIBLE_FORM.equals(code)     ? 4 :
                    5;
            //@formatter:on
        }
    };

    @XmlTransient
    public static final Function<UIContainer, ClassFqn> FQN_EXTRACTOR = input ->
    {
        Preconditions.checkNotNull(input);
        return input.getFqn();
    };

    private ClassFqn fqn;
    private String code;
    private Content content;

    public UIContainer()
    {
    }

    public UIContainer(ClassFqn fqn, String code, Content content)
    {
        this.fqn = fqn;
        this.code = code;
        this.content = content;
    }

    @XmlElement(required = true)
    public String getCode()
    {
        return code;
    }

    // @formatter:off
    @XmlElements({
        @XmlElement(name = "window", type = Window.class),
        @XmlElement(name = "form",   type = Form.class),
        @XmlElement(name = "changecaseform",   type = ChangeCaseForm.class),
        @XmlElement(name = "quickform",        type = QuickForm.class),
        @XmlElement(name = "masseditform",        type = MassEditForm.class),
        @XmlElement(name = "content")
    })
    // @formatter:on
    public Content getContent()
    {
        return content;
    }

    @XmlElement(required = true)
    public ClassFqn getFqn()
    {
        return fqn;
    }

    @Override
    public String getSegmentID()
    {
        return getFqn().toString() + '_' + getCode();
    }

    @Override
    public String getSegmentType()
    {
        return TopLevelSegments.UI_CONTAINER;
    }

    @Override
    public boolean isDetachableSegment()
    {
        return true;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setContent(Content content)
    {
        this.content = content;
    }

    public void setFqn(ClassFqn fqn)
    {
        this.fqn = fqn;
    }
}
