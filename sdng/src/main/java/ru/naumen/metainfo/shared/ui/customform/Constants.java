package ru.naumen.metainfo.shared.ui.customform;

import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Константы, относящиеся к настройке специальных форм {@link CustomForm}
 * 
 * <AUTHOR>
 * @since 22.04.16
 */
public class Constants
{
    public static class CustomUserForm
    {
        /**
         * Атрибуты настройки специальных форм
         */
        public static class Attributes
        {
            /**
             * Тип специальной пользовательской формы
             */
            public final static AttributeFqn FORM_TYPE = AttributeFqn.create(CustomUserForm.FQN,
                    CustomUserForm.FORM_TYPE);

            /**
             * Название пользовательской формы
             */
            public final static AttributeFqn TITLE = AttributeFqn.create(CustomUserForm.FQN, AbstractBO.TITLE);

            /**
             * Типы для которых настроен переход
             */
            public final static AttributeFqn TRANSITION_CASES = AttributeFqn.create(CustomUserForm.FQN,
                    CustomUserForm.TRANSITION_CASES);

            /**
             * Группа атрибутов
             */
            public final static AttributeFqn ATTRIBUTES_GROUP = AttributeFqn.create(CustomUserForm.FQN,
                    CustomUserForm.ATTRIBUTES_GROUP);

            /**
             * Комментарий на форме
             */
            public final static AttributeFqn COMMENT_ON_FORM = AttributeFqn.create(CustomUserForm.FQN,
                    CustomUserForm.COMMENT_ON_FORM);

        }

        public final static String CLASS_ID = "customForm";

        /**
         * Фейковый FQN
         */
        public final static ClassFqn FQN = ClassFqn.parse(CLASS_ID);

        /**
         * Коды полей
         */
        public final static String FORM_TYPE = "formType";
        public final static String ORIGINAL_CUSTOM_FORM = "original";
        public final static String TRANSITION_CASES = "transitionCases";
        public final static String ATTRIBUTES_GROUP = "attributesGroup";
        public final static String ATTRIBUTES_GROUP_TITLE = "attributesGroupTitle";
        public final static String COMMENT_ON_FORM = "commentOnForm";
        public final static String FORM_CLASS_FQN = "formClassFqn";
        public final static String ATTR_DESCRIPTION = "attrDescription";
        public final static String USE_AS_DEFAULT = "useAsDefault";

        /**
         * Заполнение атрибута "Комментарий на форме"
         */
        public final static String NOT_FILL = "notFill";
        public final static String FILL = "fill";
        public final static String MUST_FILL = "mustFill";
    }
}
