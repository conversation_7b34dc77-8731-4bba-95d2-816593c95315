package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.Objects;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Lists;

import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.AttrReference;

/**
 * Контент "Список файлов"
 * <AUTHOR>
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "FileList")
public class FileList extends ObjectListBase implements HasObjectActionsMenu
{
    /**
     * Тип связи, которую необходимо использовать для вывода списка файлов 
     */
    public interface RelationType
    {
        /**
         * Файлы текущего объекта
         */
        String OBJECT_FILES = "objectFiles";
        /**
         * Файлы связанного объекта
         */
        String RELATED_OBJECT_FILES = "relatedObjectFiles";
        /**
         * Файлы набора связанных объектов
         */
        String RELATED_OBJECT_SET_FILES = "relatedObjectSetFiles";
        /**
         * Файлы, ссылающиеся на объект 
         */
        String LINKED_WITH_OBJECT_FILES = "linkedWithObjectFiles";
        /**
         * Файлы, ссылающиеся на связанные объекты
         */
        String LINKED_WITH_RELATED_OBJECT_FILES = "linkedWithRelatedObjectFiles";
    }

    public static String OWN_OBJECT_FILES = "ownObjectFiles";

    /**
     * Используется для сохранения UUID объекта до подмены его на UUID связанного объекта
     */
    public static String INITIAL_CONTEXT_OBJECT_UUID = "initialSourceUuid";

    private String relationType = RelationType.OBJECT_FILES;

    /**
     * Коды атрибутов, файлы которых необходимо отобразить в списке
     */
    private ArrayList<String> attributeCodes;

    /**
     * Цепь ссылок на атрибуты, по которой нужно получить связанные файлы
     */
    private ArrayList<AttrReference> attrChain;

    private ObjectActionsMenuHolder objectsActionsMenuHolder;

    public FileList()
    {
        objectsActionsMenuHolder = new ObjectActionsMenuHolder();
    }

    @XmlElement
    public ArrayList<AttrReference> getAttrChain()
    {
        if (null == attrChain)
        {
            attrChain = Lists.newArrayList();
        }
        return attrChain;
    }

    @XmlElement
    public ArrayList<String> getAttributeCodes()
    {
        if (null == attributeCodes)
        {
            attributeCodes = Lists.newArrayList(OWN_OBJECT_FILES);
        }
        return attributeCodes;
    }

    @XmlElement
    @Override
    @Deprecated
    public String getMenuIconCatalogCode()
    {
        return null;
    }

    @XmlElement
    @Override
    public ObjectActionsMenuHolder getObjectActionsMenu()
    {
        return objectsActionsMenuHolder;
    }

    @XmlElement
    @Override
    @Deprecated
    public String getObjectActionsMenuPosition()
    {
        return null;
    }

    @XmlElement
    @Override
    @Deprecated
    public ToolPanel getObjectActionsToolPanel()
    {
        return null;
    }

    @XmlElement(name = "relationType")
    public String getRelationType()
    {
        if (null == relationType)
        {
            relationType = RelationType.OBJECT_FILES;
        }
        return relationType;
    }

    @Override
    public int integrityHashCode()
    {
        return Objects.hash(super.integrityHashCode(), getRelationType(), getAttrChain(), getAttributeCodes());
    }

    public void setAttributeCodes(ArrayList<String> attributeCodes)
    {
        this.attributeCodes = attributeCodes;
    }

    @Override
    @Deprecated
    public void setMenuIconCatalogCode(String menuIconCatalogCode)
    {
        objectsActionsMenuHolder.setMenuIconCatalogCode(menuIconCatalogCode);
    }

    @Override
    public void setObjectActionsMenu(ObjectActionsMenuHolder menu)
    {
        this.objectsActionsMenuHolder = menu;
    }

    @Deprecated
    @Override
    public void setObjectActionsMenuPosition(String objectActionsMenuPosition)
    {
        objectsActionsMenuHolder.setObjectActionsMenuPosition(objectActionsMenuPosition);
    }

    @Deprecated
    @Override
    public void setObjectActionsToolPanel(ToolPanel objectActionsToolPanel)
    {
        objectsActionsMenuHolder.setObjectActionsToolPanel(objectActionsToolPanel);
    }

    public void setRelationType(String relationType)
    {
        this.relationType = relationType;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);
        if (content instanceof HasObjectActionsMenu)
        {
            objectsActionsMenuHolder.fillContent(content);
        }

        if (content instanceof FileList)
        {
            FileList fileList = (FileList)content;
            fileList.relationType = relationType;
            ObjectUtils.cloneCollection(attrChain, fileList.attrChain = new ArrayList<>());
            fileList.attributeCodes = attributeCodes != null ? new ArrayList<>(attributeCodes) : null;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new FileList();
    }
}
