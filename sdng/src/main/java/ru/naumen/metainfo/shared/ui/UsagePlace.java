package ru.naumen.metainfo.shared.ui;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Set;

import jakarta.annotation.Nullable;

import com.google.common.collect.Sets;
import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.core.shared.HasClone;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.TitledClassFqn;

/**
 * Описывает место использования элемента в настройке интерфейса.
 * <AUTHOR>
 * @since 23 июня 2015 г.
 */
public class UsagePlace implements Serializable, IsSerializable, HasClone
{
    private static final long serialVersionUID = -7946625759475509519L;

    private TitledClassFqn clazz;
    private TitledClassFqn caze;
    //Последовательность вкладок и списков объектов от корня
    //иерархии контентов до контрола, возбуждающего пользовательское событие
    //включительно
    private ArrayList<Content> contentChain;
    private ActionTool actionTool;
    private String holderCode;
    private Set<ClassFqn> holderFqns;
    private String templateTitle;

    public UsagePlace()
    {
    }

    public UsagePlace(TitledClassFqn clazz, @Nullable TitledClassFqn caze, ActionTool actionTool, String holderCode,
            Set<ClassFqn> holderFqns)
    {
        this.clazz = clazz;
        this.caze = caze;
        this.actionTool = actionTool;
        this.holderFqns = holderFqns;
        this.contentChain = new ArrayList<>();
        this.holderCode = holderCode;
    }

    public UsagePlace(TitledClassFqn clazz, @Nullable TitledClassFqn caze, ArrayList<Content> contentChain)
    {
        this.clazz = clazz;
        this.caze = caze;
        this.contentChain = contentChain;
    }

    @Override
    public UsagePlace clone()
    {
        UsagePlace clone = new UsagePlace();
        clone.clazz = clazz;
        clone.caze = caze;
        clone.holderCode = holderCode;
        clone.templateTitle = templateTitle;
        if (actionTool != null)
        {
            clone.actionTool = (ActionTool)actionTool.clone();
        }
        ObjectUtils.cloneCollection(contentChain, clone.contentChain = new ArrayList<>());
        ObjectUtils.cloneCollection(holderFqns, clone.holderFqns = Sets.newHashSet());
        return clone;

    }

    public ActionTool getActionTool()
    {
        if (contentChain.isEmpty() || !(contentChain.get(contentChain.size() - 1) instanceof ActionTool))
        {
            return this.actionTool;
        }

        return (ActionTool)contentChain.get(contentChain.size() - 1);
    }

    public TitledClassFqn getCaze()
    {
        return caze;
    }

    public TitledClassFqn getClazz()
    {
        return clazz;
    }

    public String getTemplateTitle()
    {
        return templateTitle;
    }

    /**
     * Возвращает контент в который добавлен контрол или null 
     * в случае если контрол добавлен непосредственно на карточку
     * 
     * @return
     */
    public Content getContent()
    {
        if (contentChain.size() < 2)
        {
            return null;
        }
        return contentChain.get(contentChain.size() - 2);
    }

    public ArrayList<Content> getContentChain()
    {
        return contentChain;
    }

    public String getHolderCode()
    {
        return this.holderCode;
    }

    public Set<ClassFqn> getHolderFqns()
    {
        return holderFqns;
    }

    public void setTemplateTitle(String templateTitle)
    {
        this.templateTitle = templateTitle;
    }

    public void setHolderCode(String holderCode)
    {
        this.holderCode = holderCode;
    }
}
