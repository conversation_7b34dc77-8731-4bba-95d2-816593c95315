package ru.naumen.metainfo.shared.ui.customform;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * <AUTHOR>
 * @since 22.04.2016
 *
 */
@SuppressWarnings({ "serial" })
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ChangeCaseForm")
public class ChangeCaseForm extends CustomFormWithCommentBase
{
    public ChangeCaseForm()
    {
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (!super.equals(obj))
        {
            return false;
        }
        if (getClass() != obj.getClass())
        {
            return false;
        }

        ChangeCaseForm other = (ChangeCaseForm)obj;
        if (!ObjectUtils.equals(commentOnFormProperty, other.commentOnFormProperty))
        {
            return false;
        }
        if (!ObjectUtils.equals(commentOnFormAttrGroupCode, other.commentOnFormAttrGroupCode))
        {
            return false;
        }
        if (showAttrDescription != other.showAttrDescription)
        {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((commentOnFormProperty == null) ? 0 : commentOnFormProperty.hashCode())
                + ((commentOnFormAttrGroupCode == null) ? 0 : commentOnFormAttrGroupCode.hashCode());
        return result;
    }

    @Override
    public String toString()
    {
        return "ChangeCaseForm with " + getAttrGroup();
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof ChangeCaseForm)
        {
            ChangeCaseForm form = (ChangeCaseForm)content;
            form.commentOnFormProperty = commentOnFormProperty;
            form.showAttrDescription = showAttrDescription;
            form.commentOnFormAttrGroupCode = commentOnFormAttrGroupCode;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ChangeCaseForm();
    }
}
