package ru.naumen.metainfo.shared.ui;

import java.util.ArrayDeque;
import java.util.Deque;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Контент "Выбор контрагента"
 * 
 * @see https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00530
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "SelectClient")
public class SelectClient extends EditablePropertiesContentBase implements DtoObjectsWithoutLinkAttrsOwner
{
    private static final long serialVersionUID = -1437351184329495726L;

    public static boolean hasSelectClientContent(Content content, boolean checkVisibility)
    {
        Deque<Content> stack = new ArrayDeque<>();
        stack.add(content);

        while (!stack.isEmpty())
        {
            Content current = stack.pop();
            for (Content child : current.getChilds())
            {
                if ((!checkVisibility || child.isVisible())
                        && child instanceof SelectClient)
                {
                    return true;
                }
                stack.add(child);
            }
        }
        return false;
    }

    @Override
    protected Content newInstance()
    {
        return new SelectClient();
    }
}
