package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.base.Predicates;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;

import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.segment.MetainfoSegmentNames.ContentSegments;

/**
 * Панель действий (action bar)
 *
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ToolPanel")
public class ToolPanel extends Content
{
    private boolean useSystemSettings;

    private boolean panelDisabled;

    private ArrayList<ToolBar> toolBars;

    private String code;
    /**
     * Флаг необходимый при загрузке/выгрузки метаинформации.
     * Флаг обозначает, был ли выбран toolBar для выгрузки
     */
    private boolean ignoreInImport = false;

    /**
     * Показывать названия инструментов(кнопок)
     */
    private boolean showToolCaptions = true;

    public ToolPanel()
    {
        this(null);
    }

    public ToolPanel(Content parent)
    {
        setParent(parent);
        setUuid(UUIDGenerator.get().nextUUID());
    }

    @Override
    @XmlTransient
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public Collection<Content> getChilds()
    {
        return (Collection)Collections2.filter(getToolBars(), Predicates.notNull());
    }

    @XmlTransient
    public String getCode()
    {
        return code;
    }

    public Tool getTool(String code)
    {
        for (Tool tool : getTools())
        {
            if (tool instanceof HasCode && ObjectUtils.equals(code, ((HasCode)tool).getCode()))
            {
                return tool;
            }
        }
        return null;
    }

    /**
     * @return список тулбаров расположенных на панели
     */
    @XmlElement
    public List<ToolBar> getToolBars()
    {
        if (toolBars == null)
        {
            toolBars = new ArrayList<ToolBar>();
        }
        return this.toolBars;
    }

    /**
     * @return список действий, расположенных на панели
     */
    public List<Tool> getTools()
    {
        List<Tool> result = Lists.newArrayList();
        for (ToolBar toolBar : getToolBars())
        {
            result.addAll(toolBar.getTools());
        }
        return result;
    }

    @XmlElement
    public boolean isIgnoreInImport()
    {
        return ignoreInImport;
    }

    @XmlElement
    public boolean isPanelDisabled()
    {
        return panelDisabled;
    }

    @Deprecated // Дублирует официальный функционал через интерфейс
    @XmlElement
    public boolean isShowToolCaptions()
    {
        return showToolCaptions;
    }

    @XmlElement
    public boolean isUseSystemSettings()
    {
        return useSystemSettings;
    }

    @Override
    public void removeChild(Content content)
    {
        getToolBars().remove(content);
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setIgnoreInImport(boolean ignoreInImport)
    {
        this.ignoreInImport = ignoreInImport;
    }

    public void setPanelDisabled(boolean panelDisabled)
    {
        this.panelDisabled = panelDisabled;
    }

    @Deprecated
    public void setShowToolCaptions(boolean showToolCaptions)
    {
        this.showToolCaptions = showToolCaptions;
    }

    public void setUseSystemSettings(boolean useSystemSettings)
    {
        this.useSystemSettings = useSystemSettings;
    }

    public void setToolBars(List<ToolBar> toolBars)
    {
        this.toolBars = new ArrayList<>(toolBars);
        this.toolBars.forEach(toolBar -> toolBar.setParent(this));
    }

    public void addToolBar(ToolBar toolBar)
    {
        getToolBars().add(toolBar);
        toolBar.setParent(this);
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof ToolPanel)
        {
            ToolPanel toolPanel = (ToolPanel)content;
            toolPanel.useSystemSettings = useSystemSettings;
            toolPanel.panelDisabled = panelDisabled;
            toolPanel.code = code;
            toolPanel.ignoreInImport = ignoreInImport;
            toolPanel.showToolCaptions = showToolCaptions;
            ObjectUtils.cloneCollection(toolBars, toolPanel.toolBars = new ArrayList<>());
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ToolPanel();
    }

    @Override
    public String getSegmentType()
    {
        return ContentSegments.TOOL_PANELS;
    }

    @Override
    public String getSegmentID()
    {
        return getUuid();
    }

    @Override
    public boolean isDetachableSegment()
    {
        return true;
    }
}
