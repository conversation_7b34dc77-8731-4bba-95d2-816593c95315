package ru.naumen.metainfo.shared.ui;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Список событий связанных с объектом
 * 
 * <AUTHOR>
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "EventList")
public class EventList extends ObjectListBase
{
    @Override
    protected Content newInstance()
    {
        return new EventList();
    }
}
