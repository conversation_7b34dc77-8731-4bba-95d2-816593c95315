package ru.naumen.metainfo.shared.ui;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.Unmarshaller;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.core.shared.HasClone;
import ru.naumen.core.shared.ui.UIComponent;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.metainfo.shared.mobile.contents.MobileListsGroupContent;
import ru.naumen.metainfo.shared.segment.MetainfoSegment;
import ru.naumen.metainfo.shared.sets.HasSettingsSet;
import ru.naumen.metainfo.shared.tags.HasTags;

/**
 * Базовый класс для всевозможных контентов используемых в настраиваемом пользовательском
 * интерфейсе
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "Content")
@XmlSeeAlso({ Layout.class, Form.class, Window.class, FlowContent.class, ActionBar.class, ActionBarElement.class,
        Tab.class, ToolBar.class, ToolPanel.class, CommonMobileView.class })
public abstract class Content extends MetainfoSegment implements IsSerializable, Serializable, UIComponent, HasClone,
        HasTags, HasSettingsSet
{
    public static final Function<Content, String> UUID_EXTRACTOR = input -> input != null ? input.getUuid() : null;
    private static final long serialVersionUID = -5517256250601206598L;
    public static Function<Content, String> toUuid()
    {
        return UUID_EXTRACTOR;
    }

    private String uuid;
    private Content parent;
    private List<String> profiles;
    private List<String> versProfiles;
    private List<String> tags;

    /**F
     * Комплект настроек
     */
    private String settingsSet;
    private boolean visible = true;

    private boolean enabled = true;

    public Content()
    {
    }

    /**
     * Конструктор предназначен для динамического создания контентов
     * с указанием родительского контента
     * @param parent родитель
     */
    public Content(@Nullable Content parent)
    {
        this.parent = parent;
    }

    public Content(String uuid)
    {
        this.uuid = uuid;
    }

    public void afterUnmarshal(Unmarshaller u, Object parent)
    {
        this.parent = parent instanceof Content ? (Content)parent : null;
    }

    public void clearChilds()
    {
        List<Content> childs = new ArrayList<>(getChilds());
        for (Content child : childs)
        {
            removeChild(child);
        }
    }

    /**
     * Делаем клонирование контентов через java код, так как он будет работать быстрее,
     * чем рефлексия или сериализация/десериализация. Нам нужна максимальная производительность.
     */
    @Override
    public Content clone()
    {
        Content copy = newInstance();
        fillContent(copy);

        for (Content child : copy.getChilds())
        {
            child.setParent(copy);
            if (child instanceof MobileListsGroupContent)
            {
                ((MobileListsGroupContent)child).getListContents().forEach(content -> content.setParent(child));
            }
        }
        return copy;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (obj instanceof Content)
        {
            return ObjectUtils.equals(uuid, ((Content)obj).getUuid());
        }
        return false;
    }

    public List<LocalizedString> getCaption()
    {
        return Collections.emptyList();
    }

    /**
     * @return коллекцию вложенных контентов
     */
    @Override
    @XmlTransient
    public abstract Collection<Content> getChilds();

    /**
     * Идентификаця элементов интерфейса.<br>
     * Для потомков, которым нужен id.
     * @return debugId
     */
    public String getDebugId()
    {
        //возвращаем по умолчанию null, чтобы не выставлять лишних id.
        return null;
    }

    /**
     * @return родительский контент
     */
    @XmlTransient
    @Override
    public Content getParent()
    {
        return this.parent;
    }

    @XmlElementWrapper(name = "profiles")
    @XmlElement(name = "profile")
    public List<String> getProfiles()
    {
        if (null == profiles)
        {
            profiles = Lists.newArrayList();
        }
        return profiles;
    }

    @XmlElementWrapper(name = "versProfiles")
    @XmlElement(name = "verProfile")
    public List<String> getVersProfiles()
    {
        if (null == versProfiles)
        {
            versProfiles = Lists.newArrayList();
        }
        return versProfiles;
    }

    @XmlElementWrapper(name = "tags")
    @XmlElement(name = "tag")
    @Override
    public List<String> getTags()
    {
        if (null == tags)
        {
            tags = new ArrayList<>();
        }
        return tags;
    }
    @XmlElement(name = "set")
    @Override
    public @Nullable String getSettingsSet()
    {
        return settingsSet;
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet)
    {
        this.settingsSet = settingsSet;
    }

    @Override
    @XmlAttribute(name = "uuid")
    public String getUuid()
    {
        return uuid;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(uuid);
    }

    @XmlTransient
    public boolean isEnabled()
    {
        return enabled;
    }

    @XmlTransient
    public boolean isVisible()
    {
        return visible;
    }

    /**
     * Если один из родителей контента в иерархии не виден,
     * значит не виден и он сам.
     */
    public boolean isVisibleRecursive()
    {
        Content c = this;
        while (c != null && c.isVisible())
        {
            c = c.getParent();
        }
        return c == null;
    }

    public abstract void removeChild(Content content);

    public boolean removeFromParent()
    {
        if (parent != null)
        {
            parent.removeChild(this);
            return true;
        }
        return false;
    }

    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }

    public void setParent(@Nullable Content parent)
    {
        this.parent = parent;
    }

    public void setProfiles(Collection<String> profiles)
    {
        this.profiles = Lists.newArrayList(profiles);
    }

    public void setVersProfiles(Collection<String> versProfiles)
    {
        this.versProfiles = Lists.newArrayList(versProfiles);
    }

    public void setTags(Collection<String> tags)
    {
        this.tags = Lists.newArrayList(tags);
    }

    public void setUuid(String uuid)
    {
        this.uuid = uuid;
    }

    public void setVisible(boolean visible)
    {
        this.visible = visible;
    }

    protected <T> T cloneIfNotNull(@Nullable HasClone clonable)
    {
        return clonable == null ? null : (T)clonable.clone();
    }

    protected void fillContent(Content content)
    {
        content.profiles = profiles != null ? new ArrayList<>(profiles) : null;
        content.versProfiles = versProfiles != null ? new ArrayList<>(versProfiles) : null;
        content.tags = tags != null ? new ArrayList<>(tags) : null;
        content.uuid = uuid;
        content.visible = visible;
        content.enabled = enabled;
        content.parent = parent;
        content.settingsSet = settingsSet;
    }

    /**
     * Утилитарный метод для заполнения parent
     * Вынесен, чтобы не дублировать проверку на null
     * @param content - контент, в который нужно установить parent
     * @param parent - parent
     */
    protected static void fillParent(@Nullable Content content, Content parent)
    {
        if (content != null)
        {
            content.setParent(parent);
        }
    }

    protected abstract Content newInstance();

    @Override
    public String getSegmentID()
    {
        return null;
    }

    @Override
    public String getSegmentType()
    {
        return null;
    }

    @Override
    public boolean isDetachableSegment()
    {
        return false;
    }
}
