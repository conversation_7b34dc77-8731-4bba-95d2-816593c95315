package ru.naumen.metainfo.shared.ui.customform;

import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.IHasI18nTitle;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Форма быстрого добавления и редактирования.
 * <AUTHOR>
 * @since Dec 20, 2017
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "QuickForm")
public class QuickForm extends CustomFormBase implements IHasI18nTitle, HasCode
{
    private static final long serialVersionUID = 1L;

    public static final String UUID_PREFIX = Form.QUICK_ADD_AND_EDIT_FORM + '$';

    private boolean immediateObjectSavingEnabled = false;

    public QuickForm()
    {
        setUuid(UUID_PREFIX + UUIDGenerator.get().nextUUID());
    }

    @Override
    public String getCode()
    {
        return getUuid().startsWith(UUID_PREFIX) ? getUuid().substring(UUID_PREFIX.length()) : getUuid();
    }

    @XmlTransient
    @Override
    public List<LocalizedString> getTitle()
    {
        return getCaption();
    }

    @XmlElement(name = "immediate-object-saving-enabled")
    public boolean isImmediateObjectSavingEnabled()
    {
        return immediateObjectSavingEnabled;
    }

    public void setCode(String code)
    {
        setUuid(UUID_PREFIX + code);
    }

    public void setImmediateObjectSavingEnabled(boolean immediateObjectSavingEnabled)
    {
        this.immediateObjectSavingEnabled = immediateObjectSavingEnabled;
    }

    @Override
    public String toString()
    {
        return "QuickAddAndEditForm for " + getAttrGroup();
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof QuickForm)
        {
            QuickForm form = (QuickForm)content;           
            form.showAttrDescription = showAttrDescription;
            form.immediateObjectSavingEnabled = immediateObjectSavingEnabled;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new QuickForm();
    }
}
