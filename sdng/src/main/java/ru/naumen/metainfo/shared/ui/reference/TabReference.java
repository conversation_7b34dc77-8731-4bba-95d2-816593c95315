package ru.naumen.metainfo.shared.ui.reference;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElements;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Ссылка на вкладку в шаблоне карточки.
 * <AUTHOR>
 * @since Jul 05, 2021
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "TabReference")
public class TabReference extends Content implements UIComponentReference, HasContentReferences
{
    private List<LocalizedString> caption;
    private Layout layout;
    private List<ContentReference> contentReferences;

    public TabReference()
    {
        setUuid(UUIDGenerator.get().nextUUID());
    }

    public TabReference(String uuid)
    {
        setUuid(uuid);
    }

    @Override
    @XmlElement
    public List<LocalizedString> getCaption()
    {
        if (null == caption)
        {
            caption = new ArrayList<>();
        }
        return caption;
    }

    @Override
    @XmlTransient
    public Collection<Content> getChilds()
    {
        List<Content> children = new ArrayList<>();
        if (null != layout)
        {
            children.add(layout);
        }
        getContentReferences().stream().filter(Objects::nonNull).forEach(children::add);
        return children;
    }

    @XmlElements({
            @XmlElement(name = "contentReference", type = ContentReference.class),
            @XmlElement(name = "tabBarReference", type = TabBarReference.class)
    })

    @Override
    public List<ContentReference> getContentReferences()
    {
        if (null == contentReferences)
        {
            contentReferences = new ArrayList<>();
        }
        return contentReferences;
    }

    @Nullable
    @XmlElement
    public Layout getLayout()
    {
        return layout;
    }

    @Override
    public List<String> getProfiles()
    {
        return null;
    }

    @Override
    public List<String> getTags()
    {
        return null;
    }

    @Override
    public List<String> getVersProfiles()
    {
        return null;
    }

    @Override
    public void removeChild(Content content)
    {
        if (content == layout)
        {
            layout = null;
        }
        else if (null != contentReferences && content instanceof ContentReference)
        {
            contentReferences.remove(content);
        }
    }

    public void setLayout(@Nullable Layout layout)
    {
        this.layout = layout;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof TabReference)
        {
            TabReference tab = (TabReference) content;
            tab.layout = ObjectUtils.clone(layout);
            ObjectUtils.cloneCollection(caption, tab.getCaption());
            ObjectUtils.cloneCollection(contentReferences, tab.getContentReferences());
        }
    }

    @Override
    protected Content newInstance()
    {
        return new TabReference();
    }
}
