package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Lists;

import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.IHasI18nTitle;
import ru.naumen.metainfo.shared.segment.MetainfoSegmentNames.ContentSegments;

/**
 * Инструмент {@link ToolBar панели инструментов} реализующего действия на основе {@link ActionHandler}.
 */
@SuppressWarnings("serial")
@XmlType(name = "ActionTool")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class ActionTool extends Tool implements HasCode, IHasI18nTitle
{
    private String action;
    private ArrayList<LocalizedString> caption;

    /**
     * признак необходимости блокировать display при start/stop
     */
    private boolean blockable = true;
    private boolean allowedInMassOperations = true;
    private String invocationMethodCode;

    public ActionTool()
    {
    }

    public ActionTool(@Nullable ToolBar toolBar)
    {
        super(toolBar);
    }

    @XmlElement
    public String getAction()
    {
        return action;
    }

    @Override
    @XmlElement(required = true)
    public List<LocalizedString> getCaption()
    {
        if (null == caption)
        {
            caption = Lists.newArrayList();
        }
        return caption;
    }

    @Override
    public String getCode()
    {
        return action;
    }

    @Override
    public String getDebugId()
    {
        return action;
    }

    @Nullable
    @XmlElement(name = "invoke-method")
    public String getInvocationMethodCode()
    {
        return invocationMethodCode;
    }

    @Override
    public List<LocalizedString> getTitle()
    {
        if (caption == null)
        {
            caption = Lists.newArrayList();
        }
        return caption;
    }

    @XmlElement
    public boolean isAllowedInMassOperations()
    {
        return allowedInMassOperations;
    }

    @XmlElement
    public boolean isBlockable()
    {
        return blockable;
    }

    public void setAction(String action)
    {
        this.action = action;
    }

    public void setAllowedInMassOperations(boolean allowedInMassOperations)
    {
        this.allowedInMassOperations = allowedInMassOperations;
    }

    public void setBlockable(boolean blockable)
    {
        this.blockable = blockable;
    }

    public void setInvocationMethodCode(@Nullable String invocationMethodCode)
    {
        this.invocationMethodCode = invocationMethodCode;
    }

    @Override
    public String toString()
    {
        return "ActionTool [action=" + action + "]";
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);
        ActionTool actionTool = (ActionTool)content;
        actionTool.setAction(getAction());
        actionTool.setBlockable(isBlockable());
        actionTool.setAllowedInMassOperations(isAllowedInMassOperations());
        actionTool.setInvocationMethodCode(getInvocationMethodCode());
        for (LocalizedString str : getCaption())
        {
            actionTool.getCaption().add(new LocalizedString(str.getLang(), str.getValue()));
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ActionTool();
    }

    @Override
    public String getSegmentID()
    {
        return getAction() + '_' + getUuid();
    }

    @Override
    public String getSegmentType()
    {
        return ContentSegments.TOOLS;
    }

    @Override
    public boolean isDetachableSegment()
    {
        return true;
    }
}
