package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElements;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.base.Predicates;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;

import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * "Сетка" на которой может размещаться контент. Каждый контент может быть размещен либо на всю
 *	ширину, либо в правои или левой колнке (всего две колонки)
 *
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "Layout")
public class Layout extends SystemContentBase
{
    public static final String LAYOUT_CONTENT_DEBUG_PREFIX = "LayoutContent";

    protected ArrayList<FlowContent> content;
    protected boolean isWindowLayout;
    protected boolean isFirstTab = false;
    protected int leftColumnWidth;

    @Override
    @XmlTransient
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public Collection<Content> getChilds()
    {
        return (Collection)Collections2.filter(getContent(), Predicates.notNull());
    }

    /**
     * @return список контентов расположенных на форме
     */
    // @formatter:off
    @XmlElements({
        @XmlElement(name = "child-objects",             type = ChildObjectList.class),
        @XmlElement(name = "comments",                  type = CommentList.class),
        @XmlElement(name = "events",                    type = EventList.class),
        @XmlElement(name = "files",                     type = FileList.class),
        @XmlElement(name = "user-history",              type = UserHistoryList.class),
        @XmlElement(name = "objects",                   type = ObjectList.class),
        @XmlElement(name = "related-objects",           type = RelObjectList.class),
        @XmlElement(name = "search-list",               type = SearchList.class),
        @XmlElement(name = "editable-properties",       type = EditablePropertyList.class),
        @XmlElement(name = "properties",                type = PropertyList.class),
        @XmlElement(name = "related-object-properties", type = RelObjPropertyList.class),
        @XmlElement(name = "tabbar",                    type = TabBar.class),
        @XmlElement(name = "sc-client-info",            type = ClientInfo.class),
        @XmlElement(name = "sc-select-client",          type = SelectClient.class),
        @XmlElement(name = "sc-select-contract",        type = SelectContacts.class),
        @XmlElement(name = "sc-select-case",            type = SelectScCase.class),
        @XmlElement(name = "select-case",               type = SelectCase.class),
        @XmlElement(name = "mass-problems",             type = MassProblems.class),
        @XmlElement(name = "report",                    type = ReportContent.class),
        @XmlElement(name = "reports-list",              type = ReportsList.class),
        @XmlElement(name = "select-parent",             type = SelectParent.class),
        @XmlElement(name = "workflow-content",          type = WorkflowContent.class),
        @XmlElement(name = "network-scheme",            type = NetworkScheme.class),
        @XmlElement(name = "relation-scheme",           type = RelationScheme.class),
        @XmlElement(name = "embedded-application-content",     type = EmbeddedApplicationContent.class),
        @XmlElement(name = "hierarchy-grid",            type = HierarchyGrid.class),
        @XmlElement(name = "NDAPTriggerHistory",        type = NDAPTriggerHistory.class),
        @XmlElement(name = "content")
    })
    // @formatter:on
    public List<FlowContent> getContent()
    {
        if (content == null)
        {
            content = Lists.newArrayList();
        }
        return this.content;
    }

    /**      
     * @return ширина левой колонки в процентах от ширины доступной области для Layout
     */
    @XmlElement
    public int getLeftColumnWidth()
    {
        if (this.leftColumnWidth == 0)
        {
            this.leftColumnWidth = 60;
        }
        return this.leftColumnWidth;
    }

    @XmlTransient
    public boolean isWindowLayout()
    {
        return isWindowLayout;
    }

    @XmlTransient
    public boolean isFirstTab()
    {
        return isFirstTab;
    }

    @Override
    public void removeChild(Content content)
    {
        getContent().remove(content);
    }

    public void setContent(int index, FlowContent content)
    {
        getContent().set(index, content);
    }

    public void setLeftColumnWidth(int leftColumnWidth)
    {
        this.leftColumnWidth = leftColumnWidth;
    }

    public void setWindowLayout(boolean isWindowLayout)
    {
        this.isWindowLayout = isWindowLayout;
    }

    public void setFirstTab(boolean isFirstTab)
    {
        this.isFirstTab = isFirstTab;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof Layout)
        {
            Layout layout = (Layout)content;
            layout.isWindowLayout = isWindowLayout;
            layout.leftColumnWidth = leftColumnWidth;
            ObjectUtils.cloneCollection(this.content, layout.content = new ArrayList<>());
        }

    }

    @Override
    protected Content newInstance()
    {
        return new Layout();
    }
}
