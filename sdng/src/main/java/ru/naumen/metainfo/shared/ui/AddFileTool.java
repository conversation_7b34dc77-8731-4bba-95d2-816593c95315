package ru.naumen.metainfo.shared.ui;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.ui.toolbar.SystemCatalogIconCodes;
import ru.naumen.metainfo.shared.Constants.AttrGroup;

/**
 * Инструмент для добавления файла
 * с вожможностью указать куда именно будет добавлен файл (например, в определенный атрибут типа Файл)
 * 
 * <AUTHOR>
 * @since 08.04.1019
 */
@SuppressWarnings("serial")
@XmlType(name = "AddFileTool")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class AddFileTool extends ActionTool
{
    public static AddFileTool fromActionTool(ActionTool tool)
    {
        AddFileTool actionTool = new AddFileTool();
        tool.fillContent(actionTool);
        return actionTool;
    }

    private String attributeToSaveFile = FileList.OWN_OBJECT_FILES;

    private String attributeGroupForAddFile = AttrGroup.ADD_FORM;

    public AddFileTool()
    {
        setAction(Constants.ADD_FILE);
        setIconCode(SystemCatalogIconCodes.ADD_ICON);
    }

    public AddFileTool(@Nullable ToolBar toolBar)
    {
        super(toolBar);
        setAction(Constants.ADD_FILE);
    }

    @XmlElement
    public String getAttributeGroupForAddFile()
    {
        return attributeGroupForAddFile;
    }

    @Nullable
    @XmlElement
    public String getAttributeToSaveFile()
    {
        return attributeToSaveFile;
    }

    @Override
    public String getDebugId()
    {
        return Constants.ADD_FILE;
    }

    @Override
    public AddFileTool newInstance()
    {
        return new AddFileTool();
    }

    public void setAttributeGroupForAddFile(String attributeGroupForAddFile)
    {
        this.attributeGroupForAddFile = attributeGroupForAddFile;
    }

    public void setAttributeToSaveFile(@Nullable String attributeToSaveFile)
    {
        this.attributeToSaveFile = attributeToSaveFile;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);
        AddFileTool actionTool = (AddFileTool)content;
        actionTool.attributeGroupForAddFile = getAttributeGroupForAddFile();
        actionTool.attributeToSaveFile = getAttributeToSaveFile();
    }
}
