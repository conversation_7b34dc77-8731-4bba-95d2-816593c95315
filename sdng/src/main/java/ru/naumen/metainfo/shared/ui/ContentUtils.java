package ru.naumen.metainfo.shared.ui;

import java.util.ArrayDeque;
import java.util.Collection;
import java.util.Deque;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Predicate;

import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.SourceCode;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.CommentList.SubjectType;
import ru.naumen.metainfo.shared.ui.FileList.RelationType;

/**
 * Вспомогательные методы для работы с контентами
 *
 * <AUTHOR>
 * @since 27.01.2020
 */
public final class ContentUtils
{
    private ContentUtils()
    {
    }

    /**
     * Найти контент по его UUID-у
     * @param rootContent контент-родитель
     * @param contentUuid UUID искомого контента
     * @return найденный контент или null, если контент не найден
     */
    @Nullable
    public static Content findContentByUUID(Content rootContent, @Nullable String contentUuid)
    {
        if (contentUuid == null)
        {
            return null;
        }
        return findContentByCondition(rootContent,
                content -> ObjectUtils.equalsIgnoreCase(content.getUuid(), contentUuid));
    }

    /**
     * Найти контент
     * @param rootContent контент-родитель
     * @param condition предикат - условие поиска контента
     * @return найденный контент или null, если контент не найден
     */
    @Nullable
    public static Content findContentByCondition(Content rootContent, Predicate<Content> condition)
    {
        Queue<Content> queue = new LinkedList<>();
        queue.add(rootContent);

        while (!queue.isEmpty())
        {
            Content content = queue.poll();
            if (condition.test(content))
            {
                return content;
            }
            queue.addAll(content.getChilds());
        }
        return null;
    }

    /**
     * Выполняет указанное действие для переданного контента и его дочерних элементов (рекурсивно).
     * @param root корневой контент
     * @param action выполняемое действие
     */
    public static void forEachContent(Content root, Consumer<Content> action)
    {
        forEachContent(root, action, null);
    }

    /**
     * Выполняет указанное действие для переданного контента и его дочерних элементов (рекурсивно).
     * @param root корневой контент
     * @param action выполняемое действие
     * @param condition условие выполнения действия, распространяемое и на дочерние контенты, может быть
     * <code>null</code>
     */
    public static void forEachContent(Content root, Consumer<Content> action, @Nullable Predicate<Content> condition)
    {
        Queue<Content> queue = new LinkedList<>();
        queue.add(root);
        while (!queue.isEmpty())
        {
            Content content = queue.poll();
            if (null != condition && !condition.test(content))
            {
                continue;
            }
            action.accept(content);
            queue.addAll(content.getChilds());
        }
    }

    /**
     * Удаляет контенты, построенные на связанных объектах для указанных атрибутов
     *
     * @param layout сетка, в которой размещены контенты
     * @param attrChain цепочка атрибутов, на которую настроен контент
     * @param contentToRemove контент, который будет удален
     * @param attributes атрибуты, при наличии которых в цепочке, удаляется контент
     */
    public static void removeAttrChainBasedContents(Layout layout, List<AttrReference> attrChain,
            Content contentToRemove, Collection<String> attributes)
    {
        for (AttrReference ar : attrChain)
        {
            if (attributes.contains(ar.getAttrCode()))
            {
                layout.getContent().remove(contentToRemove);
            }
        }
    }

    /**
     * Удаляет все контенты, настроенные на группу атрибутов с определенным кодом
     *
     * @param window контейнер с контентами, в котором производится удаление
     * @param groupCode код группы атрибутов, контенты для которой будут удалены
     */
    public static void deleteContentsByAttrGroup(UIContainer window, String groupCode)
    {
        Deque<Content> contents = new ArrayDeque<>();
        contents.add(window.getContent());
        while (!contents.isEmpty())
        {
            Content content = contents.poll();
            contents.addAll(content.getChilds());
            if (content instanceof Layout)
            {
                Collection<Content> internalContents = Lists.newArrayList(content.getChilds());
                internalContents.stream()
                        .filter(c -> c instanceof PropertyListBase
                                && groupCode.equals(((PropertyListBase)c).getAttributeGroup())
                                && !(c instanceof RelObjPropertyList))
                        .forEach(c -> ((Layout)content).getContent().remove(c));
            }
        }
    }

    /**
     * Является ли список файлами связанного объекта
     * @param objectList список объектов
     */
    public static boolean isRelatedFileList(ObjectListBase objectList)
    {
        return objectList instanceof FileList && ((FileList)objectList).getRelationType()
                .equals(RelationType.RELATED_OBJECT_FILES);
    }

    /**
     * Является ли список комментариями связанного объекта
     * @param objectList список объектов
     */
    public static boolean isRelatedCommentList(ObjectListBase objectList)
    {
        return objectList instanceof CommentList && ((CommentList)objectList).getSubjectType()
                .equals(SubjectType.RELATED_OBJECT);
    }

    /**
     * Метод equals для сравнения параметров контентов (название, описание и т.д.)
     * Учитывает специфику атрибутов контентов.
     * @param obj1 значения параметра первого контента
     * @param obj2 значения параметра второго контента
     */
    public static boolean isContentParamEquals(@Nullable Object obj1, @Nullable Object obj2)
    {
        if (isContentParamEmpty(obj1) && isContentParamEmpty(obj2))
        {
            return true;
        }
        if (obj1 instanceof Collection && obj2 instanceof Collection)
        {
            return CollectionUtils.isEqualCollections((Collection<?>)obj1, (Collection<?>)obj2);
        }
        return ObjectUtils.equals(obj1, obj2);
    }

    /**
     * Метод для определения "пустоты" параметров контентов (название, описание и т.д.)
     * Учитывает специфику атрибутов контентов.
     */
    public static boolean isContentParamEmpty(@Nullable Object obj)
    {
        if (obj instanceof SourceCode)
        {
            // Считаем, что если текст у SourceCode пуст, то такое значение SourceCode считается пустым,
            // не смотря на то, что язык может быть выбран
            return ObjectUtils.isEmpty(((SourceCode)obj).getText());
        }
        return ObjectUtils.isEmpty(obj);
    }

    /**
     * Возвращает список контентов, удовлетворяющих условию предиката
     *
     * @param root корневой контент
     */
    private static Set<Content> findContentsByCondition(Content root, Predicate<Content> condition)
    {
        Set<Content> contents = new HashSet<>();
        Queue<Content> queue = new LinkedList<>();
        queue.add(root);

        while (!queue.isEmpty())
        {
            Content content = queue.poll();
            if (condition.test(content))
            {
                contents.add(content);
            }
            queue.addAll(content.getChilds());
            if (content instanceof ObjectListBase contentList)
            {
                queue.add(contentList.getMassOperationsPanel());
            }
        }
        return contents;
    }

    /**
     * Возвращает список контентов, атрибуты которых доступны для редактирования и содержат ограничения
     *
     * @param root корневой контент
     * @param metaClass метакласс
     */
    public static Set<Content> getContentsWithRestrictedAttributes(Content root, MetaClass metaClass)
    {
        return findContentsByCondition(root, content ->
        {
            if (!(content instanceof EditablePropertyList))
            {
                return false;
            }
            AttributeGroup attributeGroup = metaClass.getAttributeGroup(
                    ((EditablePropertyList)content).getAttributeGroup());
            if (attributeGroup == null)
            {
                return false;
            }
            return attributeGroup.getAttributeCodes().stream()
                    .anyMatch(attrCode -> DateTimeRestrictionAttributeTool.isRestricted(
                            Objects.requireNonNull(metaClass.getAttribute(attrCode))));
        });
    }
}
