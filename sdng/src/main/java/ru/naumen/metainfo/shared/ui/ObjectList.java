package ru.naumen.metainfo.shared.ui;

import java.util.HashMap;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Maps;

import ru.naumen.metainfo.shared.AttributeFqn;

/**
 * Список объектов.
 */
@SuppressWarnings("serial")
@XmlType(name = "ObjectList")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class ObjectList extends ObjectListBase implements HasObjectActionsMenu, HasFilterRestrictionStrategy
{
    private ObjectActionsMenuHolder objectsActionsMenuHolder;

    private HashMap<AttributeFqn, FilterRestrictionStrategy> filterRestrictionSettings;

    public ObjectList()
    {
        objectsActionsMenuHolder = new ObjectActionsMenuHolder();
    }

    @Override
    @XmlElement
    public HashMap<AttributeFqn, FilterRestrictionStrategy> getFilterRestrictionSettings()
    {
        if (filterRestrictionSettings == null)
        {
            filterRestrictionSettings = Maps.newHashMap();
        }
        return filterRestrictionSettings;
    }

    @XmlElement
    @Override
    @Deprecated
    public String getMenuIconCatalogCode()
    {
        return null;
    }

    @XmlElement
    @Override
    public ObjectActionsMenuHolder getObjectActionsMenu()
    {
        return objectsActionsMenuHolder;
    }

    @XmlElement
    @Override
    @Deprecated
    public String getObjectActionsMenuPosition()
    {
        return null;
    }

    @XmlElement
    @Override
    @Deprecated
    public ToolPanel getObjectActionsToolPanel()
    {
        return null;
    }

    @Override
    public void setFilterRestrictionSettings(HashMap<AttributeFqn, FilterRestrictionStrategy> filterRestrictionSettings)
    {
        this.filterRestrictionSettings = filterRestrictionSettings;
    }

    @Override
    @Deprecated
    public void setMenuIconCatalogCode(String menuIconCatalogCode)
    {
        objectsActionsMenuHolder.setMenuIconCatalogCode(menuIconCatalogCode);
    }

    @Override
    public void setObjectActionsMenu(ObjectActionsMenuHolder menu)
    {
        this.objectsActionsMenuHolder = menu;
    }

    @Override
    @Deprecated
    public void setObjectActionsMenuPosition(String objectActionsMenuPosition)
    {
        objectsActionsMenuHolder.setObjectActionsMenuPosition(objectActionsMenuPosition);
    }

    @Override
    @Deprecated
    public void setObjectActionsToolPanel(ToolPanel objectActionsToolPanel)
    {
        objectsActionsMenuHolder.setObjectActionsToolPanel(objectActionsToolPanel);
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof ObjectList)
        {
            ObjectList list = (ObjectList)content;
            list.filterRestrictionSettings = Maps.newHashMap(getFilterRestrictionSettings());
            objectsActionsMenuHolder.fillContent(content);
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ObjectList();
    }
}
