package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.base.Predicates;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;

import ru.naumen.core.shared.ui.IForm;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Форма добавления/редактирования объекта.
 *
 * @see Window
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "Form", propOrder = { "caption", "layout" })
@XmlRootElement
public class Form extends SystemContentBase implements IForm, HasLayout
{
    private static final long serialVersionUID = -2938431519468630286L;
    protected ArrayList<LocalizedString> caption;
    protected Layout layout;
    protected boolean inline;

    /**
     * @return название формы
     */
    @Override
    @XmlElement(required = true)
    public List<LocalizedString> getCaption()
    {
        if (caption == null)
        {
            caption = Lists.newArrayList();
        }
        return this.caption;
    }

    @Override
    @XmlTransient
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public Collection<Content> getChilds()
    {
        return (Collection)Collections2.filter(Arrays.asList(getLayout()), Predicates.notNull());
    }

    /**
     * @return {@link Layout} расположенный на форме.
     */
    @Override
    @XmlElement(required = true)
    public Layout getLayout()
    {
        return layout;
    }

    /**
     * @return the inline
     */
    @XmlTransient
    public boolean isInline()
    {
        return this.inline;
    }

    @Override
    public void removeChild(Content content)
    {
        if (content == layout)
        {
            layout = null;
        }
    }

    /**
     * @param inline the inline to set
     */
    public void setInline(boolean inline)
    {
        this.inline = inline;
    }

    public void setLayout(Layout value)
    {
        this.layout = value;
    }

    @Override
    public String toString()
    {
        return "Form " + getCaption();
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof Form)
        {
            Form form = (Form)content;
            ObjectUtils.cloneCollection(caption, form.caption = new ArrayList<>());
            form.inline = inline;
            form.layout = ObjectUtils.clone(layout);
        }
    }

    @Override
    protected Content newInstance()
    {
        return new Form();
    }
}
