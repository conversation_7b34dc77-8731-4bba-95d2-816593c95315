package ru.naumen.metainfo.shared.ui.customform;

import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.IHasI18nTitle;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Форма массового редактирования.
 * <AUTHOR>
 * @since 04.04.18
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "MassEditForm")
public class MassEditForm extends CustomFormWithCommentBase implements IHasI18nTitle
{
    private static final long serialVersionUID = 1L;

    private boolean useAsDefault = false;

    public MassEditForm()
    {
        setUuid(Form.MASS_EDIT + '$' + UUIDGenerator.get().nextUUID());
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (!super.equals(obj))
        {
            return false;
        }
        if (getClass() != obj.getClass())
        {
            return false;
        }
        MassEditForm other = (MassEditForm)obj;
        if (!ObjectUtils.equals(commentOnFormProperty, other.commentOnFormProperty))
        {
            return false;
        }
        if (!ObjectUtils.equals(commentOnFormAttrGroupCode, other.commentOnFormAttrGroupCode))
        {
            return false;
        }
        if (showAttrDescription != other.showAttrDescription)
        {
            return false;
        }
        if (useAsDefault != other.useAsDefault)
        {
            return false;
        }
        return true;
    }

    @XmlTransient
    @Override
    public List<LocalizedString> getTitle()
    {
        return getCaption();
    }

    /**
     * Использовать как форму по умолчанию
     */
    @XmlElement(name = "use-as-default")
    public Boolean getUseAsDefault()
    {
        return useAsDefault;
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((commentOnFormProperty == null) ? 0 : commentOnFormProperty.hashCode())
                + ((commentOnFormAttrGroupCode == null) ? 0 : commentOnFormAttrGroupCode.hashCode());
        return result;
    }



    public void setUseAsDefault(Boolean useAsDefault)
    {
        this.useAsDefault = useAsDefault;
    }

    @Override
    public String toString()
    {
        return "MassEditForm for " + getAttrGroup();
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof MassEditForm)
        {
            MassEditForm form = (MassEditForm)content;
            form.commentOnFormProperty = commentOnFormProperty;
            form.commentOnFormAttrGroupCode = commentOnFormAttrGroupCode;
            form.showAttrDescription = showAttrDescription;
            form.useAsDefault = useAsDefault;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new MassEditForm();
    }
}
