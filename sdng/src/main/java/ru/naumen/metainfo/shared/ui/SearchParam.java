package ru.naumen.metainfo.shared.ui;

import static com.google.common.base.Predicates.notNull;
import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.commons.shared.utils.FunctionUtils.newType;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Lists;

import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * <AUTHOR>
 * @since 28.02.2013
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ListSearchParam", propOrder = { "classFqn", "params" })
public class SearchParam extends SystemContentBase
{
    ArrayList<SearchParamElement> params;

    ClassFqn classFqn;

    @Override
    public Collection<Content> getChilds()
    {
        return make(getParams()).filter(notNull()).map(newType(Content.class)).toCollection();
    }

    @XmlElement
    public ClassFqn getClassFqn()
    {
        return classFqn;
    }

    @XmlElement
    public List<SearchParamElement> getParams()
    {
        if (params == null)
        {
            params = Lists.newArrayList();
        }
        return params;
    }

    @Override
    public void removeChild(Content content)
    {
        getParams().remove(content);
    }

    public void setClassFqn(ClassFqn classFqn)
    {
        this.classFqn = classFqn;
    }

    public void setParams(ArrayList<SearchParamElement> params)
    {
        this.params = params;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof SearchParam)
        {
            SearchParam param = (SearchParam)content;
            param.classFqn = classFqn;
            ObjectUtils.cloneCollection(params, param.params = new ArrayList<>(params));
        }
    }

    @Override
    protected Content newInstance()
    {
        return new SearchParam();
    }
}
