
package ru.naumen.metainfo.shared.ui;

import static com.google.common.base.Predicates.notNull;
import static com.google.common.collect.Lists.newArrayList;
import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.commons.shared.utils.FunctionUtils.newType;

import java.util.Collection;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.core.shared.HasModule;
import ru.naumen.core.shared.utils.ObjectUtils;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * <AUTHOR>
 * @since 19.11.2013 г.
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "GanttContent")
public class GanttContent extends ShowCaptionContent implements HasModule
{
    /**
     * Перечисление описывает все доступные режимы фильтрации для отображения "Диаграммы Ганта"
     */
    @XmlTransient
    public static enum FilterMode implements IsSerializable
    {
        IntervalOnly, IntervalOnlyBusyResource, Full, FullBusyResource
    }

    /**
     * Пеерчисление описывает все доступные масштабы для отображения "Диаграммы Ганта"
     */
    @XmlTransient
    public static enum Scale implements IsSerializable
    {
        Hour, Day, Week, Month
    }

    /**
     * Панель действий. В xml не сериализуется. Генерируется в GanttContentSecurityHandler
     */
    private ToolPanel toolPanel;

    /**
     * Элемент справочника время обслуживания
     */
    protected String sirviceTime;

    /**
     * Масштаб времени
     */
    protected Scale scale = Scale.Day;

    /**
     * Режим фильтрации работ
     */
    protected FilterMode filterMode = FilterMode.IntervalOnly;

    /**
     * Параметры работ
     */
    protected WorkParams workParams;

    /**
     * Параметры ресурсов
     */
    protected ResourceParams resourceParams;

    @Override
    @XmlTransient
    public Collection<Content> getChilds()
    {
        return make(getToolPanel()).filter(notNull()).map(newType(Content.class)).toCollection();
    }

    public FilterMode getFilterMode()
    {
        return filterMode;
    }

    @XmlTransient
    @Override
    public String getModule()
    {
        return Modules.WORKLOAD;
    }

    @XmlElement
    public ResourceParams getResourceParams()
    {
        if (resourceParams == null)
        {
            resourceParams = new ResourceParams();
        }
        return resourceParams;
    }

    @XmlElement
    public Scale getScale()
    {
        return scale;
    }

    @XmlElement
    public String getSirviceTime()
    {
        return sirviceTime;
    }

    @XmlTransient
    public ToolPanel getToolPanel()
    {
        if (null == toolPanel)
        {
            toolPanel = new ToolPanel();
        }
        return toolPanel;
    }

    @XmlElement
    public WorkParams getWorkParams()
    {
        if (workParams == null)
        {
            workParams = new WorkParams();
        }
        return workParams;
    }

    @Override
    public void removeChild(Content content)
    {
        if (toolPanel == content)
        {
            toolPanel = null;
        }
    }

    public void setFilterMode(FilterMode filterMode)
    {
        this.filterMode = filterMode;
    }

    public void setResourceParams(ResourceParams resourceParams)
    {
        this.resourceParams = resourceParams;
    }

    public void setScale(Scale scale)
    {
        this.scale = scale;
    }

    public void setSirviceTime(String sirviceTime)
    {
        this.sirviceTime = sirviceTime;
    }

    public void setToolPanel(ToolPanel toolPanel)
    {
        this.toolPanel = toolPanel;
    }

    public void setWorkParams(WorkParams workParams)
    {
        this.workParams = workParams;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof GanttContent)
        {
            GanttContent gantt = (GanttContent)content;
            gantt.setParent(getParent());
            gantt.setProfiles(newArrayList(getProfiles()));
            gantt.setVersProfiles(newArrayList(getVersProfiles()));
            gantt.setResourceParams(ObjectUtils.clone(resourceParams));
            gantt.setScale(scale);
            gantt.setSirviceTime(sirviceTime);
            gantt.setToolPanel(toolPanel);
            gantt.setWorkParams(ObjectUtils.clone(workParams));
            gantt.setFilterMode(filterMode);
        }
    }

    @Override
    protected Content newInstance()
    {
        return new GanttContent();
    }
}
