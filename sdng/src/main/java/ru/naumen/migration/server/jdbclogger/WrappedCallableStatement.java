package ru.naumen.migration.server.jdbclogger;

import static ru.naumen.migration.server.jdbclogger.JdbcLoggerConstants.*;

import java.io.InputStream;
import java.io.Reader;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Array;
import java.sql.Blob;
import java.sql.CallableStatement;
import java.sql.Clob;
import java.sql.Date;
import java.sql.NClob;
import java.sql.PreparedStatement;
import java.sql.Ref;
import java.sql.RowId;
import java.sql.SQLException;
import java.sql.SQLXML;
import java.sql.Statement;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Map;

import ru.naumen.migration.server.jdbclogger.DataSourceWrapper.SqlLogger;

/**
 * Класс используется для оборачивания методов CallableStatement и логгирования всех sql запросов во время миграции.
 * За основу вязата библиотека log4jdbc-log4j2
 *
 * <AUTHOR>
 */
public class WrappedCallableStatement extends WrappedPreparedStatement implements CallableStatement
{
    private static final String CLASS_TYPE = "CallableStatement";
    private CallableStatement delegate;

    public WrappedCallableStatement(String sql, WrappedConnection connection,
            CallableStatement realCallableStatement, SqlLogger log)
    {
        super(sql, connection, realCallableStatement, log);
        this.delegate = realCallableStatement;
    }

    @Override
    public Array getArray(int i) throws SQLException
    {
        return delegate.getArray(i);
    }

    @Override
    public Array getArray(String parameterName) throws SQLException
    {
        return delegate.getArray(parameterName);
    }

    @Override
    public BigDecimal getBigDecimal(int parameterIndex) throws SQLException
    {
        return delegate.getBigDecimal(parameterIndex);
    }

    /**
     * @deprecated
     */
    @Deprecated
    @Override
    public BigDecimal getBigDecimal(int parameterIndex, int scale) throws SQLException
    {
        return delegate.getBigDecimal(parameterIndex, scale);
    }

    @Override
    public BigDecimal getBigDecimal(String parameterName) throws SQLException
    {
        return delegate.getBigDecimal(parameterName);
    }

    @Override
    public Blob getBlob(int i) throws SQLException
    {
        return delegate.getBlob(i);
    }

    @Override
    public Blob getBlob(String parameterName) throws SQLException
    {
        return delegate.getBlob(parameterName);
    }

    @Override
    public boolean getBoolean(int parameterIndex) throws SQLException
    {
        return delegate.getBoolean(parameterIndex);
    }

    @Override
    public boolean getBoolean(String parameterName) throws SQLException
    {
        return delegate.getBoolean(parameterName);
    }

    @Override
    public byte getByte(int parameterIndex) throws SQLException
    {
        return delegate.getByte(parameterIndex);
    }

    @Override
    public byte getByte(String parameterName) throws SQLException
    {
        return delegate.getByte(parameterName);
    }

    @Override
    public byte[] getBytes(int parameterIndex) throws SQLException
    {
        return delegate.getBytes(parameterIndex);
    }

    @Override
    public byte[] getBytes(String parameterName) throws SQLException
    {
        return delegate.getBytes(parameterName);
    }

    @Override
    public Reader getCharacterStream(int parameterIndex) throws SQLException
    {
        return delegate.getCharacterStream(parameterIndex);
    }

    @Override
    public Reader getCharacterStream(String parameterName) throws SQLException
    {
        return delegate.getCharacterStream(parameterName);
    }

    @Override
    public String getClassType()
    {
        return CLASS_TYPE;
    }

    @Override
    public Clob getClob(int i) throws SQLException
    {
        return delegate.getClob(i);
    }

    @Override
    public Clob getClob(String parameterName) throws SQLException
    {
        return delegate.getClob(parameterName);
    }

    @Override
    public Date getDate(int parameterIndex) throws SQLException
    {
        return delegate.getDate(parameterIndex);
    }

    @Override
    public Date getDate(int parameterIndex, Calendar cal) throws SQLException
    {
        return delegate.getDate(parameterIndex, cal);
    }

    @Override
    public Date getDate(String parameterName) throws SQLException
    {
        return delegate.getDate(parameterName);
    }

    @Override
    public Date getDate(String parameterName, Calendar cal) throws SQLException
    {
        return delegate.getDate(parameterName, cal);
    }

    @Override
    public double getDouble(int parameterIndex) throws SQLException
    {
        return delegate.getDouble(parameterIndex);
    }

    @Override
    public double getDouble(String parameterName) throws SQLException
    {
        return delegate.getDouble(parameterName);
    }

    @Override
    public float getFloat(int parameterIndex) throws SQLException
    {
        return delegate.getFloat(parameterIndex);
    }

    @Override
    public float getFloat(String parameterName) throws SQLException
    {
        return delegate.getFloat(parameterName);
    }

    @Override
    public int getInt(int parameterIndex) throws SQLException
    {
        return delegate.getInt(parameterIndex);
    }

    @Override
    public int getInt(String parameterName) throws SQLException
    {
        return delegate.getInt(parameterName);
    }

    @Override
    public long getLong(int parameterIndex) throws SQLException
    {
        return delegate.getLong(parameterIndex);
    }

    @Override
    public long getLong(String parameterName) throws SQLException
    {
        return delegate.getLong(parameterName);
    }

    @Override
    public Reader getNCharacterStream(int parameterIndex) throws SQLException
    {
        return delegate.getNCharacterStream(parameterIndex);
    }

    @Override
    public Reader getNCharacterStream(String parameterName) throws SQLException
    {
        return delegate.getNCharacterStream(parameterName);
    }

    @Override
    public NClob getNClob(int parameterIndex) throws SQLException
    {
        return delegate.getNClob(parameterIndex);
    }

    @Override
    public NClob getNClob(String parameterName) throws SQLException
    {
        return delegate.getNClob(parameterName);
    }

    @Override
    public String getNString(int parameterIndex) throws SQLException
    {
        return delegate.getNString(parameterIndex);
    }

    @Override
    public String getNString(String parameterName) throws SQLException
    {
        return delegate.getNString(parameterName);
    }

    @Override
    public Object getObject(int parameterIndex) throws SQLException
    {
        return delegate.getObject(parameterIndex);
    }

    @Override
    public <T> T getObject(int parameterIndex, Class<T> type) throws SQLException
    {
        return delegate.getObject(parameterIndex, type);
    }

    @Override
    public Object getObject(int i, Map<String, Class<?>> map) throws SQLException
    {
        return delegate.getObject(i, map);
    }

    @Override
    public Object getObject(String parameterName) throws SQLException
    {
        return delegate.getObject(parameterName);
    }

    @Override
    public <T> T getObject(String parameterName, Class<T> type) throws SQLException
    {
        return delegate.getObject(parameterName, type);
    }

    @Override
    public Object getObject(String parameterName, Map<String, Class<?>> map) throws SQLException
    {
        return delegate.getObject(parameterName, map);
    }

    public CallableStatement getRealCallableStatement()
    {
        return delegate;
    }

    @Override
    public Ref getRef(int i) throws SQLException
    {
        return delegate.getRef(i);
    }

    @Override
    public Ref getRef(String parameterName) throws SQLException
    {
        return delegate.getRef(parameterName);
    }

    @Override
    public RowId getRowId(int parameterIndex) throws SQLException
    {
        return delegate.getRowId(parameterIndex);
    }

    @Override
    public RowId getRowId(String parameterName) throws SQLException
    {
        return delegate.getRowId(parameterName);
    }

    @Override
    public short getShort(int parameterIndex) throws SQLException
    {
        return delegate.getShort(parameterIndex);
    }

    @Override
    public short getShort(String parameterName) throws SQLException
    {
        return delegate.getShort(parameterName);
    }

    @Override
    public SQLXML getSQLXML(int parameterIndex) throws SQLException
    {
        return delegate.getSQLXML(parameterIndex);
    }

    @Override
    public SQLXML getSQLXML(String parameterName) throws SQLException
    {
        return delegate.getSQLXML(parameterName);
    }

    @Override
    public String getString(int parameterIndex) throws SQLException
    {
        return delegate.getString(parameterIndex);
    }

    @Override
    public String getString(String parameterName) throws SQLException
    {
        return delegate.getString(parameterName);
    }

    @Override
    public Time getTime(int parameterIndex) throws SQLException
    {
        return delegate.getTime(parameterIndex);
    }

    @Override
    public Time getTime(int parameterIndex, Calendar cal) throws SQLException
    {
        return delegate.getTime(parameterIndex, cal);
    }

    @Override
    public Time getTime(String parameterName) throws SQLException
    {
        return delegate.getTime(parameterName);
    }

    @Override
    public Time getTime(String parameterName, Calendar cal) throws SQLException
    {
        return delegate.getTime(parameterName, cal);
    }

    @Override
    public Timestamp getTimestamp(int parameterIndex) throws SQLException
    {
        return delegate.getTimestamp(parameterIndex);
    }

    @Override
    public Timestamp getTimestamp(int parameterIndex, Calendar cal) throws SQLException
    {
        return delegate.getTimestamp(parameterIndex, cal);
    }

    @Override
    public Timestamp getTimestamp(String parameterName) throws SQLException
    {
        return delegate.getTimestamp(parameterName);
    }

    @Override
    public Timestamp getTimestamp(String parameterName, Calendar cal) throws SQLException
    {
        return delegate.getTimestamp(parameterName, cal);
    }

    @Override
    public URL getURL(int parameterIndex) throws SQLException
    {
        return delegate.getURL(parameterIndex);
    }

    @Override
    public URL getURL(String parameterName) throws SQLException
    {
        return delegate.getURL(parameterName);
    }

    @Override
    public boolean isWrapperFor(Class<?> iface) throws SQLException
    {
        try
        {
            //NOTE: could call super.isWrapperFor to simplify this logic, but it would result in extra log output
            //when the super classes would be invoked..
            return (iface != null && (iface == CallableStatement.class || iface == PreparedStatement.class
                    || iface == Statement.class || iface == Wrapper.class))
                    || delegate.isWrapperFor(iface);
        }
        catch (SQLException s)
        {
            String methodCall = IS_WRAPPER_FOR + (iface == null ? "null" : iface.getName()) + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void registerOutParameter(int parameterIndex, int sqlType) throws SQLException
    {
        String methodCall = REGISTER_OUT_PARAMETER + parameterIndex + ", " + sqlType + ")";
        argTraceSet(parameterIndex, null, "<OUT>");
        try
        {
            delegate.registerOutParameter(parameterIndex, sqlType);
        }
        catch (SQLException s)
        {
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void registerOutParameter(int parameterIndex, int sqlType, int scale) throws SQLException
    {
        argTraceSet(parameterIndex, null, "<OUT>");
        try
        {
            delegate.registerOutParameter(parameterIndex, sqlType, scale);
        }
        catch (SQLException s)
        {
            String methodCall = REGISTER_OUT_PARAMETER + parameterIndex + ", " + sqlType + ", " + scale + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void registerOutParameter(int paramIndex, int sqlType, String typeName) throws SQLException
    {
        argTraceSet(paramIndex, null, "<OUT>");
        try
        {
            delegate.registerOutParameter(paramIndex, sqlType, typeName);
        }
        catch (SQLException s)
        {
            String methodCall = REGISTER_OUT_PARAMETER + paramIndex + ", " + sqlType + ", " + typeName + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void registerOutParameter(String parameterName, int sqlType) throws SQLException
    {
        try
        {
            delegate.registerOutParameter(parameterName, sqlType);
        }
        catch (SQLException s)
        {
            String methodCall = REGISTER_OUT_PARAMETER + parameterName + ", " + sqlType + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void registerOutParameter(String parameterName, int sqlType, int scale) throws SQLException
    {
        try
        {
            delegate.registerOutParameter(parameterName, sqlType, scale);
        }
        catch (SQLException s)
        {
            String methodCall = REGISTER_OUT_PARAMETER + parameterName + ", " + sqlType + ", " + scale + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void registerOutParameter(String parameterName, int sqlType, String typeName) throws SQLException
    {
        try
        {
            delegate.registerOutParameter(parameterName, sqlType, typeName);
        }
        catch (SQLException s)
        {
            String methodCall = REGISTER_OUT_PARAMETER + parameterName + ", " + sqlType + ", " + typeName + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setAsciiStream(String parameterName, InputStream x) throws SQLException
    {
        try
        {
            delegate.setAsciiStream(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_ASCII_STREAM + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setAsciiStream(String parameterName, InputStream x, int length) throws SQLException
    {
        try
        {
            delegate.setAsciiStream(parameterName, x, length);
        }
        catch (SQLException s)
        {
            String methodCall = SET_ASCII_STREAM + parameterName + ", " + x + ", " + length + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setAsciiStream(String parameterName, InputStream x, long length) throws SQLException
    {
        try
        {
            delegate.setAsciiStream(parameterName, x, length);
        }
        catch (SQLException s)
        {
            String methodCall = SET_ASCII_STREAM + parameterName + ", " + x + ", " + length + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setBigDecimal(String parameterName, BigDecimal x) throws SQLException
    {
        try
        {
            delegate.setBigDecimal(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_BIG_DECIMAL + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setBinaryStream(String parameterName, InputStream x) throws SQLException
    {
        try
        {
            delegate.setBinaryStream(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_BINARY_STREAM + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setBinaryStream(String parameterName, InputStream x, int length) throws SQLException
    {
        try
        {
            delegate.setBinaryStream(parameterName, x, length);
        }
        catch (SQLException s)
        {
            String methodCall = SET_BINARY_STREAM + parameterName + ", " + x + ", " + length + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setBinaryStream(String parameterName, InputStream x, long length) throws SQLException
    {
        try
        {
            delegate.setBinaryStream(parameterName, x, length);
        }
        catch (SQLException s)
        {
            String methodCall = SET_BINARY_STREAM + parameterName + ", " + x + ", " + length + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setBlob(String parameterName, Blob x) throws SQLException
    {
        try
        {
            delegate.setBlob(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_BLOB + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setBlob(String parameterName, InputStream inputStream) throws SQLException
    {
        try
        {
            delegate.setBlob(parameterName, inputStream);
        }
        catch (SQLException s)
        {
            String methodCall = SET_BLOB + parameterName + ", " + inputStream + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setBlob(String parameterName, InputStream inputStream, long length) throws SQLException
    {
        String methodCall = SET_BLOB + parameterName + ", " + inputStream + ", " + length + ")";
        try
        {
            delegate.setBlob(parameterName, inputStream, length);
        }
        catch (SQLException s)
        {
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setBoolean(String parameterName, boolean x) throws SQLException
    {
        try
        {
            delegate.setBoolean(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_BOOLEAN + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setByte(String parameterName, byte x) throws SQLException
    {
        try
        {
            delegate.setByte(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_BYTE + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setBytes(String parameterName, byte[] x) throws SQLException
    {
        try
        {
            delegate.setBytes(parameterName, x);
        }
        catch (SQLException s)
        {
            //todo: dump byte array?
            String methodCall = SET_BYTES + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setCharacterStream(String parameterName, Reader reader) throws SQLException
    {
        try
        {
            delegate.setCharacterStream(parameterName, reader);
        }
        catch (SQLException s)
        {
            String methodCall = SET_CHARACTER_STREAM + parameterName + ", " + reader + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setCharacterStream(String parameterName, Reader reader, int length) throws SQLException
    {
        try
        {
            delegate.setCharacterStream(parameterName, reader, length);
        }
        catch (SQLException s)
        {
            String methodCall = SET_CHARACTER_STREAM + parameterName + ", " + reader + ", " + length + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setCharacterStream(String parameterName, Reader reader, long length) throws SQLException
    {
        try
        {
            delegate.setCharacterStream(parameterName, reader, length);
        }
        catch (SQLException s)
        {
            String methodCall = SET_CHARACTER_STREAM + parameterName + ", " + reader + ", " + length + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setClob(String parameterName, Clob x) throws SQLException
    {
        try
        {
            delegate.setClob(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_CLOB + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setClob(String parameterName, Reader reader) throws SQLException
    {
        try
        {
            delegate.setClob(parameterName, reader);
        }
        catch (SQLException s)
        {
            String methodCall = SET_CLOB + parameterName + ", " + reader + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setClob(String parameterName, Reader reader, long length) throws SQLException
    {
        try
        {
            delegate.setClob(parameterName, reader, length);
        }
        catch (SQLException s)
        {
            String methodCall = SET_CLOB + parameterName + ", " + reader + ", " + length + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setDate(String parameterName, Date x) throws SQLException
    {
        try
        {
            delegate.setDate(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_DATE + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setDate(String parameterName, Date x, Calendar cal) throws SQLException
    {
        try
        {
            delegate.setDate(parameterName, x, cal);
        }
        catch (SQLException s)
        {
            String methodCall = SET_DATE + parameterName + ", " + x + ", " + cal + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setDouble(String parameterName, double x) throws SQLException
    {
        try
        {
            delegate.setDouble(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_DOUBLE + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setFloat(String parameterName, float x) throws SQLException
    {
        try
        {
            delegate.setFloat(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_FLOAT + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setInt(String parameterName, int x) throws SQLException
    {
        try
        {
            delegate.setInt(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_INT + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setLong(String parameterName, long x) throws SQLException
    {
        try
        {
            delegate.setLong(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_LONG + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setNCharacterStream(String parameterName, Reader reader) throws SQLException
    {
        try
        {
            delegate.setNCharacterStream(parameterName, reader);
        }
        catch (SQLException s)
        {
            String methodCall = SET_N_CHARACTER_STREAM + parameterName + ", " + reader + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setNCharacterStream(String parameterName, Reader reader, long length) throws SQLException
    {
        try
        {
            delegate.setNCharacterStream(parameterName, reader, length);
        }
        catch (SQLException s)
        {
            String methodCall = SET_N_CHARACTER_STREAM + parameterName + ", " + reader + ", " + length + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setNClob(String parameterName, NClob value) throws SQLException
    {
        try
        {
            delegate.setNClob(parameterName, value);
        }
        catch (SQLException s)
        {
            String methodCall = SET_N_CLOB + parameterName + ", " + value + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setNClob(String parameterName, Reader reader) throws SQLException
    {
        try
        {
            delegate.setNClob(parameterName, reader);
        }
        catch (SQLException s)
        {
            String methodCall = SET_N_CLOB + parameterName + ", " + reader + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setNClob(String parameterName, Reader reader, long length) throws SQLException
    {
        try
        {
            delegate.setNClob(parameterName, reader, length);
        }
        catch (SQLException s)
        {
            String methodCall = SET_N_CLOB + parameterName + ", " + reader + ", " + length + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setNString(String parameterName, String value) throws SQLException
    {
        try
        {
            delegate.setNString(parameterName, value);
        }
        catch (SQLException s)
        {
            String methodCall = SET_N_STRING + parameterName + ", " + value + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setNull(String parameterName, int sqlType) throws SQLException
    {
        try
        {
            delegate.setNull(parameterName, sqlType);
        }
        catch (SQLException s)
        {
            String methodCall = SET_NULL + parameterName + ", " + sqlType + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setNull(String parameterName, int sqlType, String typeName) throws SQLException
    {
        try
        {
            delegate.setNull(parameterName, sqlType, typeName);
        }
        catch (SQLException s)
        {
            String methodCall = SET_NULL + parameterName + ", " + sqlType + ", " + typeName + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setObject(String parameterName, Object x) throws SQLException
    {
        try
        {
            delegate.setObject(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_OBJECT + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setObject(String parameterName, Object x, int targetSqlType) throws SQLException
    {
        try
        {
            delegate.setObject(parameterName, x, targetSqlType);
        }
        catch (SQLException s)
        {
            String methodCall = SET_OBJECT + parameterName + ", " + x + ", " + targetSqlType + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setObject(String parameterName, Object x, int targetSqlType, int scale) throws SQLException
    {
        try
        {
            delegate.setObject(parameterName, x, targetSqlType, scale);
        }
        catch (SQLException s)
        {
            String methodCall = SET_OBJECT + parameterName + ", " + x + ", " + targetSqlType + ", " + scale + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setRowId(String parameterName, RowId x) throws SQLException
    {
        try
        {
            delegate.setRowId(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_ROW_ID + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setShort(String parameterName, short x) throws SQLException
    {
        try
        {
            delegate.setShort(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_SHORT + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setSQLXML(String parameterName, SQLXML xmlObject) throws SQLException
    {
        try
        {
            delegate.setSQLXML(parameterName, xmlObject);
        }
        catch (SQLException s)
        {
            String methodCall = SET_SQLXML + parameterName + ", " + xmlObject + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setString(String parameterName, String x) throws SQLException
    {
        try
        {
            delegate.setString(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_STRING + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setTime(String parameterName, Time x) throws SQLException
    {
        try
        {
            delegate.setTime(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_TIME + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setTime(String parameterName, Time x, Calendar cal) throws SQLException
    {
        try
        {
            delegate.setTime(parameterName, x, cal);
        }
        catch (SQLException s)
        {
            String methodCall = SET_TIME + parameterName + ", " + x + ", " + cal + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setTimestamp(String parameterName, Timestamp x) throws SQLException
    {
        try
        {
            delegate.setTimestamp(parameterName, x);
        }
        catch (SQLException s)
        {
            String methodCall = SET_TIMESTAMP + parameterName + ", " + x + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setTimestamp(String parameterName, Timestamp x, Calendar cal) throws SQLException
    {
        try
        {
            delegate.setTimestamp(parameterName, x, cal);
        }
        catch (SQLException s)
        {
            String methodCall = SET_TIMESTAMP + parameterName + ", " + x + ", " + cal + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public void setURL(String parameterName, URL val) throws SQLException
    {
        try
        {
            delegate.setURL(parameterName, val);
        }
        catch (SQLException s)
        {
            String methodCall = SET_URL + parameterName + ", " + val + ")";
            reportException(methodCall, s);
            throw s;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T unwrap(Class<T> iface) throws SQLException
    {
        String methodCall = UNWRAP + (iface == null ? "null" : iface.getName()) + ")";
        try
        {
            //todo: double check this logic
            //NOTE: could call super.isWrapperFor to simplify this logic, but it would result in extra log output
            //because the super classes would be invoked, thus executing their logging methods too...
            return (T)reportReturn(methodCall,
                    (iface != null && (iface == CallableStatement.class || iface == PreparedStatement.class
                            || iface == Statement.class || iface == Wrapper.class)) ? (T)this : delegate.unwrap(iface));
        }
        catch (SQLException s)
        {
            reportException(methodCall, s);
            throw s;
        }
    }

    @Override
    public boolean wasNull() throws SQLException
    {
        try
        {
            return delegate.wasNull();
        }
        catch (SQLException s)
        {
            reportException(WAS_NULL, s);
            throw s;
        }
    }

}
