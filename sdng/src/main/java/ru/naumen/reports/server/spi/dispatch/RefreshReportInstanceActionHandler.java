package ru.naumen.reports.server.spi.dispatch;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.reports.server.ReportsService;
import ru.naumen.reports.server.objects.ReportInstance;
import ru.naumen.reports.server.spi.ReportDateUtils;
import ru.naumen.reports.server.spi.ReportsUtils;
import ru.naumen.reports.shared.Constants;
import ru.naumen.reports.shared.ReportDetails;
import ru.naumen.reports.shared.dispatch.CheckExportPosibilityAction;
import ru.naumen.reports.shared.dispatch.CheckExportPosibilityResult;
import ru.naumen.reports.shared.dispatch.RefreshReportInstanceAction;
import ru.naumen.reports.shared.dispatch.RefreshReportInstanceResponse;

/**
 * Обновление экземпляра отчета
 *
 * <AUTHOR>
 * @since 16.10.2012
 */
@Component
public class RefreshReportInstanceActionHandler
        extends TransactionalActionHandler<RefreshReportInstanceAction, RefreshReportInstanceResponse>
{
    private final ReportsService reportsService;
    private final Dispatch dispatch;
    private final ReportsUtils reportsUtils;
    private final IPrefixObjectLoaderService loader;
    private final MessageFacade messages;
    private final ReportDateUtils reportDateUtils;

    public RefreshReportInstanceActionHandler(ReportsService reportsService, Dispatch dispatch,
            ReportsUtils reportsUtils, IPrefixObjectLoaderService loader, MessageFacade messages,
            ReportDateUtils reportDateUtils)
    {
        this.reportsService = reportsService;
        this.dispatch = dispatch;
        this.reportsUtils = reportsUtils;
        this.loader = loader;
        this.messages = messages;
        this.reportDateUtils = reportDateUtils;
    }

    @Override
    public RefreshReportInstanceResponse executeInTransaction(RefreshReportInstanceAction action,
            ExecutionContext context) throws DispatchException
    {
        String reportUUID = action.getContext().getReportUUID();
        ReportInstance instance = loader.get(reportUUID);

        RefreshReportInstanceResponse response = new RefreshReportInstanceResponse();

        if (instance.getData() != null)
        {
            CheckExportPosibilityResult checkResponse = dispatch.executeExceptionSafe(
                    new CheckExportPosibilityAction(action.getContext().getReportUUID(), Constants.ReportFormat.HTML));

            if (checkResponse.isDeffered())
            {
                response.setReport(null);
                response.setError(checkResponse.getDetails());
                response.setDetails(ReportDetails.ONLY_ASYNC_EXPORT);
                return response;
            }
        }

        ReportsUtils.setTimeZoneOffset(action);

        reportDateUtils.convertDateParametersToTimeZone(action.getContext().getParameters());

        String html = reportsService.refreshReport(action.getContext());
        reportsUtils.fillResponse(html, response);

        ReportInstance report = loader.get(action.getContext().getReportUUID());
        if (!StringUtilities.isEmpty(report.getErrorMessageCode()))
        {
            response.setError(messages.getMessage(report.getErrorMessageCode()));
        }
        return response;
    }

}