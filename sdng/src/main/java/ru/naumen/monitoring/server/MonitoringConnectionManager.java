
package ru.naumen.monitoring.server;

import com.tibbo.aggregate.common.context.Context;
import com.tibbo.aggregate.common.context.ContextManager;

/**
 * Интерфейс менеджера подключения к серверу системы мониторинга
 * 
 * <AUTHOR>
 * @since 30.03.2012
 */
public interface MonitoringConnectionManager
{
    /**
     * Изменить использование соединения с сервером на delta
     * @param delta
     */
    void addUsage(long delta);

    /**
     * Изменить использование соединения с сервером на delta и обновить соединение, если нужно (включить/выключить)
     * Нужно использовать, если соединение нужно получить в данном потоке
     * @param delta
     */
    void addUsageAndUpdate(long delta);

    /**
     * @return интервал, через который происходят попытки соединения с сервером в случае неполадок
     */
    int getCheckConnectionInterval();

    /**
     * @return менеджер контекстов tibbo, через этот менеджер производятся все операции с объектами
     */
    ContextManager<? extends Context> getContextManager();

    /**
     * @return контроллер сервера {@link MonitoringServerController}
     */
    MonitoringServerController getMonitoringServerController();

    /**
     * Устанавливает интервал проверки подключения к серверу
     * @param checkConnectionInterval 
     */
    void setCheckConnectionInterval(int checkConnectionInterval);

    /**
     * Если изменились настройки или соединение пропало, то
     * пробует переподключиться с актуальными параметрами
     */
    void updateConnection();
}
