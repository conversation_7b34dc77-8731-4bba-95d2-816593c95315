package ru.naumen.core.shared.licensing.policy;

import com.google.gwt.user.client.rpc.IsSerializable;
import ru.naumen.metainfo.shared.elements.wf.Transition;

import java.util.Collections;
import java.util.Set;
import java.util.function.Predicate;

/**
 * Политики лицензирования действий с связанных с жизненным циклом объекта
 * 
 * <AUTHOR>
 */
public class WorkflowPolicy implements IsSerializable
{
    private Set<TransitionPolicy> transitions;

    public WorkflowPolicy(Set<TransitionPolicy> transitions)
    {
        this.transitions = transitions;
    }

    protected WorkflowPolicy()
    {
    }

    public Predicate<Transition> getTransitionPredicate()
    {
        return input ->
                null != input
                        && hasTransition(input.getBeginState(), input.getEndState());
    }

    public Set<TransitionPolicy> getTransitions()
    {
        return Collections.unmodifiableSet(transitions);
    }

    /**
     * Доступен ли переход из стостояния from в состояние to
     */
    public boolean hasTransition(String from, String to)
    {
        return transitions.contains(new TransitionPolicy(from, to))
                // Если нет явного перехода,
                // проверим возможность перехода из любого статуса в to
                || transitions.contains(new TransitionPolicy(null, to));
    }
}
