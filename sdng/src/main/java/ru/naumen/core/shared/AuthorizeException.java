package ru.naumen.core.shared;

import ru.naumen.commons.shared.FxException;

/**
 * Исключение сервиса авторизации.
 * Генерируется при проверке прав доступа.
 * <AUTHOR>
 */
public class AuthorizeException extends FxException
{
    private static final long serialVersionUID = -3473369550219286695L;

    public AuthorizeException()
    {
        super();
    }

    public AuthorizeException(String msg)
    {
        super(msg);
    }

    public AuthorizeException(String msg, boolean readable)
    {
        super(msg, readable);
    }

    public AuthorizeException(String msg, boolean readable, String uiMessage)
    {
        super(msg, readable, uiMessage);
    }

    public AuthorizeException(String msg, boolean readable, Throwable cause)
    {
        super(msg, readable, cause);
    }

    public AuthorizeException(Throwable cause)
    {
        super(cause);
    }
}
