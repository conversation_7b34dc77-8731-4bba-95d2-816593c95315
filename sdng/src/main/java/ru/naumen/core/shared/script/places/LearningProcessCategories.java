package ru.naumen.core.shared.script.places;

public enum LearningProcessCategories implements ScriptCategory
{
    GATHERING_DATA, SAVE_DATA, DATA_PREPARATION, LEARNING_AND_VALIDATION;

    @Override
    public String getTitleCode()
    {
        return "scriptcatalog-LearningProcessCategories." + name() + ".title";
    }

    @Override
    public boolean isCatalogCategory()
    {
        return true;
    }
}
