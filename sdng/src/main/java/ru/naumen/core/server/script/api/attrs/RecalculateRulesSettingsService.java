package ru.naumen.core.server.script.api.attrs;

import static ru.naumen.core.server.bo.bop.Constants.RECALCULATE_VALUE_FROM_VMAP;

import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.bcp.server.operations.context.HasObjectBOContext;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.IDao;
import ru.naumen.core.server.catalog.valuemap.ValueMapCatalogItem;
import ru.naumen.core.server.catalog.valuemap.ValueMapCatalogItemDao;
import ru.naumen.core.server.catalog.valuemap.ValueMapCatalogItemImpl;
import ru.naumen.core.server.catalog.valuemap.ValueMapHelper;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.shared.AggregateContainer;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.fts.server.lucene.reindex.ReindexController;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeDescription;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Сервис повторного расчёта значений атрибутов, определяемых по таблице соответствий.
 *
 * <AUTHOR>
 * @since 23.05.2024
 */
@Component
public class RecalculateRulesSettingsService
{
    private static final Logger LOG = LoggerFactory.getLogger(RecalculateRulesSettingsService.class);

    private final CommonUtils commonUtils;
    private final MetainfoService metainfoService;
    private final DaoFactory daoFactory;
    private final ReindexController reindexController;
    private final ValueMapCatalogItemDao<ValueMapCatalogItemImpl> valueMapDao;
    private final ValueMapHelper valueMapHelper;
    private final AccessorHelper accessorHelper;
    private final ResolverUtils resolverUtils;

    @Inject
    public RecalculateRulesSettingsService(
            final CommonUtils commonUtils,
            final MetainfoService metainfoService,
            final DaoFactory daoFactory,
            final ReindexController reindexController,
            final ValueMapCatalogItemDao<ValueMapCatalogItemImpl> valueMapDao,
            final ValueMapHelper valueMapHelper,
            final AccessorHelper accessorHelper,
            final ResolverUtils resolverUtils)
    {
        this.commonUtils = commonUtils;
        this.metainfoService = metainfoService;
        this.daoFactory = daoFactory;
        this.reindexController = reindexController;
        this.valueMapDao = valueMapDao;
        this.valueMapHelper = valueMapHelper;
        this.accessorHelper = accessorHelper;
        this.resolverUtils = resolverUtils;
    }

    /**
     * Пересчитать значение атрибутов, определяемых по таблице соответствий, в объекте.
     * При этом, как при обычном редактировании, объект пройдет валидацию в полном объеме, отработают действия по
     * событиям и т.д.
     *
     * @param object         объекта, для которого необходимо пересчитать значение атрибута
     * @param attributeCodes коды атрибутов, значение которых будет пересчитано
     */
    public void recalculateRulesSettingsValue(IUUIDIdentifiable object, List<String> attributeCodes)
    {
        IProperties properties = MapProperties.builder()
                .setProperty(RECALCULATE_VALUE_FROM_VMAP, attributeCodes)
                .build();
        TransactionRunner.run(TransactionType.NEW, () -> commonUtils.edit(object, properties));
    }

    /**
     * Пересчитать значение атрибутов, определяемых по таблице соответствий, во всех объектах указанного метакласса.
     * Будут пересчитаны только сами значения, то есть: не отработают действия по событиям, объекты не пройдут
     * валидацию и т.д.
     *
     * @param classFqn       FQN класса/типа, в объектах которого будет пересчитано значение атрибута
     * @param attributeCodes коды атрибутов, значения которых будут пересчитаны
     */
    public void recalculateRulesSettingsValueOfMetaClass(final ClassFqn classFqn, final List<String> attributeCodes)
    {
        MetaClass metaClass = metainfoService.getMetaClass(classFqn);

        IDao<IUUIDIdentifiable> dao = daoFactory.get(classFqn);
        Iterator<IUUIDIdentifiable> result = dao.iterateAll(metaClass);
        while (result.hasNext())
        {
            IUUIDIdentifiable object = result.next();
            for (String attrCode : attributeCodes)
            {
                MetaClass directMetaClass = metainfoService.getMetaClass(object);
                recalculateRulesSettingsValueWithoutPermission(directMetaClass, object, attrCode);
            }
        }
        reindexController.reindex(classFqn);
    }

    private void recalculateRulesSettingsValueWithoutPermission(final MetaClass metaClass,
            final IUUIDIdentifiable object,
            final String attrCode)
    {
        Attribute attribute = metaClass.getAttribute(attrCode);
        if (attribute == null || !Boolean.TRUE.equals(attribute.isDeterminable()))
        {
            return;
        }

        try
        {
            TransactionRunner.run(TransactionType.NEW, () ->
            {
                HasObjectBOContext<IUUIDIdentifiable> ctx = new HasObjectBOContext<>(object, metaClass.getFqn());
                ValueMapCatalogItem<?> valueMapItem = valueMapDao.getItemWithoutEscalationType(
                        attribute.getDeterminer());
                Object calculatedValue = valueMapHelper.calculateValue(attrCode, valueMapItem, ctx);

                // каждый элемент агрегирующего атрибута нужно установить отдельно
                AttributeType attributeType = attribute.getType();
                if (Constants.AggregateAttributeType.CODE.equals(attributeType.getCode()))
                {
                    Map<ClassFqn, IUUIDIdentifiable> attributeValue =
                            resolveAggregateValue(attribute, calculatedValue, object);
                    AggregateAttributeType type = attributeType.cast();
                    for (AttributeDescription description : type.getAttributes())
                    {
                        String attributePartCode = description.getAttribute();
                        ClassFqn referenceMetaClass = description.getReferenceMetaClass();
                        accessorHelper.setAttributeValueWithoutPermission(object, attributePartCode,
                                attributeValue.get(referenceMetaClass));
                    }
                    return;
                }

                //для типов "Набор ссылок на БО", "Набор типов класса", "Набор элементов справочника"
                Object newValue = (calculatedValue instanceof List<?> calculatedListValue)
                        ? new HashSet<>(calculatedListValue)
                        : calculatedValue;
                accessorHelper.setAttributeValueWithoutPermission(object, attrCode, newValue);
            });
        }
        catch (Exception e)
        {
            LOG.error(e.getMessage(), e);
        }
    }

    private Map<ClassFqn, IUUIDIdentifiable> resolveAggregateValue(final Attribute attribute,
            @Nullable final Object value, final IUUIDIdentifiable object)
    {
        if (value == null)
        {
            return Collections.emptyMap();
        }

        ResolverContext context = new ResolverContext(attribute, value, object);
        return resolverUtils.<AggregateContainer> resolvAndValidate(context).asMap();
    }
}
