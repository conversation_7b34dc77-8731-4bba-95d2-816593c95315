package ru.naumen.core.server.mbean.plannedevent;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

import jakarta.inject.Inject;
import javax.management.openmbean.TabularData;
import javax.sql.DataSource;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import ru.naumen.core.server.mbean.OpenTypeAdapter;

import com.google.common.collect.Maps;

/**
 * Реализация MBean {@link PlannedEventStatisticsMBean}
 *
 * <AUTHOR>
 * @since 15.02.2019
 */
@Component
@Transactional(propagation = Propagation.REQUIRED)
public class PlannedEventStatistics implements PlannedEventStatisticsMBean
{
    private static final String SELECT_OVERDUE_TRIGGERS = "select trig.job_name, trig.next_fire_time "
            + "from qrtz_triggers trig where next_fire_time < ?";
    private static final String SELECT_COUNT_OVERDUE_TRIGGERS = "select count(trig) "
            + "from qrtz_triggers trig where next_fire_time < ?";

    private final DataSource dataSource;

    @Inject
    public PlannedEventStatistics(DataSource dataSource)
    {
        this.dataSource = dataSource;
    }

    @Override
    public long getOverdueTasks() throws SQLException
    {
        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = statementWithCurrentTime(connection, SELECT_COUNT_OVERDUE_TRIGGERS);
             ResultSet resultSet = statement.executeQuery())
        {
            if (resultSet != null && resultSet.next())
            {
                return resultSet.getLong(1);
            }
            return 0;
        }
    }

    @Override
    public TabularData getOverdueTimePerTasks() throws SQLException
    {
        Map<String, Long> overdueTasks = Maps.newHashMap();

        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = statementWithCurrentTime(connection, SELECT_OVERDUE_TRIGGERS);
             ResultSet resultSet = statement.executeQuery())
        {
            if (resultSet != null)
            {
                while (resultSet.next())
                {
                    String taskName = resultSet.getString(1);
                    long overdueTime = resultSet.getLong(2);
                    overdueTasks.put(taskName, overdueTime);
                }
            }
        }

        return OpenTypeAdapter.intoTabularData(overdueTasks);
    }

    private PreparedStatement statementWithCurrentTime(Connection connection, String query)
            throws SQLException
    {
        final PreparedStatement statement = connection.prepareStatement(query);
        statement.setLong(1, System.currentTimeMillis());
        return statement;
    }
}
