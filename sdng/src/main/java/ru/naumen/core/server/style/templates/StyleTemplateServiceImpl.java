package ru.naumen.core.server.style.templates;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.script.spi.ScriptCacheInvalidationHelper;
import ru.naumen.core.server.style.templates.events.BeforeDeleteTemplateEvent;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.shared.style.templates.StyleTemplate;

/**
 * Реализация сервиса для работы с шаблонами стилей.
 *
 * <AUTHOR>
 * @since Dec 12, 2106
 */
@Component
public class StyleTemplateServiceImpl implements StyleTemplateService
{
    @Inject
    private StyleTemplateStorageService storageService;
    @Inject
    private MetainfoServicePersister persister;
    @Inject
    private ApplicationEventPublisher eventPublisher;
    @Inject
    private ScriptCacheInvalidationHelper invalidationHelper;

    @Override
    public void addTemplate(StyleTemplate template)
    {
        StyleTemplate cloned = template.clone();
        cloned.setCreationDate(new Date());
        cloned.setLastModifiedDate(cloned.getCreationDate());
        storageService.addTemplate(cloned);
        persister.persistStyleTemplate(cloned);
    }

    @Override
    public void deleteTemplates(String... codes)
    {
        for (String code : codes)
        {
            StyleTemplate template = getTemplate(code);
            if (null != template)
            {
                eventPublisher.publishEvent(new BeforeDeleteTemplateEvent(template));
                template.getTemplateText().forEach(t -> invalidationHelper.invalidateTemplate(t.getValue()));
            }
        }
        storageService.deleteTemplates(codes);
        Arrays.stream(codes).forEach(c -> persister.deleteStyleTemplate(c));
    }

    @Override
    public List<StyleTemplate> getAll()
    {
        return storageService.getAll().stream().map(t -> t.clone()).collect(Collectors.toList());
    }

    @Override
    public StyleTemplate getTemplate(String code)
    {
        StyleTemplate template = storageService.getTemplate(code);
        return null != template ? template.clone() : null;
    }

    @Override
    public void saveTemplate(StyleTemplate template)
    {
        Objects.requireNonNull(template);
        StyleTemplate old = storageService.getTemplate(template.getCode());
        if (old == null)
        {
            addTemplate(template);
            return;
        }

        StyleTemplate cloned = template.clone();
        if (!ObjectUtils.equals(old.getTemplateText(), cloned.getTemplateText()))
        {
            cloned.setLastModifiedDate(new Date());
            old.getTemplateText().forEach(t -> invalidationHelper.invalidateTemplate(t.getValue()));
        }
        // поле мест использования не хранится в бд, но для кластера нам надо сообщить, что место было добавлено
        if (old.getUsagePoints().size() != cloned.getUsagePoints().size())
        {
            cloned.setLastModifiedDate(new Date());
        }
        storageService.saveTemplate(cloned);
        persister.persistStyleTemplate(cloned);
    }
}