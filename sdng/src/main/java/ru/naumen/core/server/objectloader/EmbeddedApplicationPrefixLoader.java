package ru.naumen.core.server.objectloader;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;

/**
 * <AUTHOR>
 * @since 07.11.16
 */
@Component
public class EmbeddedApplicationPrefixLoader implements IObjectLoader<EmbeddedApplication>
{
    @Inject
    PrefixObjectLoaderRegistryImpl registry;
    @Inject
    EmbeddedApplicationService service;

    @Override
    public EmbeddedApplication get(String uuid)
    {
        String code = UuidHelper.toIdStr(uuid);
        return service.getApplication(code);
    }

    @PostConstruct
    public void init()
    {
        registry.add(EmbeddedApplication.UUID_PREFIX, this);
    }

    @Override
    public EmbeddedApplication load(String uuid)
    {
        return get(uuid);
    }

}
