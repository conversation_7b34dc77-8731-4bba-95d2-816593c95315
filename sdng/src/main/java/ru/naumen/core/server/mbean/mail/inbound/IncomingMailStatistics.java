package ru.naumen.core.server.mbean.mail.inbound;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

/**
 * Реализация MBean {@link IncomingMailStatisticsMBean}
 *
 * <AUTHOR>
 * @since 15.02.2019
 */
@Component
public class IncomingMailStatistics implements IncomingMailStatisticsMBean
{
    private final IncomingMailStatisticsStorage storage;

    @Inject
    public IncomingMailStatistics(IncomingMailStatisticsStorage storage)
    {
        this.storage = storage;
    }

    @Override
    public long getFailed()
    {
        return storage.getFailed();
    }

    @Override
    public long getSuccessful()
    {
        return storage.getSuccessful();
    }

    @Override
    public long getWithIssues()
    {
        return storage.getWithIssues();
    }

    @Override
    public void reset()
    {
        storage.reset();
    }
}
