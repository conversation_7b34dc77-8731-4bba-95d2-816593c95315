/**
 * 
 */
package ru.naumen.core.server.timer.bcp.actions;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.timer.AbstractTimer;
import ru.naumen.core.server.timer.bcp.TimerCalculationContext;
import ru.naumen.core.server.timer.bcp.operations.TimerStatusChangeOperations;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.timer.AbstractTimerDto;

/**
 * <AUTHOR>
 * @since 21.05.2013
 *
 */
@Component
public class TimerStatusChangeActionActivateImpl<T extends AbstractTimer, DT extends AbstractTimerDto> extends
        TimerStatusChangeActionImpl<T, DT>
{
    @Override
    public void execute(TimerCalculationContext<T, DT> timerCalcContext, IHasObjectBOContext<IHasMetaInfo> context,
            TimerStatusChangeOperations<T, DT> operations)
    {
        operations.doActivate(timerCalcContext, context);
    }
}