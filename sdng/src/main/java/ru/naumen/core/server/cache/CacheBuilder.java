package ru.naumen.core.server.cache;

import static ru.naumen.core.server.SpringContext.getInstance;
import static ru.naumen.core.server.cache.CacheBuilderService.CACHE_BUILDER_SERVICE_BEAN;

import java.util.concurrent.TimeUnit;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.cache.RemovalListener;

import edu.umd.cs.findbugs.annotations.CheckForNull;


/**
 * Вариативный Builder для кэша. Если нужен транзакционный кэш, то будет создаваться Infinispan кэш, иначе - Guava Cache.
 *
 * <AUTHOR>
 * @since 17.10.14
 */
public class CacheBuilder<K, V>
{
    private final String cacheName;
    private long expireAfterWriteTime = -1;
    private TimeUnit expireAfterWriteUnit;
    private long expireAfterAccessTime = -1;
    private TimeUnit expireAfterAccessUnit;
    private int maxEntries = -1;
    private boolean isTransactional;
    private RemovalListener<K, V> removalListener = null;

    public CacheBuilder(String cacheName)
    {
        this.cacheName = cacheName;
    }

    /**
     * Создание {@link CacheBuilder}
     */
    public static <K, V> CacheBuilder<K, V> newBuilder(String cacheName)
    {
        return new CacheBuilder<>(cacheName);
    }

    /**
     * Построить кэш
     */
    public Cache<K, V> build()
    {
        return getCacheBuilder().build(this);
    }

    /**
     * Построить кэш с {@link CacheLoader}
     * Впоследствии при обращении по ключу, не присутствующему в кэше
     * будет вызван loader и вычислено значение для этого ключа 
     * и помещено в кэш по данному ключу.
     */
    public LoadingCache<K, V> build(CacheLoader<K, V> loader)
    {
        return getCacheBuilder().build(this, loader);
    }

    /**
     * Построить принудительно Infinispan cache
     */
    public Cache<K, V> buildInfinispanCache()
    {
        return getCacheBuilder().buildInfinispanCache(this, null);
    }

    /**
     * Устанавливает время жизни объекта после обращения к нему
     */
    public CacheBuilder<K, V> expireAfterAccess(long time, TimeUnit timeUnit)
    {
        expireAfterAccessTime = time;
        expireAfterAccessUnit = timeUnit;
        return this;
    }

    /**
     * Устанавливает время жизни объекта после записи объекта в кэш
     */
    public CacheBuilder<K, V> expireAfterWrite(long time, TimeUnit timeUnit)
    {
        expireAfterWriteTime = time;
        expireAfterWriteUnit = timeUnit;
        return this;
    }

    public String getCacheName()
    {
        return cacheName;
    }

    public long getExpireAfterAccessTime()
    {
        return expireAfterAccessTime;
    }

    public TimeUnit getExpireAfterAccessUnit()
    {
        return expireAfterAccessUnit;
    }

    public long getExpireAfterWriteTime()
    {
        return expireAfterWriteTime;
    }

    public TimeUnit getExpireAfterWriteUnit()
    {
        return expireAfterWriteUnit;
    }

    public int getMaxEntries()
    {
        return maxEntries;
    }


    @CheckForNull
    public RemovalListener<K, V> getRemovalListener()
    {
        return removalListener;
    }

    /**
     * Говорит о том, будет ли создаваемый кэш транзакционным.
     *
     * @return true - транзакционный, false - не транзакционный;
     */
    public boolean isTransactional()
    {
        return isTransactional;
    }

    /**
     * Устанавливает максимальное количество объектов в кэше
     */
    public CacheBuilder<K, V> maximumSize(int maxEntries)
    {
        this.maxEntries = maxEntries;
        return this;
    }

    public CacheBuilder<K, V> removalListener(RemovalListener<K, V> removalListener)
    {
        this.removalListener = removalListener;
        return this;
    }

    /**
     * Устанавливает транзакционность кэша.
     *
     * @param isTransactional true - транзакционный, false - не транзакционный;
     */
    public CacheBuilder<K, V> transactional(boolean isTransactional)
    {
        this.isTransactional = isTransactional;
        return this;
    }

    private static CacheBuilderService getCacheBuilder()
    {
        return getInstance().getBean(CACHE_BUILDER_SERVICE_BEAN, CacheBuilderService.class);
    }
}
