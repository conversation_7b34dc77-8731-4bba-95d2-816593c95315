package ru.naumen.core.server.jms.dispatch;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.jms.JMSUtil;
import ru.naumen.core.server.jms.services.JMSQueueService;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.jmsqueue.JMSQueue;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.GetAvailableJMSQueuesForEventAction;

/**
 * Обработчик события запроса доступных очередей для данного ДПС
 * <AUTHOR>
 * @since 27.05.2021
 **/
@Component
public class GetAvailableJMSQueuesForEventActionHandler extends TransactionalReadActionHandler<GetAvailableJMSQueuesForEventAction, SimpleResult<List<DtObject>>>
{
    private final JMSQueueService jmsQueueService;
    private final EventActionService eventActionService;
    private final MetainfoUtils metainfoUtils;

    @Inject
    public GetAvailableJMSQueuesForEventActionHandler(
            JMSQueueService jmsQueueService,
            @Lazy EventActionService eventActionService, MetainfoUtils metainfoUtils)
    {
        this.jmsQueueService = jmsQueueService;
        this.eventActionService = eventActionService;
        this.metainfoUtils = metainfoUtils;
    }

    @Override
    public SimpleResult<List<DtObject>> executeInTransaction(GetAvailableJMSQueuesForEventAction action, ExecutionContext context) throws DispatchException
    {
        final EventAction eventAction = action.getEventAction();
        final ActionType actionType = eventAction.getAction().getActionType();
        final List<DtObject> jmsQueues = new ArrayList<>();
        final boolean isArriveMessageOnQueue = eventAction.getEvent() != null &&
                EventType.arriveMessageOnQueue.equals(eventAction.getEvent().getEventType());

        for(JMSQueue jmsQueue : jmsQueueService.getActiveJMSQueues())
        {
            if (!jmsQueue.isSystem() && jmsQueue.getTypes().contains(actionType) &&
                    (!isArriveMessageOnQueue || isPossibleForArrivedEvents(jmsQueue, eventAction)))
            {
                jmsQueues.add(new SimpleDtObject(jmsQueue.getCode(),
                        metainfoUtils.getLocalizedValue(jmsQueue.getTitle())));
            }
        }

        // Если нет пользовательских очередей - поле не будет доступно
        if (jmsQueues.isEmpty())
        {
            return new SimpleResult<>(Collections.emptyList());
        }

        // если тип события "Поступление собщения в очередь" - то системных быть не должно
        if (isArriveMessageOnQueue)
        {
            return new SimpleResult<>(jmsQueues);
        }

        // если есть пользовательские - нужно первой добавить системную дефолтную
        final String defaultJMSQueueCode = JMSUtil.getDefaultJMSQueue(eventAction);
        if (defaultJMSQueueCode == null)
        {
            return new SimpleResult<>(Collections.emptyList());
        }
        final JMSQueue defaultJMSQueue = jmsQueueService.getJMSQueue(defaultJMSQueueCode);
        if (defaultJMSQueue == null)
        {
            return new SimpleResult<>(Collections.emptyList());
        }
        jmsQueues.add(0, new SimpleDtObject(defaultJMSQueueCode,
                metainfoUtils.getLocalizedValue(defaultJMSQueue.getTitle())));

        return new SimpleResult<>(jmsQueues);
    }

    /**
     * Возможно ли использовать очередь с типом события "Поступление сообщения в очередь"
     * должно соблюдаться два правила:
     * 1. Очередь должна быть скриптовой
     * 2. Не должно быть уже связанных с этой очередью ДПС c данным типом события
     */
    private boolean isPossibleForArrivedEvents(JMSQueue jmsQueue, EventAction eventAction)
    {
        if (!jmsQueue.getTypes().contains(ActionType.ScriptEventAction))
        {
            return false;
        }

        return eventActionService.getEventActions(EventType.arriveMessageOnQueue.name())
                .stream()
                    .filter(e -> !e.equals(eventAction))
                    .noneMatch(e -> jmsQueue.getCode().equals(e.getJmsQueue()));
    }
}