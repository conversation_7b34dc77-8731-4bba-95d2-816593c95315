/**
 * 
 */
package ru.naumen.core.server.timer.bcp.conditionstrategies;

import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.timer.Timer;
import ru.naumen.core.server.timer.bcp.TimerCalculationContext;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.timer.Status;
import ru.naumen.core.shared.timer.definition.TimerCondition;

/**
 * Стратегия определения текущего {@link Timer#getStatus() статуса} счетчика 
 */
public interface TimerConditionStrategy<C extends TimerCondition>
{
    /**
     * @return новый статус счетчика согласно условиям смены статуса
     */
    Status getState(TimerCalculationContext<?, ?> timerCalcContext, IHasObjectBOContext<IHasMetaInfo> context);
}