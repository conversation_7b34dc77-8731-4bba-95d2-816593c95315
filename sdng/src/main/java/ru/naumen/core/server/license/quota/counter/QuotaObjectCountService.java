package ru.naumen.core.server.license.quota.counter;

import java.util.Collection;
import java.util.Map;

import jakarta.annotation.Nullable;

import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Компонент, позволяющий подсчитать количество объектов разных типов.
 * <AUTHOR>
 * @since Feb 11, 2022
 */
public interface QuotaObjectCountService
{
    /**
     * Подсчитывает общее количество объектов указанных типов.
     * @param fqns список типов, объекты которых нужно подсчитать
     * @param onlyActive <code>true</code>, если подсчитывать только активные объекты, иначе <code>false</code>
     * @param limit необязательное верхнее ограничение количества, выше которого можно прекратить подсчет
     * @return общее количество объектов
     */
    long countObjects(Collection<ClassFqn> fqns, boolean onlyActive, @Nullable Long limit);

    /**
     * Подсчитывает количество объектов отдельно по типам.
     * @param fqns список типов, объекты которых нужно подсчитать
     * @param onlyActive <code>true</code>, если подсчитывать только активные объекты, иначе <code>false</code>
     * @return карта, ключом в которой является FQN типа, а значением — количество объектов этого типа
     */
    Map<ClassFqn, Long> countObjectsByFqn(Collection<ClassFqn> fqns, boolean onlyActive);
}
