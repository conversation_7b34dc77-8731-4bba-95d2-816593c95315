package ru.naumen.core.server.common;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.bo.bop.OperationValidator;
import ru.naumen.core.server.cache.transaction.readonly.CachedInReadOnlyTx;
import ru.naumen.core.server.common.attribute.group.AttributeGroupService;
import ru.naumen.core.server.dispatch.HandlerUtils;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.ScriptServiceException;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants.Scripts;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.script.places.OriginService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Реализация {@link Accessor} для вычислимых атрибутов
 *
 * <AUTHOR>
 * @since 06.07.2012
 *
 */
@Component(ComputableAccessor.NAME)
public class ComputableAccessor<T extends IHasMetaInfo> extends SecuredAccessor<T>
{
    private static final Logger LOG = LoggerFactory.getLogger(ComputableAccessor.class);

    public static final String NAME = Constants.Accessors.COMPUTABLE;

    private final DefaultValueHelper defaultValueHelper;
    private final OperationValidator validator;
    private final ResolverUtils resolverUtils;
    private final ScriptService service;
    private final ScriptStorageService scriptStorageService;
    private final SpringContext springContext;
    private final CurrentEmployeeContext currentEmployeeContext;
    private final AttributeGroupService attributeGroupService;
    private final HandlerUtils handlerUtils;
    private final OriginService originService;

    /**
     * Используем через отдельное поле для работы аннотаций кэширования
     */
    private ComputableAccessor<T> valueResolver;

    @Inject
    public ComputableAccessor(DefaultValueHelper defaultValueHelper, OperationValidator validator,
                              ResolverUtils resolverUtils, ScriptService service,
                              ScriptStorageService scriptStorageService, SpringContext springContext,
                              AttributeGroupService attributeGroupService, HandlerUtils handlerUtils,
                              OriginService originService, CurrentEmployeeContext currentEmployeeContext)
    {
        this.defaultValueHelper = defaultValueHelper;
        this.validator = validator;
        this.resolverUtils = resolverUtils;
        this.service = service;
        this.scriptStorageService = scriptStorageService;
        this.springContext = springContext;
        this.attributeGroupService = attributeGroupService;
        this.handlerUtils = handlerUtils;
        this.originService = originService;
        this.currentEmployeeContext = currentEmployeeContext;
    }

    @CachedInReadOnlyTx
    public Object getDefaultValue(Attribute attr)
    {
        Object defaultValue = defaultValueHelper.getDefaultValue(attr, IProperties.EMPTY);
        try
        {
            return resolverUtils.resolvAndValidate(new ResolverContext(attr, defaultValue));
        }
        catch (Exception e)
        {
            LOG.warn("Default value '" + defaultValue + "' of attribute '" + attr.getCode()
                    + "' couldn't be casted to attribute type '" + attr.getType().getCode() + "'", e);
            return null;
        }
    }

    Object getDefaultValueWithResolver(Attribute attr)
    {
        return getValueResolver().getDefaultValue(attr);
    }

    @CachedInReadOnlyTx
    public Object resolveValue(Attribute attr, Object value, @Nullable Integer limit)
    {
        Object result = resolverUtils.resolvAndValidate(new ResolverContext(attr, value), limit);
        validator.checkCatalogItemAttributeOnFolder(attr, result);
        return result;
    }

    @Override
    protected Object internalGet(T object, Attribute attr)
    {
        return internalGet(object, attr, DtoCriteria.NO_LIMIT);
    }

    @Override
    protected Object internalGet(T object, Attribute attr, @Nullable Integer limit)
    {
        if (!isNecessaryToCompute(object, attr))
        {
            return getDefaultValueWithResolver(attr);
        }
        final Script script = scriptStorageService.getScript(attr.getScript());
        Object value;
        try
        {
            if(script == null)
            {
                throw new ScriptServiceException("script is null", true);
            }
            final Map<String, Object> bindings = new HashMap<>(3);
            bindings.put(ScriptService.Constants.SUBJECT, object);
            bindings.put(ScriptService.Constants.ACTION_USER, currentEmployeeContext.getCurrentEmployee());
            bindings.put(ScriptService.Constants.CARD_OBJECT, handlerUtils.getCardObject());
            bindings.put(Scripts.ORIGIN, originService.getOrigin());

            value = authorizeRunner.callWithAllPermission(() -> service.execute(script, bindings));
        }
        catch (ScriptServiceException e)
        {
            LOG.warn("Error in calculation of computable attribute '" + attr.getCode() + "' of metaclass '"
                    + attr.getDeclaredMetaClass() + "'", e);

            return getValueResolver().getDefaultValue(attr);
        }
        try
        {
            value = getValueResolver().resolveValue(attr, value, limit);
        }
        catch (RuntimeException e)
        {
            LOG.warn("Value '" + value + "' of attribute '" + attr.getCode()
                    + "' couldn't be casted to attribute type '" + attr.getType().getCode() + "'", e);
            return getValueResolver().getDefaultValue(attr);
        }
        return value;
    }

    @Override
    protected void internalSet(T object, Attribute attr, @Nullable Object value) throws UnsupportedOperationException
    {
    }

    @SuppressWarnings("unchecked")
    private ComputableAccessor<T> getValueResolver()
    {
        if (valueResolver == null)
        {
            valueResolver = springContext.getBean(ComputableAccessor.NAME, ComputableAccessor.class);
        }
        return valueResolver;
    }

    /**
     * Метод для определения необходимости вычисления вычислимого атрибута.
     *
     * @param object {@link IHasMetaInfo} объект
     * @param attr вычислимый атрибут
     *
     * @return true, если attr подлежит вычислению
     */
    private boolean isNecessaryToCompute(T object, Attribute attr)
    {
        ClassFqn classFqn = object.getMetaClass();
        String attrCode = attr.getCode();
        if (classFqn == null)
        {
            return true;
        }

        String objectCardCaptionAttrCode = attributeGroupService.getObjectCardCaptionAttrCode(classFqn);
        if (attrCode.equals(objectCardCaptionAttrCode))
        {
            return true;
        }

        MetaClass metaClass = metainfoService.getMetaClass(classFqn);
        if (metaClass.isTabTitleAttributeOverridden() && attrCode.equals(metaClass.getTabTitleAttribute()))
        {
            return true;
        }

        Collection<String> relObjectAttrCodes = attributeGroupService.getRelObjectPropAttrCode(classFqn);
        if (relObjectAttrCodes.contains(attrCode))
        {
            return true;
        }

        Collection<String> visibleAttrGroupsOnFormWithCompAttr = attributeGroupService
                .getAttrGroupsUuidsVisibleOnObjectCardWithCompAttr(classFqn, attrCode);
        return !CollectionUtils.isEmpty(visibleAttrGroupsOnFormWithCompAttr);
    }
}
