/**
 * 
 */
package ru.naumen.core.server.timer.bcp.actions;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.timer.BackTimer;
import ru.naumen.core.server.timer.bcp.TimerCalculationContext;
import ru.naumen.core.server.timer.bcp.TimerLoggingHelper;
import ru.naumen.core.server.timer.bcp.operations.TimerStatusChangeOperations;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.timer.BackTimerDto;
import ru.naumen.core.shared.timer.Status;

/**
 * <AUTHOR>
 * @since 21.05.2013
 *
 */
@Component
public class BackTimerStatusChangeActionPassivateSetZeroDeadlineIfExceedImpl extends
        TimerStatusChangeActionImpl<BackTimer, BackTimerDto>
{
    public static final Logger LOG = LoggerFactory.getLogger(BackTimerStatusChangeActionPassivateSetZeroDeadlineIfExceedImpl.class);

    @Override
    public void execute(TimerCalculationContext<BackTimer, BackTimerDto> timerCalcContext,
            IHasObjectBOContext<IHasMetaInfo> context, TimerStatusChangeOperations<BackTimer, BackTimerDto> operations)
    {
        String prefixLogs = TimerLoggingHelper.getPrefixLogs(timerCalcContext.getAttribute(), context);
        operations.doPassivate(timerCalcContext, context);
        if (!Status.EXCEED.equals(timerCalcContext.getTimer().getStatus()))
        {
            LOG.debug("{} Timer status is not EXCEED, set deadline to NULL",
                    prefixLogs);
            timerCalcContext.getTimer().setDeadLineTime(null);
        }
    }
}