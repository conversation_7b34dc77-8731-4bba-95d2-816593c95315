package ru.naumen.core.server.cache.transaction;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;

import jakarta.inject.Inject;
import jakarta.transaction.SystemException;
import jakarta.transaction.Transaction;
import jakarta.transaction.TransactionManager;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.support.SimpleValueWrapper;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.jta.ds.BaseSynchronization;

/**
 * Кэш хранящий элементы в рамках одной транзакции
 * При закрытии транзакции элементы из кэша удаляются
 * 
 * <AUTHOR>
 * @since Apr 22, 2016
 */
public abstract class TransactionCacheBase implements TransactionCache
{
    public class CacheItem
    {
        Transaction tx;
        boolean txCached;
        Map<Object, ValueWrapper> map;

        public CacheItem(Transaction tx, boolean txCached)
        {
            this.tx = tx;
            this.txCached = txCached;
        }
    }

    public static final Logger LOG = LoggerFactory.getLogger(TransactionCacheBase.class);

    private ThreadLocal<CacheItem> cacheItemTL = new ThreadLocal<>();

    @Inject
    private TransactionManager txManager;

    @Override
    public void clear()
    {
        Map<Object, ValueWrapper> map = getMap();
        if (map != null)
        {
            map.clear();
        }
    }

    @Override
    public void evict(Object key)
    {
        Map<Object, ValueWrapper> map = getMap();
        if (map != null)
        {
            map.remove(key);
        }
    }

    @Override
    public ValueWrapper get(Object key)
    {
        Map<Object, ValueWrapper> map = getMap();
        ValueWrapper value = map != null ? map.get(key) : null;
        return value;
    }

    @Override
    @SuppressWarnings({ "unchecked" })
    public <T> T get(Object key, Class<T> type)
    {
        ValueWrapper valueWrapper = get(key);
        Object value = valueWrapper != null ? valueWrapper.get() : null;
        if (value != null && type != null && !type.isInstance(value))
        {
            throw new IllegalStateException("Cached value is not of required type [" + type.getName() + "]: " + value);
        }
        return (T)value;
    }

    @Override
    public <T> T get(Object key, Callable<T> valueLoader)
    {
        throw new UnsupportedOperationException();
    }

    @Override
    public Object getNativeCache()
    {
        return this;
    }

    @Override
    public boolean invalidate()
    {
        boolean wasEmpty = cacheItemTL.get() == null;
        removeCache();
        return wasEmpty;
    }

    @Override
    public void put(Object key, Object value)
    {
        Transaction tx = getTransaction();
        if (null == tx)
        {
            return;
        }

        CacheItem cacheItem = getCacheItem();
        if (cacheItem == null)
        {
            cacheItem = createCacheItem(tx);
            registerSynchronization(tx);
            cacheItemTL.set(cacheItem);
        }

        if (!cacheItem.txCached)
        {
            return;
        }

        if (cacheItem.map == null)
        {
            cacheItem.map = new HashMap<>();
        }
        cacheItem.map.put(key, new SimpleValueWrapper(value));
    }

    @Override
    public ValueWrapper putIfAbsent(Object key, Object value)
    {
        ValueWrapper existingValue = get(key);
        if (existingValue == null)
        {
            put(key, value);
            return null;
        }
        return existingValue;
    }

    protected abstract CacheItem createCacheItem(Transaction tx);

    private CacheItem getCacheItem()
    {
        Transaction tx = getTransaction();
        if (tx == null)
        {
            return null;
        }
        CacheItem cacheItem = cacheItemTL.get();
        if (cacheItem == null || !tx.equals(cacheItem.tx))
        {
            return null;
        }
        return cacheItem;
    }

    private Map<Object, ValueWrapper> getMap()
    {
        CacheItem cacheItem = getCacheItem();
        return cacheItem == null ? null : cacheItem.map;
    }

    private Transaction getTransaction()
    {
        try
        {
            return txManager.getTransaction();
        }
        catch (SystemException e)
        {
            //Не будем использовать кэш
            //Ничего критичного
            LOG.warn(e.getMessage(), e);
        }
        return null;
    }

    private void registerSynchronization(final Transaction tx)
    {
        try
        {
            tx.registerSynchronization(new BaseSynchronization()
            {
                @Override
                public void afterCompletion(int status)
                {
                    removeCache();
                }
            });
        }
        catch (Exception e)
        {
            //Иначе может быть утечка памяти
            removeCache();
            throw new FxException(e);
        }
        catch (Throwable t)
        {
            //Иначе может быть утечка памяти
            removeCache();
            throw t;
        }
    }

    /**
     * Метод призван очистить TL при закрытии транзакции потока.
     * При отработке вложенной транзакции кэш полностью чистится, 
     * так как вложенная транзакция могла что-то поменять, 
     * что было закэшировано в исходной транзакции.
     */
    private void removeCache()
    {
        cacheItemTL.remove();
    }
}
