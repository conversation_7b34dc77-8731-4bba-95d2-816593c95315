/**
 * 
 */
package ru.naumen.core.server.timer.bcp.conditionstrategies;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.core.server.timer.bcp.AbstractTimerCalculationOperation;
import ru.naumen.core.shared.timer.definition.ScriptTimerCondition;
import ru.naumen.core.shared.timer.definition.StatusTimerCondition;
import ru.naumen.core.shared.timer.definition.TimerCondition;
import ru.naumen.core.shared.timer.definition.TimerDefinition;

/**
 * <AUTHOR>
 * @since 21.05.2013
 *
 */
@Component
public class TimerConditionStrategySelector
{
    @Inject
    private TimerConditionStrategyStateImpl stateConditionStrategyInstance;
    @Inject
    private TimerConditionStrategyScriptImpl scriptConditionStrategyInstance;

    public TimerConditionStrategy<? extends TimerCondition> getConditionStrategy(TimerDefinition timerDefinition)
    {
        TimerCondition timerCondition = timerDefinition.getTimerCondition();
        if (AbstractTimerCalculationOperation.LOG.isDebugEnabled())
        {
            AbstractTimerCalculationOperation.LOG.debug("Timer condition is " + timerCondition);
        }
        if (timerCondition instanceof StatusTimerCondition)
        {
            return stateConditionStrategyInstance;
        }
        if (timerCondition instanceof ScriptTimerCondition)
        {
            return scriptConditionStrategyInstance;
        }
        throw new OperationException("Strategy not determined for " + timerCondition);
    }
}