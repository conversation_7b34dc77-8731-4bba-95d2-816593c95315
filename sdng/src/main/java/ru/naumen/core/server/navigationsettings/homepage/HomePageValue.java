package ru.naumen.core.server.navigationsettings.homepage;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.navigationsettings.ReferenceValue;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.homepage.HomePageType;
import ru.naumen.metainfo.shared.sets.HasSettingsSet;
import ru.naumen.metainfo.shared.tags.HasTags;

/**
 * Серверный объект, предназначенный для хранения настроек домашней стрнаницы
 *
 * <AUTHOR>
 * @since 25.01.2023
 */
@XmlType(name = "HomePage")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlSeeAlso(ReferenceValue.class)
public class HomePageValue implements HasTags, IUUIDIdentifiable, HasSettingsSet
{
    @Serial
    private static final long serialVersionUID = -8466111466999244303L;

    private String title;
    private String uuid;
    private String content;
    private HomePageType type;
    private String objectClass;
    @Nullable
    private List<String> tags;
    @Nullable
    private List<String> profiles;
    @Nullable
    private Set<String> objectCases;
    private String referenceCardType;
    private String customLink;
    private String settingsSet;

    /**
     * Ссылка на карточку объекта.<br>
     * Хранит fqn класса, список вкладок и атрибут связи.
     */
    @Nullable
    private ReferenceValue reference;

    public HomePageValue()
    {
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null)
        {
            return false;
        }
        if (!(obj instanceof IUUIDIdentifiable other))
        {
            return false;
        }
        return ObjectUtils.equals(getUUID(), other.getUUID());
    }

    @Override
    public int hashCode()
    {
        if (uuid == null)
        {
            return UuidHelper.hashCode(0);
        }
        if (UuidHelper.isValid(uuid))
        {
            long id = UuidHelper.toId(uuid);
            return UuidHelper.hashCode(id);
        }
        return uuid.hashCode();
    }

    @XmlAttribute(required = true)
    @Override
    public String getUUID()
    {
        return uuid;
    }

    public void setUUID(String uuid)
    {
        this.uuid = uuid;
    }

    @XmlElementWrapper
    @XmlElement(name = "profile")
    public List<String> getProfiles()
    {
        if (profiles == null)
        {
            profiles = new ArrayList<>();
        }
        return profiles;
    }

    public void setObjectCases(@Nullable Set<String> objectCases)
    {
        this.objectCases = objectCases;
    }

    @XmlElementWrapper
    @XmlElement(name = "objectCase")
    @Nullable
    public Set<String> getObjectCases()
    {
        return objectCases;
    }

    public void setProfiles(@Nullable List<String> profiles)
    {
        this.profiles = profiles;
    }

    @XmlElementWrapper
    @XmlElement(name = "tag")
    public List<String> getTags()
    {
        if (null == tags)
        {
            tags = new ArrayList<>();
        }
        return tags;
    }

    @Override
    @XmlElement(name = "set")
    public @Nullable String getSettingsSet()
    {
        return settingsSet;
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet)
    {
        this.settingsSet = settingsSet;
    }

    @XmlElement
    public String getContent()
    {
        return content;
    }

    public void setContent(String content)
    {
        this.content = content;
    }

    @XmlElement
    public HomePageType getType()
    {
        return type;
    }

    public void setType(HomePageType type)
    {
        this.type = type;
    }

    @XmlElement
    public String getObjectClass()
    {
        return objectClass;
    }

    public void setObjectClass(String objectClass)
    {
        this.objectClass = objectClass;
    }

    @XmlElement
    public String getReferenceCardType()
    {
        return referenceCardType;
    }

    public void setReferenceCardType(String referenceCardType)
    {
        this.referenceCardType = referenceCardType;
    }

    @XmlElement
    @Nullable
    public ReferenceValue getReference()
    {
        return reference;
    }

    public void setReference(@Nullable ReferenceValue reference)
    {
        this.reference = reference;
    }

    @XmlElement
    public String getTitle()
    {
        return title;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    @XmlElement
    public String getCustomLink()
    {
        if (customLink == null)
        {
            customLink = StringUtilities.EMPTY;
        }
        return customLink;
    }

    public void setCustomLink(@Nullable String customLink)
    {
        this.customLink = customLink;
    }
}
