package ru.naumen.core.server.mbean.license;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.security.core.session.SessionInformation;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpSession;
import ru.naumen.core.server.bo.employee.EmployeeAuthInfoService;
import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.license.conf.AbstractLicenseGroup;
import ru.naumen.core.server.script.api.IEmployeeApi;
import ru.naumen.sec.server.users.UserPrincipal;
import ru.naumen.sec.server.session.SessionInfoBase;

/**
 * Компонент, который предоставляет необходимую информацию о сессиях
 */
@Component
public class LicenseStatisticsStorage
{
    private final LicensingService licensingService;
    private final EmployeeAuthInfoService employeeAuthInfoService;
    private final IEmployeeApi employeeApi;

    private static final String PORTAL = "REST,JWT";

    @Inject
    public LicenseStatisticsStorage(LicensingService licensingService, EmployeeAuthInfoService employeeAuthInfoService,
            IEmployeeApi employeeApi)
    {
        this.licensingService = licensingService;
        this.employeeAuthInfoService = employeeAuthInfoService;
        this.employeeApi = employeeApi;
    }

    /**
     * Получение количества лицензий каждого вида (всего)
     *
     * @return <код лицензии, количество>
     */
    Map<String, Long> getLicenseGroupsByCode()
    {
        return licensingService.getLicenseGroups()
                .stream()
                .collect(Collectors.
                        toMap(AbstractLicenseGroup::getId, g -> (long)g.getCount()));
    }

    /**
     * Подсчет количества активных сессий каждого вида
     *
     * @return <тип сессии, количество>
     */
    public Map<String, Long> getSessions()
    {
        List<SessionInfoBase> allSession = getActiveSessions();

        long noKeySession = allSession.stream()
                .filter(session -> session.getAccessKeyUuid() == null)
                .count();
        long portalSession = countPortalUsers();

        long keySession = allSession.stream()
                .filter(session -> session.getAccessKeyUuid() != null)
                .count();

        Map<String, Long> sessions = Maps.newHashMap();
        sessions.put("login", noKeySession - portalSession);
        sessions.put("portal", portalSession);
        sessions.put("access_key", keySession);

        return sessions;
    }

    /**
     * Получение информации об использовании лицензий по сессиям
     *
     * @return <тип лицензии, количество сессий>
     */
    Map<String, Long> getLicenseUsagePerSession()
    {
        final Set<String> apiUsage = employeeApi.getLicensesUsage().keySet();
        return getActiveSessions()
                .stream()
                .map(SessionInfoBase::getActualLicenses)
                .flatMap(s -> new ArrayList<>(s).stream())
                .filter(apiUsage::contains)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
    }

    /**
     * Получение информации об использовании лицензий по пользователям
     *
     * @return <тип лицензии, количество пользователей>
     */
    Map<String, Long> getLicenseUsagePerUser()
    {
        final Set<String> apiUsage = employeeApi.getLicensesUsage().keySet();
        return getUniqueUsersSessions()
                .stream()
                .map(SessionInfoBase::getActualLicenses)
                .flatMap(s -> new ArrayList<>(s).stream())
                .filter(apiUsage::contains)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
    }

    /**
     * Получение всех активных сессий
     *
     * @return список сессий
     */
    private List<SessionInfoBase> getActiveSessions()
    {
        return employeeAuthInfoService.getAllSessions();
    }

    /**
     * Подсчет сотрудников, работаюших в Портале
     *
     * @return количество сотрудников
     */
    private long countPortalUsers()
    {
        List<String> sessionIds = getActiveSessions()
                .stream()
                .map(SessionInformation::getSessionId)
                .toList();

        return sessionIds.stream().filter(sessionId ->
        {
            /*
            Вероятно, информацию что сессия относится к порталу можно унести в SessionInfo.
            После этого можно удалить *.mbean.license.SessionListener, в котором хранятся прям сессии целиком.
            В итоге все сессии хранятся в приложении трижды:
            1. Контейнер (или встроенный репозиторий сессий)
            2. Счетчик от JavaMelody
            3. *.mbean.license.SessionListener

            3 нужен только для того, чтобы взять атрибут у сессии и сделать на основе его какие-то выводы.
             */
            HttpSession session = SessionListener.getSessionById(sessionId);
            if (session == null)
            {
                return false;
            }
            return PORTAL.equals(session.getAttribute("aud"));
        }).count();
    }

    /**
     * Получение активных сессий по уникальным сотрудникам
     *
     * @return список сессий
     */
    private List<SessionInfoBase> getUniqueUsersSessions()
    {
        List<UserPrincipal> uniquePrincipals = Lists.newArrayList();
        List<SessionInfoBase> activeSessions = Lists.newArrayList();

        getActiveSessions().forEach(session ->
        {
            final Object principal = session.getPrincipal();

            if (principal instanceof UserPrincipal p && !uniquePrincipals.contains(principal))
            {
                uniquePrincipals.add(p);
                activeSessions.add(session);
            }
        });

        return activeSessions;
    }

}
