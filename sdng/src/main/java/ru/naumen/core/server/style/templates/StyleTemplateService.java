package ru.naumen.core.server.style.templates;

import java.util.List;

import ru.naumen.metainfo.shared.style.templates.StyleTemplate;

/**
 * Сервис для работы с шаблонами стилей.
 * <AUTHOR>
 * @since Nov 30, 2016
 */
public interface StyleTemplateService
{
    /**
     * Сохраняет новый шаблон стилей. Проверяет код шаблона на уникальность.
     * @param template шаблон стилей
     */
    void addTemplate(StyleTemplate template);
    
    /**
     * Удаляет шаблоны стилей с указанными кодами.
     * @param codes коды шаблонов стилей
     */
    void deleteTemplates(String... codes);

    /**
     * Возвращает все имеющиеся в системе шаблоны стилей.
     * @return все шаблоны стилей
     */
    List<StyleTemplate> getAll();

    /**
     * Возвращает шаблон стилей, имеющий указанный код.
     * @param code код шаблона
     * @return шаблон стилей или null, если шаблона с указанным кодом не существует
     */
    StyleTemplate getTemplate(String code);

    /**
     * Сохраняет указанный шаблон стилей.
     * @param template шаблон стилей
     */
    void saveTemplate(StyleTemplate template);
}
