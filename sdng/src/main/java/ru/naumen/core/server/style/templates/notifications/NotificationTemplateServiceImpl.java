package ru.naumen.core.server.style.templates.notifications;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.filestorage.FileUtils;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.templates.TemplateService;
import ru.naumen.core.server.style.templates.StyleTemplateService;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.style.templates.StyleTemplate;

/**
 * Реализация сервиса генерации оповещений по шаблону.
 * <AUTHOR>
 * @since Dec 21, 2016
 */
@Component
public class NotificationTemplateServiceImpl implements NotificationTemplateService
{
    private static final String IMG_REPLACE = "<#replace#>";
    @Inject
    private ScriptService scriptService;
    @Inject
    private StyleTemplateService styleTemplateService;
    @Inject
    private MetainfoUtils metainfoUtils;

    @Override
    public String generate(String content, @Nullable String templateCode, Map<String, Object> initialBindings)
    {
        StyleTemplate template = null == templateCode ? null : styleTemplateService.getTemplate(templateCode);
        return generate(content, template, initialBindings);
    }

    @Override
    public String generate(String content, @Nullable StyleTemplate template, Map<String, Object> initialBindings)
    {
        String notificationContent = prepareBody(content, initialBindings);
        if (null == template)
        {
            return notificationContent;
        }
        initialBindings.put(ScriptService.Constants.CONTENT_PARAM, notificationContent);
        try
        {
            return prepareBody(metainfoUtils.getLocalizedValue(template.getTemplateText()), initialBindings);
        }
        catch (FxException e)
        {
            throw new NotificationTemplateException(e.getMessage(), e);
        }
    }

    private String prepareBody(String content, Map<String, Object> initialBindings)
    {
        List<String> contentImgs = Lists.newArrayList();
        String prepareContentWithoutImg = cutImg(contentImgs, content);
        String prepareContent = scriptService.runScriptTemplate(prepareContentWithoutImg, initialBindings);
        return pastImg(contentImgs, prepareContent);
    }

    public static String cutImg(List<String> notificationImgs, String content)
    {
        if (content.length() < TemplateService.MAX_TEMPLATE_LENGTH)
        {
            return content;
        }
        String notification = content;

        Document document = Jsoup.parseBodyFragment(content);
        document.select("img").stream().map(Element::toString).forEach(notificationImgs::add);
        Matcher matcher = FileUtils.CUT_IMG.matcher(notification);
        notification = matcher.replaceAll(IMG_REPLACE);

        return notification;
    }

    public static String pastImg(List<String> notificationImgs, String notificationContent)
    {
        for (String img : notificationImgs)
        {
            notificationContent = notificationContent.replaceFirst(IMG_REPLACE, img);
        }
        return notificationContent;
    }
}
