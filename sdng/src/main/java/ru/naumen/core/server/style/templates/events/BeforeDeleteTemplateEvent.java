package ru.naumen.core.server.style.templates.events;

import org.springframework.context.ApplicationEvent;

import ru.naumen.metainfo.shared.style.templates.StyleTemplate;

/**
 * Событие, вызываемое перед удалением шаблона стилей.
 * <AUTHOR>
 * @since Jan 9, 2017
 */
public class BeforeDeleteTemplateEvent extends ApplicationEvent
{
    private static final long serialVersionUID = 1162170976173300676L;
    
    public BeforeDeleteTemplateEvent(StyleTemplate template)
    {
        super(template);
    }

    public StyleTemplate getTemplate()
    {
        return (StyleTemplate)getSource();
    }
}
