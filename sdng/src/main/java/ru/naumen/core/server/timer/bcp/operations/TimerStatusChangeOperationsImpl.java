/**
 * 
 */
package ru.naumen.core.server.timer.bcp.operations;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.timer.AbstractTimer;
import ru.naumen.core.server.timer.TimerUtils;
import ru.naumen.core.server.timer.bcp.TimerCalculationContext;
import ru.naumen.core.server.timer.bcp.TimerLoggingHelper;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.timer.AbstractTimerDto;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.core.shared.timer.definition.TimerDefinition.TimeMetric;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * <AUTHOR>
 * @since 21.05.2013
 *
 */
public abstract class TimerStatusChangeOperationsImpl<T extends AbstractTimer, DT extends AbstractTimerDto> implements
        TimerStatusChangeOperations<T, DT>
{
    public static final Logger LOG = LoggerFactory.getLogger(TimerStatusChangeOperationsImpl.class);

    @Inject
    protected TimerUtils timerUtils;
    @Inject
    protected AccessorHelper accessorHelper;

    @Override
    public DT getNewAttributeValue(TimerCalculationContext<T, DT> timerCalcContext,
            IHasObjectBOContext<IHasMetaInfo> context)
    {
        IHasMetaInfo object = context.getObject();
        return accessorHelper.getAttributeValueWithoutPermission(object, timerCalcContext.getAttribute().getCode());
    }

    @Override
    public boolean isCanStart(TimerDefinition timerDefinition, IHasObjectBOContext<IHasMetaInfo> context)
    {
        if (null == getAttributeValue(timerDefinition.getTimeZoneAttribute(), context))
        {
            return false;
        }
        if (TimeMetric.ASTRO.equals(timerDefinition.getTimeMetric()))
        {
            return true;
        }
        return null != getAttributeValue(timerDefinition.getServiceTimeAttribute(), context);
    }

    @Override
    public boolean isChanged(TimerCalculationContext<T, DT> timerCalcContext)
    {
        return !ObjectUtils.equals(timerCalcContext.getCurrentStatus(), timerCalcContext.getNewStatus());
    }

    @Override
    public void logChange(TimerCalculationContext<T, DT> timerCalcContext, IHasObjectBOContext<IHasMetaInfo> context)
    {
        String prefixLogs = TimerLoggingHelper.getPrefixLogs(timerCalcContext.getAttribute(), context);
        if (!ObjectUtils.equals(timerCalcContext.getCurrentStatus(), timerCalcContext.getNewStatus()))
        {
            if (LOG.isDebugEnabled())
            {
                LOG.debug("{} Timer change status {} => {}", prefixLogs,
                        timerCalcContext.getCurrentStatus(), timerCalcContext.getNewStatus()) ;
            }
            timerCalcContext.getTimer().setStatus(timerCalcContext.getNewStatus());
        }
    }

    protected <V> V getAttributeValue(String attribute, IHasObjectBOContext<IHasMetaInfo> context)
    {
        IHasMetaInfo object = context.getObject();
        return accessorHelper.<V> getAttributeValueWithoutPermission(object, attribute);
    }
}