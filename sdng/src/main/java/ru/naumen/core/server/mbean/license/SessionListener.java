package ru.naumen.core.server.mbean.license;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.servlet.http.HttpSession;
import jakarta.servlet.http.HttpSessionEvent;
import jakarta.servlet.http.HttpSessionListener;

/**
 * Реализация {@link HttpSessionListener} взята у класса net.bull.javamelody.SessionListener,
 * чтобы иметь возможность сделать метод getSessionById доступным для использования
 *
 * Хранит в себе Map [sessionId : session] и позволяет получить по sessionId сессию
 *
 * <AUTHOR>
 * @since 06.03.2019
 */
public class SessionListener implements HttpSessionListener, java.io.Serializable
{
    private static final ConcurrentMap<String, HttpSession> SESSION_MAP_BY_ID = new ConcurrentHashMap<>();

    @Override
    public void sessionCreated(final HttpSessionEvent event)
    {
        final HttpSession session = event.getSession();

        SESSION_MAP_BY_ID.put(session.getId(), session);
    }

    @Override
    public void sessionDestroyed(final HttpSessionEvent event)
    {
        final HttpSession session = event.getSession();

        final HttpSession removedSession = SESSION_MAP_BY_ID.remove(session.getId());
        if (removedSession == null) {
            // In some cases (issue 473), Tomcat changes id in session withtout calling sessionCreated.
            // In servlet 3.1, HttpSessionIdListener.sessionIdChanged could be used.
            removeSessionsWithChangedId();
        }
    }

    /**
     * Удаление всех сессий, в которых изменился идентификатор сессии без вызова sessionCreated
     */
    private static void removeSessionsWithChangedId() {
        for (final Map.Entry<String, HttpSession> entry : SESSION_MAP_BY_ID.entrySet()) {
            final String id = entry.getKey();
            final HttpSession other = entry.getValue();
            if (!id.equals(other.getId())) {
                SESSION_MAP_BY_ID.remove(id);
            }
        }
    }

    /**
     * Получение HTTP-сессии по идентификатору, может вернуть null.
     * Например, сессия, которая создаётся для JWT в МК не привязана к HTTP-сессии
     *
     * @param sessionId идентификатор сессии
     * @return сессия или null
     */
    @CheckForNull
    public static HttpSession getSessionById(String sessionId)
    {
        final HttpSession session = SESSION_MAP_BY_ID.get(sessionId);
        if (session == null)
        {
            for (final HttpSession other : SESSION_MAP_BY_ID.values())
            {
                if (other.getId().equals(sessionId))
                {
                    return other;
                }
            }
        }
        return session;
    }

}
