package ru.naumen.core.server.mbean.notification;

/**
 * Интерфейс mbean: получение информации по отправленным оповещениям.
 *
 * <AUTHOR>
 * @since 15.02.2019
 */
public interface NotificationStatisticsMBean
{
    /**
     * Получить количество удачно отправленных оповещений
     * с момента запуска приложения или с момента последнего сброса статистики
     *
     * @return количество оповещений
     */
    long getSendSuccessful();

    /**
     * Получить количество оповещений, которые должны были отправиться, но не были отправлены по некоторой причине,
     * с момента запуска приложения или с момента последнего сброса статистики
     *
     * @return количество оповещений
     */
    long getSendFailed();

    /**
     * Получить количество оповещений, которые не были отправлены из-за некорректных адресов,
     * с момента запуска приложения или с момента последнего сброса статистики
     *
     * @return количество оповещений
     */
    long getInvalidEmails();

    /**
     * Количество сбоев у первой быстрой отправки оповещения
     *
     * @return количество оповещений
     */
    long getAttemptFailed();

    /**
     * Количество оповещений, которые отправлены только для части адресов
     *
     * @return количество оповещений
     */
    long getSendFailedPartially();

    /**
     * Количество оповещений, которые не отправились, т.к. у получателя не был указан email адрес
     *
     * @return количество оповещений
     */
    long getSendFailedEmailNotExist();

    /**
     * Количество оповещений, которые не отправились на адреса указаные в параметрах подключения серверов входящей почты.
     *
     * @return количество оповещений
     */
    long getSendFailedSystemEmail();

    /**
     * Сбросить всю статистику
     */
    void reset();
}
