/**
 * 
 */
package ru.naumen.core.server.timer.bcp.operations;

import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.timer.AbstractTimer;
import ru.naumen.core.server.timer.bcp.TimerCalculationContext;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.timer.AbstractTimerDto;
import ru.naumen.core.shared.timer.Status;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Набор операций по изменению состояний счетчика времени. {@link AbstractTimer}
 * 
 * <AUTHOR>
 * @since 21.05.2013
 *
 */
public interface TimerStatusChangeOperations<T extends AbstractTimer, DT extends AbstractTimerDto>
{
    T createTimer(Attribute attribute);

    void doActivate(TimerCalculationContext<T, DT> timerCalcContext, IHasObjectBOContext<IHasMetaInfo> context);

    void doPassivate(TimerCalculationContext<T, DT> timerCalcContext, IHasObjectBOContext<IHasMetaInfo> context);

    DT getNewAttributeValue(TimerCalculationContext<T, DT> timerCalcContext, IHasObjectBOContext<IHasMetaInfo> context);

    Status getNewStatus(TimerCalculationContext<T, DT> timerCalcContext);

    DT getOldAttributeValue(TimerCalculationContext<T, DT> timerCalcContext, IHasObjectBOContext<IHasMetaInfo> context);

    boolean isCanStart(TimerDefinition timerDefinition, IHasObjectBOContext<IHasMetaInfo> context);

    boolean isChanged(TimerCalculationContext<T, DT> timerCalcContext);

    void logChange(TimerCalculationContext<T, DT> timerCalcContext, IHasObjectBOContext<IHasMetaInfo> context);
}