package ru.naumen.core.server.possiblevalues.extractors.businessobject;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BACK_LINK_TYPE_CODE;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BO_LINKS_TYPE_CODE;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BO_LINK_TYPE_CODE;

import java.util.List;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.possiblevalues.businessobject.BusinessObjectPossibleValue;
import ru.naumen.core.server.possiblevalues.businessobject.BusinessObjectPossibleValuesListExtractor;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.server.utils.AttributeHelper;

/**
 * Реализация получения списка возможных значений и поиска по нему для атрибутов ссылочных типов:
 * <ul>
 *     <li>Ссылка на БО</li>
 *     <li>Набор ссылок на БО</li>
 *     <li>Обратная ссылка</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 20.04.2024
 */
@Component
public class BusinessObjectPossibleValuesListExtractorImpl
        extends AbstractLinksPossibleValuesListExtractor<BusinessObjectPossibleValue>
        implements BusinessObjectPossibleValuesListExtractor
{
    private static final List<String> ATTR_TYPES = List.of(BO_LINK_TYPE_CODE, BO_LINKS_TYPE_CODE,
            BACK_LINK_TYPE_CODE);

    @Inject
    public BusinessObjectPossibleValuesListExtractorImpl(Dispatch dispatch, AttributeHelper attributeHelper)
    {
        super(dispatch, attributeHelper);
    }

    @Override
    public List<String> getAttributeTypes()
    {
        return ATTR_TYPES;
    }

    @Override
    protected BusinessObjectPossibleValue convertToElement(final DtObject dto)
    {
        return new BusinessObjectPossibleValueImpl(dto.getUUID(), dto.getMetaClass(), dto.getTitle());
    }
}
