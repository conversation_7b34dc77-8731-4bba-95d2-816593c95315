package ru.naumen.core.server.timer.bcp.conditionstrategies;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.timer.definition.MetaClassState;
import ru.naumen.core.shared.timer.definition.StatusTimerCondition;
import ru.naumen.core.shared.timer.definition.TimerCondition;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.wf.State;

import com.google.common.collect.Collections2;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import com.googlecode.functionalcollections.FunctionalIterables;

/**
 * Формирует данные для клиента (набор фактически выбранных статусов в полях "учитывать" и "останавливать" и их строковое представление)
 * Или восстанавливает данные из фактических, в более краткой форме за счет наследования, для сохранения в базе 
 * (в базе хранятся узлы, все потомки которых либо полностью выбраны, либо полностью не выбраны)
 * <AUTHOR>
 * @since 04 дек. 2015 г.
 *
 */
@Component
public class TimerConditionConverter
{
    @Inject
    private MetainfoService metainfoService;

    /**
     * Сформировать данные условия запуска счетчика по смене статуса
     * (развернуть набор значений, учитывая наследование)
     */
    public void updateActualStateInformation(TimerCondition condition)
    {
        if (!(condition instanceof StatusTimerCondition))
        {
            return;
        }

        StatusTimerCondition statusTimerCondition = (StatusTimerCondition)condition;
        ClassFqn fqnOfClass = getFqnOfClassFromServer(statusTimerCondition);
        if (fqnOfClass == null)
        {
            return;
        }
        statusTimerCondition.getActualSelected().clear();
        statusTimerCondition.getActualStop().clear();
        updateStatesForClient(Collections.<String> emptySet(), Collections.<String> emptySet(), fqnOfClass,
                statusTimerCondition);
        statusTimerCondition.setSelectedStatesAsString(convertStatesToString(statusTimerCondition.getActualSelected()));
        statusTimerCondition.setStopStatesAsString(convertStatesToString(statusTimerCondition.getActualStop()));
    }

    /**
     * Сформировать данные условия запуска счетчика по смене статуса для сервера по данным с клиента
     * (свернуть набор значений, используя наследование)
     */
    public void updateClientToServer(TimerCondition condition)
    {
        if (!(condition instanceof StatusTimerCondition))
        {
            return;
        }
        StatusTimerCondition statusTimerCondition = (StatusTimerCondition)condition;
        ClassFqn fqnOfClass = getFqnOfClassFromClient(statusTimerCondition);
        if (fqnOfClass == null)
        {
            return;
        }
        statusTimerCondition.getSelected().clear();
        statusTimerCondition.getDenied().clear();
        statusTimerCondition.getStopStates().clear();
        statusTimerCondition.getStopDenied().clear();
        updateStateForServer(Collections.<String> emptySet(), Collections.<String> emptySet(), fqnOfClass,
                statusTimerCondition);
    }

    /**
     * Генерит строку, которая выводится в полях "Учитывать время в статусах" и "Останавливать счетчик в статусах" на карточке счетчика времени
     * 
     * Статусы сгруппированы по типу и сформированы по правилу:
     * "%[название типа]%: % список выбранных статусов через запятую%",
     * список статусов отсортирован согласно последовательности статусов в типе;
     * Список статусов каждого типа начинается с новой строки, сортировка лексикографическая по названию типа;
     * Если в нескольких типах идентичен набор выбранных статусов (их названия и порядок совпадают),
     * то эти типы так же группируются: "%[название типа1], [название типа2]%: % список выбранных статусов через запятую%";
     */
    private String convertStatesToString(ArrayList<MetaClassState> states)
    {
        //Формируем соответствие <метакласс> => <набор его статусов>
        Multimap<ClassFqn, String> typeToStateCodes = HashMultimap.create();
        for (MetaClassState state : states)
        {
            typeToStateCodes.put(state.getMetaClass(), state.getState());
        }

        //Превращаем набор статусов в строку и переворачиваем мапу, чтобы метаклассы с одинаковыми статусами оказались вместе
        Multimap<String, ClassFqn> stateTitlesToType = HashMultimap.create();
        for (ClassFqn fqn : typeToStateCodes.keySet())
        {
            Collection<String> selectedStates = typeToStateCodes.get(fqn);
            List<String> orderedStateTitles = Lists.newArrayList();
            for (State state : metainfoService.getMetaClass(fqn).getWorkflow().getStates())
            {
                if (selectedStates.contains(state.getCode()))
                {
                    orderedStateTitles.add(state.getTitle());
                }
            }
            stateTitlesToType.put(StringUtilities.join(orderedStateTitles), fqn);
        }

        /*
         * Если в нескольких типах идентичен набор выбранных статусов (их названия и порядок совпадают),
         * то эти типы так же группируются (объединенные типы находятся выше одиночных в коллекции):
         * "[название типа1], [название типа2]" => упорядоченное множество выбранных статусов
         */
        List<String> resultStrings = Lists.newArrayList();
        for (String stateTitles : stateTitlesToType.keySet())
        {
            List<String> metaClassTitles = Lists.newArrayList();
            for (ClassFqn fqn : stateTitlesToType.get(stateTitles))
            {
                metaClassTitles.add(String.format("[%s]", metainfoService.getMetaClass(fqn).getTitle()));
            }
            //Упорядочиваем список типов в рамках одной строки в алфавитном порядке
            Collections.sort(metaClassTitles);
            String typeTitles = StringUtilities.join(metaClassTitles);
            resultStrings.add(String.format("%s : %s", typeTitles, stateTitles));
        }
        //Упорядочиваем все строки в алфавитном порядке (=> по названию первого типа в них)
        Collections.sort(resultStrings);
        return StringUtilities.join(resultStrings, "<br>");
    }

    private ClassFqn getFqnOfClassFromClient(StatusTimerCondition condition)
    {
        if (!condition.getActualSelected().isEmpty())
        {
            return condition.getActualSelected().iterator().next().getMetaClass().fqnOfClass();
        }
        if (!condition.getActualStop().isEmpty())
        {
            return condition.getActualStop().iterator().next().getMetaClass().fqnOfClass();
        }
        return null;
    }

    private ClassFqn getFqnOfClassFromServer(StatusTimerCondition condition)
    {
        if (!condition.getSelected().isEmpty())
        {
            return condition.getSelected().iterator().next().getMetaClass().fqnOfClass();
        }
        if (!condition.getStopStates().isEmpty())
        {
            return condition.getStopStates().iterator().next().getMetaClass().fqnOfClass();
        }
        return null;
    }

    //Вычисляем изменения набора статусов для очередного типа-потомка
    //Смотрим параллельно по двум деревьям - "Учитывать время в статусах" и "Останавливать счетчик в статусах"
    //Добавляем в объект condition те статусы, выборка которых изменилась в текущем типе
    private void updateStateForServer(Set<String> inheritedSelectedStates, Set<String> inheritedStopStates,
            ClassFqn fqn, StatusTimerCondition condition)
    {
        Set<String> selectedInMetaClass = FunctionalIterables.make(condition.getActualSelected())
                .filter(new MetaClassState.MetaClassStateFqnFilter(fqn)).transform(MetaClassState.STATE_EXTRACTOR)
                .toSet();
        Set<String> stopStatesInMetaClass = FunctionalIterables.make(condition.getActualStop())
                .filter(new MetaClassState.MetaClassStateFqnFilter(fqn)).transform(MetaClassState.STATE_EXTRACTOR)
                .toSet();
        Set<String> selectedAddedInThisMetaClass = Sets.difference(selectedInMetaClass, inheritedSelectedStates);
        Set<String> selectedDeniedInThisMetaClass = Sets.difference(inheritedSelectedStates, selectedInMetaClass);
        Set<String> stopStatesAddedInThisMetaClass = Sets.difference(stopStatesInMetaClass, inheritedStopStates);
        Set<String> stopStatesDeniedInThisMetaClass = Sets.difference(inheritedStopStates, stopStatesInMetaClass);
        condition.getSelected().addAll(
                Collections2.transform(selectedAddedInThisMetaClass,
                        new MetaClassState.MetaClassStateWithNewFqnFromStateCodeFactory(fqn)));
        condition.getDenied().addAll(
                Collections2.transform(selectedDeniedInThisMetaClass,
                        new MetaClassState.MetaClassStateWithNewFqnFromStateCodeFactory(fqn)));
        condition.getStopStates().addAll(
                Collections2.transform(stopStatesAddedInThisMetaClass,
                        new MetaClassState.MetaClassStateWithNewFqnFromStateCodeFactory(fqn)));
        condition.getStopDenied().addAll(
                Collections2.transform(stopStatesDeniedInThisMetaClass,
                        new MetaClassState.MetaClassStateWithNewFqnFromStateCodeFactory(fqn)));

        for (ClassFqn child : metainfoService.getMetaClass(fqn).getChildren())
        {
            updateStateForServer(selectedInMetaClass, stopStatesInMetaClass, child, condition);
        }
    }

    //Вычисляем состояние набора статусов для очередного типа-потомка
    //Смотрим параллельно по двум деревьям - "Учитывать время в статусах" и "Останавливать счетчик в статусах"
    private void updateStatesForClient(Set<String> inheritedSelectedStates, Set<String> inheritedStopStates,
            ClassFqn fqn, StatusTimerCondition condition)
    {
        MetaClass metaClass = metainfoService.getMetaClass(fqn);

        Set<String> statesSelectedInMetaclass = FunctionalIterables.make(condition.getSelected())
                .filter(new MetaClassState.MetaClassStateFqnFilter(fqn)).transform(MetaClassState.STATE_EXTRACTOR)
                .toSet();
        Set<String> statesSelectedDeniedInMetaclass = FunctionalIterables.make(condition.getDenied())
                .filter(new MetaClassState.MetaClassStateFqnFilter(fqn)).transform(MetaClassState.STATE_EXTRACTOR)
                .toSet();
        Set<String> selectedStates = Sets.newHashSet(inheritedSelectedStates);
        selectedStates.addAll(statesSelectedInMetaclass);
        selectedStates.removeAll(statesSelectedDeniedInMetaclass);
        condition.getActualSelected().addAll(
                Collections2.transform(selectedStates, new MetaClassState.MetaClassStateWithNewFqnFromStateCodeFactory(
                        fqn)));

        Set<String> stopStatesInMetaclass = FunctionalIterables.make(condition.getStopStates())
                .filter(new MetaClassState.MetaClassStateFqnFilter(fqn)).transform(MetaClassState.STATE_EXTRACTOR)
                .toSet();
        Set<String> stopStatesDeniedInMetaclass = FunctionalIterables.make(condition.getStopDenied())
                .filter(new MetaClassState.MetaClassStateFqnFilter(fqn)).transform(MetaClassState.STATE_EXTRACTOR)
                .toSet();
        Set<String> stopStates = Sets.newHashSet(inheritedStopStates);
        stopStates.addAll(stopStatesInMetaclass);
        stopStates.removeAll(stopStatesDeniedInMetaclass);
        condition.getActualStop().addAll(
                Collections2
                        .transform(stopStates, new MetaClassState.MetaClassStateWithNewFqnFromStateCodeFactory(fqn)));

        for (ClassFqn child : metaClass.getChildren())
        {
            updateStatesForClient(selectedStates, stopStates, child, condition);
        }
    }
}