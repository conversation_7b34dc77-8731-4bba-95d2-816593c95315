package ru.naumen.core.server.cache;

/**
 * Данный объект владеет адаптером элемента кэша.
 * Используется для объектов, которые (полностью или частично) должны храниться в кэше. 
 * 
 * 
 * <AUTHOR>
 *
 * @param <A>
 */
public interface HasCacheNodeAdapter<A extends CacheNodeAdapter>
{
    /**
     * Код для кэша для cacheNode.putMultiProperty(PROPERTIES, ...
     * нужно использовать его, а не изобретать свои
     */
    String PROPERTIES = "properties";

    /**
     * Возвращает адаптер элемента кэша, связанный с данным объектом.
     */
    A getCacheNode();

    /**
     * Инициализация элемента кэша.
     * Операции, которые должны быть выполнены до работы с кэшем.
     * 
     * Например, коллекции должны быть проинициализированы до того, как в них начнут складывать значения.
     * @param cacheNode
     */
    void initCacheNode(A cacheNode);
}
