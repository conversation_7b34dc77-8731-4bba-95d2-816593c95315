package ru.naumen.core.server.mbean.notification;

import java.util.concurrent.atomic.AtomicLong;

import org.springframework.stereotype.Component;

/**
 * Компонент, которые хранит в себе количество различных результатов при отправке оповещений
 */
@Component
public class NotificationStatisticsStorage
{
    private final AtomicLong countSendSuccessful = new AtomicLong();
    private final AtomicLong countSendFailed = new AtomicLong();
    private final AtomicLong countInvalidEmails = new AtomicLong();
    private final AtomicLong countAttemptFailed = new AtomicLong();
    private final AtomicLong countSendFailedPartially = new AtomicLong();
    private final AtomicLong countSendFailedEmailNotExist = new AtomicLong();
    private final AtomicLong countSendFailedSystemEmail = new AtomicLong();

    /**
     * Увеличить количество удачно отправленных оповещений
     * с момента запуска приложения или с момента последнего сброса статистики
     */
    public void incSendSuccessful()
    {
        countSendSuccessful.incrementAndGet();
    }

    /**
     * Увеличить количество оповещений, которые должны были отправиться, но не были отправлены по некоторой причине,
     * с момента запуска приложения или с момента последнего сброса статистики
     */
    public void incSendFailed()
    {
        countSendFailed.incrementAndGet();
    }

    /**
     * Увеличить количество оповещений, которые не были отправлены из-за некорректных адресов,
     * с момента запуска приложения или с момента последнего сброса статистики
     */
    public void incInvalidEmails()
    {
        countInvalidEmails.incrementAndGet();
    }

    /**
     * Увеличить количество сбоев у первой быстрой отправки оповещения
     */
    public void incAttemptFailed()
    {
        countAttemptFailed.incrementAndGet();
    }

    /**
     * Увеличить количество оповещений, которые отправлены только для части адресов
     */
    public void incSendFailedPartially()
    {
        countSendFailedPartially.incrementAndGet();
    }

    /**
     * Увеличить количество оповещений, которые не отправились, т.к. у получателя не был указан email адрес
     */
    public void incSendFailedEmailNotExist()
    {
        countSendFailedEmailNotExist.incrementAndGet();
    }

    /**
     * Увеличить количество оповещений, которые не отправились на адреса указаные в параметрах подключения серверов входящей почты.
     */
    public void incSendFailedSystemEmail()
    {
        countSendFailedSystemEmail.incrementAndGet();
    }

    /**
     * Получить количество удачно отправленных оповещений
     * с момента запуска приложения или с момента последнего сброса статистики
     *
     * @return количество оповещений
     */
    public long getSendSuccessful()
    {
        return countSendSuccessful.get();
    }

    /**
     * Получить количество оповещений, которые должны были отправиться, но не были отправлены по некоторой причине,
     * с момента запуска приложения или с момента последнего сброса статистики
     *
     * @return количество оповещений
     */
    public long getSendFailed()
    {
        return countSendFailed.get();
    }

    /**
     * Получить количество оповещений, которые не были отправлены из-за некорректных адресов,
     * с момента запуска приложения или с момента последнего сброса статистики
     *
     * @return количество оповещений
     */
    public long getInvalidEmails()
    {
        return countInvalidEmails.get();
    }

    /**
     * Количество сбоев у первой быстрой отправки оповещения
     *
     * @return количество оповещений
     */
    public long getAttemptFailed()
    {
        return countAttemptFailed.get();
    }

    /**
     * Количество оповещений, которые отправлены только для части адресов
     *
     * @return количество оповещений
     */
    public long getSendFailedPartially()
    {
        return countSendFailedPartially.get();
    }

    /**
     * Количество оповещений, которые не отправились, т.к. у получателя не был указан email адрес
     *
     * @return количество оповещений
     */
    public long getSendFailedEmailNotExist()
    {
        return countSendFailedEmailNotExist.get();
    }

    /**
     * Количество оповещений, которые не отправились на адреса указаные в параметрах подключения серверов входящей почты.
     *
     * @return количество оповещений
     */
    public long getSendFailedSystemEmail()
    {
        return countSendFailedSystemEmail.get();
    }

    /**
     * Сбросить всю статистику
     */
    public void reset()
    {
        countSendSuccessful.set(0);
        countSendFailed.set(0);
        countInvalidEmails.set(0);
        countAttemptFailed.set(0);
        countSendFailedPartially.set(0);
        countSendFailedEmailNotExist.set(0);
        countSendFailedSystemEmail.set(0);
    }
}
