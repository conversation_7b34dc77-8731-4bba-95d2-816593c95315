package ru.naumen.core.server.style.templates;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.style.templates.StyleTemplate;
import ru.naumen.metainfoadmin.shared.Constants; // NOPMD admin-operator

/**
 * Реализация фабрики DTO для шаблонов стилей.
 * <AUTHOR>
 * @since Dec 2, 2016
 */
@Component
public class StyleTemplateDtoFactoryImpl implements StyleTemplateDtoFactory
{
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private EventActionService eventActionService;

    @Override
    public List<DtObject> create(Collection<StyleTemplate> templates)
    {
        return templates.stream().map(t -> create(t)).collect(Collectors.toList());
    }

    @Override
    public DtObject create(StyleTemplate template)
    {
        DtObject dto = createLite(template);
        if (null == dto)
        {
            return null;
        }
        dto.setProperty(Constants.StyleTemplate.TEMPLATE_TEXT,
                metainfoUtils.getLocalizedValue(template.getTemplateText()));
        dto.setProperty(Constants.StyleTemplate.USAGE_PLACES, resolveUsagePoints(template));
        return dto;
    }

    @Override
    public List<DtObject> createLite(Collection<StyleTemplate> templates)
    {
        return templates.stream().map(t -> createLite(t)).collect(Collectors.toList());
    }

    @Override
    public DtObject createLite(StyleTemplate template)
    {
        if (null == template)
        {
            return null;
        }
        SimpleDtObject dto = new SimpleDtObject();
        dto.setUUID(template.getCode());
        dto.setTitle(metainfoUtils.getLocalizedValue(template.getTitle()));
        dto.setProperty(Constants.StyleTemplate.CODE, template.getCode());
        dto.setProperty(Constants.StyleTemplate.CREATION_DATE, template.getCreationDate());
        dto.setProperty(Constants.StyleTemplate.LAST_MODIFIED_DATE, template.getLastModifiedDate());
        return dto;
    }

    private List<DtObject> resolveUsagePoints(StyleTemplate template)
    {
        return template.getUsagePoints().stream().map(eventActionService::getEventAction).filter(ea -> null != ea)
                .map(ea -> new SimpleDtObject(ea.getCode(), metainfoUtils.getLocalizedValue(ea.getTitle())))
                .sorted(ITitled.COMPARATOR).collect(Collectors.toList());
    }
}
