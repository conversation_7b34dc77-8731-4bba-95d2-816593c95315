/**
 * 
 */
package ru.naumen.core.server.timer.bcp.actions;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.timer.AbstractTimer;
import ru.naumen.core.server.timer.bcp.TimerCalculationContext;
import ru.naumen.core.server.timer.bcp.TimerLoggingHelper;
import ru.naumen.core.server.timer.bcp.operations.TimerStatusChangeOperations;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.timer.AbstractTimerDto;

/**
 * <AUTHOR>
 * @since 21.05.2013
 *
 */
@Component
public class TimerStatusChangeActionPassivateActivateIfNeedRecalcImpl<T extends AbstractTimer, DT extends AbstractTimerDto>
        extends TimerStatusChangeActionImpl<T, DT>
{
    public static final Logger LOG = LoggerFactory.getLogger(TimerStatusChangeActionPassivateActivateIfNeedRecalcImpl.class);

    @Override
    public void execute(TimerCalculationContext<T, DT> timerCalcContext, IHasObjectBOContext<IHasMetaInfo> context,
            TimerStatusChangeOperations<T, DT> operations)
    {
        String prefixLogs = TimerLoggingHelper.getPrefixLogs(timerCalcContext.getAttribute(), context);
        if (timerUtils
                .needRecalc(context, timerCalcContext.getObjectMetaClass(), timerCalcContext.getTimerDefinition()))
        {
            LOG.debug("{} Force recount activate", prefixLogs);
            // Изменен определяющий атрибут. Пересчитываем время по старому значению атрибута.
            operations.doPassivate(timerCalcContext, context);
            operations.doActivate(timerCalcContext, context);
        }
    }
}