package ru.naumen.core.server.theme;

import ru.naumen.commons.shared.utils.StringUtilities;

/**
 * Параметры тем.
 * Имеют значения по умолчанию и древовидное наследование.
 * Разделены на разделы.
 *
 * При добавлении нового параметра его нужно расположить в соответствующем разделе
 *
 * <AUTHOR>
 * @since 05.07.22
 */
public enum ThemeParameters
{
    /**
     * Код темы.
     * (нужен только для стандартных тем, для нахождения стандартного лого)
     */
    themeCode(StringUtilities.EMPTY),


    // БАЗОВЫЕ ЦВЕТА

    /**
     * акцентный цвет
     */
    accentColor("#2a84b7"),

    /**
     * акцентный цвет третьего уровня
     */
    accentTertiaryColor("#fff"),

    /**
     * базовое семейство шрифтов
     */
    baseFont("'Roboto', sans-serif"),

    /**
     * базовый темный цвет (для текстовых элементов, обводок, иконок и т.д.)
     */
    blackColorBase("#323232"),

    /**
     * Основной цвет текста
     */
    textColor(inherit(blackColorBase)),

    /**
     * цвет вспомогательного текста
     */
    secondaryTextColor("#5f5f5f"),

    /**
     * цвет задизейбленного текста (светлее, чем secondaryTextColor)
     */
    disabledTextColor("#999"),

    /**
     * цвет ссылок (встречаются везде)
     */
    linkColor("#085896"),

    /**
     * Цвет основного фона
     */
    baseBackground("#eff3f8"),

    /**
     * цвет основных разделителей и границ
     */
    borderColor("#c0c0c0"),
    
    /**
     * Базовый цвет заливки по умолчанию фона какого-либо объекта при наведении
     */
    defaultHoverBackground("#ebebeb"),

    /**
     * плавный переход между состояниями элемента
     */
    defaultTransition("color .2s, background .2s, fill .2s"),



    // ПОЛЯ ВВОДА и ВЫПАДАЮЩИЕ СПИСКИ

    /**
     * высота поля ввода(либо селекта)
     */
    inputHeight("32px"),

    /**
     * Радиус поля ввода(либо селекта)
     */
    inputRadius("2px"),

    /**
     * Отступ для текста в полях ввода(либо селекта)
     */
    inputPaddingLeft("12px"),

    /**
     * Цвет текста в полях ввода(либо селектах)
     */
    inputTextColor(inherit(blackColorBase)),

    /**
     * Фон полей ввода(либо селектов)
     */
    inputBackground("#fff"),

    /**
     * Фон недоступных полей ввода(либо селектов)
     */
    inputDisabledBackground("#fafafa"),

    /**
     * Фон поля ввода с подсветкой синтаксиса
     */
    inputSyntaxBackground(inherit(inputBackground)),

    /**
     * Цвет рамки поля в фокусе
     */
    inputFocusBorderColor("#0063b0"),

    /**
     * цвет фона выбранного элемента в списках и деревьях
     */
    itemSelectedBackground("#f4f4f4"),

    /**
     * рамка для элемента в выпадающем списке
     */
    selectItemBorder("solid 1px " + inputBackground.getDefaultValue()),

    /**
     * отступы элемента в выпадающем списке(верхний и нижний)
     */
    selectItemPadding("8px 12px"),

    /**
     *  левый отступ для элементов выпадающего списка(дерева)
     */
    selectItemLeftOffset("12"),

    /**
     * фон элемента выпадающего списка при наведении
     */
    popupSelectItemHoverBackground(inherit(defaultHoverBackground)),

    /**
     * рамка выпадающего списка
     */
    selectPopupBorder("solid 1px #e2e7ed"),

    /**
     * фон элемента списка, на который навели курсор мыши
     * (в кнопке "Добавить" и в навигационном меню)
     */
    selectItemHoverBackground(inherit(accentColor)),

    /**
     * цвет текста элемента списка, на который навели курсор мыши (вариант для движка safari aka webkit)
     * (в кнопке "Добавить" и в навигационном меню)
     */
    selectItemHoverTextColor("#fff"),

    /**
     * Цвет плашки у выпадающего списка множественного выбора
     */
    badgeBackground("#d9d9d9"),

    /**
     * Цвет плашки у выпадающего списка множественного выбора при наведении
     */
    badgeHoverBackground("#bfbfbf"),

    /**
     * Цвет заливки виджетов после вставки в них сохраненного значения
     */
    autoInsertValueColor("#e8f1f8"),

    /**
     * Цвет заливки границы виджета после вставки в них сохраненного значения
     */
    autoInsertBorderColor("#4d92c8"),


    // ЛЕВОЕ МЕНЮ

    /**
     * фон навигационного меню
     */
    navMenuBackground(inherit(baseBackground)),

    /**
     * Фон левого меню
     */
    leftMenuBackground("#fff"),

    /**
     * Цвет текста элемента в левом меню
     */
    leftMenuItemTextColor(inherit(secondaryTextColor)),

    /**
     * Цвет текста заголовка и особого раздела в левом меню
     */
    leftMenuModuleItemTextColor("#999"),

    /**
     * цвет полоски-контрола, скрывающей левое меню
     */
    leftMenuHiderColor("#d1d1d1"),

    /**
     * цвет фона меню быстрого доступа.
     */
    quickAccessMenuBackground(inherit(accentColor)),

    /**
     * цвет надписи или иконки в плитке меню быстрого доступа.
     */
    quickAccessTileColor("#fff"),

    /**
     * цвет надписи или иконки в плитке-ссылке меню быстрого доступа.
     */
    quickAccessTileLinkColor(inherit(quickAccessTileColor)),

    
    // ШАПКА САЙТА

    /**
     * фон шапки сайта
     */
    headerBackground("#fff"),

    /**
     * фон шапки контента
     */
    contentHeaderBackground("#fff"),

    /**
     * цвет различных ссылок, расположенных в шапке карточки (текст вкладок, ссылка на предыдущую страницу)
     */
    contentHeaderLinkColor(inherit(secondaryTextColor)),

    /**
     * цвет кнопок-ссылок, расположенных в шапке карточки
     */
    contentHeaderActionLinkColor(inherit(contentHeaderLinkColor)),

    /**
     * цвет кнопок-ссылок при наведении, расположенных в шапке карточки
     */
    contentHeaderActionLinkHoverColor(inherit(contentHeaderActionLinkColor)),

    /**
     * цвет текста в шапке карточки и в диалоговых окнах
     */
    contentHeaderTextColor(inherit(textColor)),

    /**
     * цвет иконок в шапке
     */
    headerIconColor(inherit(accentColor)),

    /**
     * толщина полоски под шапкой(по умолчанию 0)
     */
    headerBorderBottomWidth("0"),

    /**
     * толщина полоски под шапкой в компактном режиме
     */
    headerBorderBottomWidthCompact(inherit(headerBorderBottomWidth)),

    /**
     * цвет полоски под шапкой
     */
    headerBorderBottomColor(inherit(leftMenuHiderColor)),

    /**
     * цвет рамки поля ввода поиска в шапке
     */
    headerSearchBorderColor(inherit(borderColor)),

    /**
     * цвет шапки карточки архивного объекта
     */
    removedBOHeaderColor("#e8e8e8"),

    /**
     * цвет текста  у верхнего элемента меню
     */
    menuItemTextColor("#fff"),

    /**
     * фон выпадашки верхнего меню или кнопки Еще.
     */
    menuPopupBackground("#fff"),

    /**
     * цвет последней (активной) "крошки"
     */
    lastCrumbColor(inherit(textColor)),

    /**
     * цвет названия кнопки верхнего меню.
     */
    topMenuButtonTextColor(inherit(menuItemTextColor)),

    /**
     * цвет текста в выпадающем меню верхнего меню.
     */
    topMenuPopupTextColor(inherit(selectItemHoverBackground)),

    /**
     * цвет текста элемента верхнего меню, на который навели курсор мыши
     */
    topMenuItemHoverTextColor(inherit(selectItemHoverTextColor)),

    /**
     * фон элемента верхнего меню, на который наведен курсор мыши
     */
    topMenuItemHoverBackground(inherit(accentColor)),

    /**
     * Цвет заливки кнопки при наведении в верхнем меню и ее контура.
     */
    topMenuButtonBackground("transparent"),

    /**
     * Цвет названия кнопки верхнего меню при наведении мыши.
     */
    topMenuButtonHoverTextColor(inherit(topMenuItemHoverTextColor)),

    /**
     * Цвет заливки кнопки в верхнем меню.
     */
    topMenuButtonHoverBackground(inherit(topMenuItemHoverBackground)),


    // КОНТЕНТЫ

    /**
     * фон зоны контента
     */
    contentBackground("#fff"),

    /**
     * цвет различных заголовков на страницах - названия контентов, названия блоков в результатах поиска
     */
    blockTitleColor(inherit(textColor)),

    /**
     * цвет кнопок-ссылок
     */
    actionLinkColor(inherit(blackColorBase)),

    /**
     * цвет кнопок-ссылок при наведении
     */
    actionLinkHoverColor(inherit(actionLinkColor)),

    /**
     * цвет названия атрибута
     */
    attrTitleColor(inherit(secondaryTextColor)),

    /**
     * Заливка скролла
     */
    scrollBackground(inherit(blackColorBase)),

    /**
     * Цвет фона бейджа приватного комментария
     */
    privateCommentBadgeBackground("#E6E6E6"),

    /**
     * Цвет текста в бейдже приватного комментария
     */
    privateCommentBadgeColor("#4D4D4D"),
    
    // ПАНЕЛИ

    /**
     * Фон различных панелей. Например, массовых действий адвлиста
     */
    panelBackground("#f2f2f2"),

    /**
     * цвет текста информационной панели (либо, описание атрибута)
     */
    infoPanelTextColor(inherit(attrTitleColor)),

    /**
     * фон информационной панели (либо, описание атрибута)
     */
    infoPanelBackground("#f4f4f4"),


    // КНОПКИ

    /**
     * размер текста на стандартной кнопке
     */
    buttonFontSize("13px"),

    /**
     * толщина текста на стандартной кнопке
     */
    buttonFontWeight("bold"),

    /**
     * высота стандартной кнопки
     */
    buttonHeight("24px"),

    /**
     * фон стандартной кнопки
     */
    buttonBackground(inherit(accentColor)),

    /**
     * фон стандартной кнопки при наведении
     */
    buttonHoverBackground(inherit(accentColor)),

    /**
     * фон стандартной кнопки при нажатии
     */
    buttonActiveBackground(inherit(accentColor)),

    /**
     * фон зафиксированной стандартной кнопки
     */
    buttonPressedBackground(inherit(accentColor)),

    /**
     * цвет текста стандартной кнопки
     */
    buttonTextColor("#fff"),

    /**
     * цвет текста стандартной кнопки при наведении
     */
    buttonHoverTextColor(inherit(buttonTextColor)),

    /**
     * цвет текста стандартной кнопки при нажатии
     */
    buttonActiveTextColor(inherit(buttonHoverTextColor)),

    /**
     * цвет текста зафиксированной стандартной кнопки
     */
    buttonPressedTextColor(inherit(buttonTextColor)),

    /**
     * фон кнопок на адвлисте при нажатии
     */
    advlistButtonActiveBackground("#bbb"),

    /**
     * цвет текста кнопок на адвлисте при наведении
     */
    advlistButtonHoverTextColor(inherit(blackColorBase)),

    /**
     * цвет текста кнопок на адвлисте при нажатии
     */
    advlistButtonActiveTextColor(inherit(advlistButtonHoverTextColor)),

    /**
     * фон кнопок на адвлисте
     */
    advlistButtonBackground("#f4f4f4"),

    /**
     * фон кнопок на адвлисте при наведении
     */
    advlistButtonHoverBackground("#e4e4e4"),

    /**
     * фон зажатых кнопок на адвлисте
     */
    advlistButtonPressedBackground(inherit(advlistButtonActiveBackground)),

    /**
     * цвет текста зажатых кнопок на адвлисте
     */
    advlistButtonPressedTextColor(inherit(advlistButtonHoverTextColor)),

    /**
     * цвет текста кнопок на адвлисте
     */
    advlistButtonTextColor(inherit(secondaryTextColor)),

    /**
     * цвет иконки описания атрибута
     */
    attrDescriptionIconColor(inherit(accentColor)),

    /**
     * цвет текста кнопок на формах
     */
    formButtonTextColor(inherit(buttonTextColor)),

    /**
     * фон кнопок на формах
     */
    formButtonBackground(inherit(buttonBackground)),

    /**
     * фон кнопок на формах при наведении
     */
    formButtonHoverBackground(inherit(buttonHoverBackground)),

    /**
     * фон кнопок на формах при нажатии
     */
    formButtonPressedBackground(inherit(buttonPressedBackground)),

    /**
     * фон кнопок на вкладке 1 уровня
     */
    tabButtonBackground(inherit(buttonBackground)),

    /**
     * фон кнопок на вкладке 1 уровня при наведении
     */
    tabButtonHoverBackground(inherit(buttonHoverBackground)),

    /**
     * фон кнопок на вкладке 1 уровня при нажатии
     */
    tabButtonActiveBackground(inherit(buttonActiveBackground)),

    /**
     * цвет текста кнопок на вкладке 1 уровня
     */
    tabButtonTextColor(inherit(buttonTextColor)),

    /**
     * цвет текста кнопок на вкладке 1 уровня при наведении
     */
    tabButtonHoverTextColor(inherit(tabButtonTextColor)),

    /**
     * цвет пользовательской кнопки (кнопки-скрипт)
     */
    userButtonTextColor(inherit(accentColor)),

    /**
     * цвет пользовательской кнопки (кнопки-скрипт) при наведении
     */
    userButtonHoverTextColor(inherit(blackColorBase)),

    /**
     * Фон кнопки Отмена на формах
     */
    cancelButtonBackground("none"),

    /**
     * Фон кнопки Отмена на формах при наведении
     */
    cancelButtonHoverBackground("rgba(228,228,228,.5)"),


    // М О Д А Л Ь Н Ы Е   Ф О Р М Ы

    /**
     * цвет фона нижней части модального окна
     */
    popupFooterBackgroundColor("#f2f2f2"),

    /**
     * цвет шапки модального окна
     */
    popupHeaderBackgroundColor(inherit(headerBackground)),

    /**
     * цвет текста в шапке всплывающей формы
     */
    popupHeaderTextColor("#fff"),

    /**
     * Позиционирование(padding) названия для поля ввода на формах добавления и ред.
     */
    formLabelPadding("2px 4px 4px 0"),


    // ТАБЛИЦЫ

    /**
     * Цвет текста в заголовках таблицы
     */
    tableHeaderColor(inherit(attrTitleColor)),

    /**
     * Высота шрифта в заголовках таблицы
     */
    tableHeaderFontSize("13px"),

    /**
     * Цвет фона строки таблицы при наведении на неё курсора мыши
     */
    tableRowBackgroundHover(inherit(defaultHoverBackground)),

    /**
     * Цвет фона выбранной строки в таблице
     */
    tableRowSelectedBackground("#f4f4f4"),

    /**
     * Цвет текста в таблицах.
     */
    tableTextColor(inherit(textColor)),

    /**
     * цвет разделителей строк
     */
    rowBorderColor("#d9d9d9"),

    /**
     * Светлый цвет разделителей строк
     */
    rowBorderColorLight(inherit(tableRowSelectedBackground)),


    // ВКЛАДКИ

    /**
     * Шрифт названий вкладок
     */
    tabLabelFont("inherit"),

    /**
     * Цвет названий вкладок у контента "Панель вкладок" в режиме оператора
     * (т.к. у контента отличается фон в режиме оператора и администратора)
     */
    tabLinkColor(inherit(secondaryTextColor)),

    /**
     * цвет кнопок-ссылок на вкладке 1 уровня
     */
    tabActionLinkColor(inherit(actionLinkColor)),

    /**
     * цвет кнопок-ссылок на вкладке 1 уровня при наведении
     */
    tabActionLinkHoverColor(inherit(tabActionLinkColor)),

    /**
     * Цвет названия вкладки во всплывающем списке.
     */
    popupTabLinkColorHover(inherit(contentHeaderLinkColor)),

    /**
     * Цвет названя активной вкладки у контента "Панель вкладок"
     */
    activeTabColor(inherit(blockTitleColor)),

    /**
     * фон корешка выбранной вкладки в контенте Панель вкладок
     */
    contentSelectedTabBackground(inherit(contentBackground)),


    // ИКОНКИ

    /**
     * Основной цвет иконок в статическом режиме.
     */
    iconStatColor("#5f5f5f"),

    /**
     * Основной цвет иконок в hover-режиме.
     */
    iconHoverColor(inherit(blackColorBase)),

    /**
     * Цвет иконок в режиме disabled
     */
    iconDisabledColor("#ddd"),

    /**
     * Фон подложки для иконок
     */
    iconBackground("#f2f2f2"),

    /**
     * Фон подложки для иконок в hover-режиме
     */
    iconHoverBackground("#d1d1d1"),

    /**
     * Фон подложки для иконок в active-режиме
     */
    iconActiveBackground("#bbb"),

    /**
     * Цвет иконок в статическом режиме. Для иконок находящихся в ячейках таблиц
     */
    rowHovIconStatColor(inherit(iconStatColor)),

    /**
     * Основной цвет иконок в hover-режиме. Для иконок находящихся в ячейках таблиц
     */
    rowHovIconHoverColor(inherit(iconHoverColor)),


    // ТЕНИ

    /**
     * Светлая длинная тень
     */
    lightShadow("0px 4px 16px rgba(95, 95, 95, 0.2)"),

    /**
     * Светлая короткая тень
     */
    lightShortShadow("0 2px 8px rgba(129, 146, 161, 0.2)"),

    /**
     * Темная длинная тень
     */
    darkShadow("0 4px 14px #899aa9"),

    /**
     * Специальная светлая тень для декорирования контентов и других блоков, лежащих прямо на странице.
     */
    contentShadow("0 4px 6px rgba(129, 146, 161, 0.2)"),


    // ВЕРСИОНИРОВАНИЕ

    /**
     * цвет полосы-маркера переключения режимов
     */
    modeMarkerBackground("#45a495"),

    /**
     * цвет полосы-маркера переключения режимов при наведении
     */
    modeMarkerHoverBackground("#2f6f65"),

    /**
     * цвет полосы-маркера переключения режимов при наведении
     */
    modeMarkerPressedBackground("#183934"),

    /**
     * Цвет текста  инлайн-бейджа объектов окружения в ячейках таблицы, ссылках и т.п.
     */
    environmentObjectColor("#999"),

    // ПУШ УВЕДОМЛЕНИЯ

    /**
     * Цвет рамки уведомления в интерфейсе.
     */
    pushNotificationBorderColor("#edd69a"),

    /**
     * Цвет элементов внутри рамки уведомления в интерфейсе.
     */
    @Deprecated
    pushNotificationBorderIconColor(inherit(contentBackground)),

    /**
     * Цвет фона системного уведомления.
     */
    systemNotificationBackgroundColor("#fff7e2"),

    /**
     * Цвет фона системного уведомления при наведении мыши.
     */
    systemNotificationBackgroundColorHover("#ebdfc2"),

    /**
     * Цвет границы системного уведомления.
     */
    systemNotificationBorderColor(inherit(pushNotificationBorderColor)),


    // ПАРАМЕТРЫ, ОБЩИЕ ДЛЯ ВСЕХ ТЕМ

    /**
     * Цвет выделения элементов результатов поиска
     */
    searchResultsColor("#fba66a"),

    /**
     * Цвет фона информационного сообщения
     */
    messageInformationBackground("#ECF7F9"),

    /**
     * Цвет полосы информационного сообщения
     */
    messageInformationColor("#8CCCD9"),

    /**
     * Цвет фона элементов результатов поиска
     */
    searchResultsBackground("#fee1cd"),

    /**
     * цвет фона сообщения при одновременной работе с объектом (предупреждение)
     */
    messageActionWarningBackground("#e5d4b3"),

    /**
     * цвет фона сообщения при одновременной работе с объектом (информирование)
     */
    messageActionInformationBackground("#b2dde6"),

    /**
     * цвет элементов (полосы) cообщения об удачном создании объекта
     */
    messageActionSuccessColor("#94d1ad"),

    /**
     * цвет фона cообщения об удачном создании объекта
     */
    messageActionSuccessBackground("#edf7f1"),

    /**
     * цвет элементов системных сообщений (предупреждения)
     */
    messageWarningColor("#d9bf8c"),

    /**
     * цвет фона системных сообщений (предупреждения)
     */
    messageWarningBackground("#fff8e5"),
    /**
     * цвет элементов системных сообщений (информация)
     */
    messageInfoColor("#73cddc"),

    /**
     * цвет фона системных сообщений (информация)
     */
    messageInfoBackground("#e9f6fa"),

    /**
     * цвет инлайн-кнопки в сообщении (предупреждения)
     */
    messageWarningButtonColor("#c69f53"),

    /**
     * цвет темных элементов системных сообщений (ошибка)
     */
    messageErrorColor("#e08a85"),

    /**
     * цвет фона системных сообщений (ошибка)
     */
    messageErrorBackground("#faeceb"),


    // КАЛЕНДАРЬ (Надо убрать)

    /**
     * цвет текста даты в календаре, на которую навели курсор мыши
     */
    datePickerDayHighlightedColor("#4d5ced"),

    /**
     * цвет фона выбранной даты в календаре, на которую навели курсор мыши
     */
    datePickerDaySelectedHighlightedBackground("#d7dfe8"),

    /**
     * цвет текста и стрелок на панели инструментов в режиме редактирования
     */
    editableToolPanelSeparatorTextColor(inherit(blackColorBase)),


    // АДМИНКА (Надо убрать)

    /**
     * цвет фона названия метакласса при наведении курсора мыши
     * на карточке метаклассов или справочников
     */
    metaClassHoverBackground("#ecf3fb"),

    /**
     * <ul><li>Цвет фона и границ блока текущего статуса (в котором находится объект) в контенте "Диаграмма жизненного цикла".</li>
     * <li>Цвет фона и границ блока статуса в диаграмме жизненного цикла на странице "Жизненный цикл" в интерфейсе настройки, который выбран путём клика на область блока.</li>
     * </ul>
     */
    workflowSelectedStateColor("#7f96b9"),

    /**
     *  Цвет фона и границ блока статуса в контенте "Диаграмма жизненного цикла" и в интерфейсе настройки.
     */
    workflowStateColor("#aab7ca"),

    /**
     * цвет фона ячеек с ошибок в таблице переходов на вкладке "Жизненный цикл"
     */
    transitionMatrixErrorCellBackground("#ffe4e4");


    // =============================================================

    /**
     * Префикс для указания зависимости одного параметра от другого
     * в файле параметров тем
     * Например linkColor=$textColor
     */
    public static final String DEPENDENCY_PREFIX = "$";

    /**
     * Префикс комментария в файле параметров тем
     */
    public static final String COMMENT_PREFIX = "#";

    /**
     * Знак определения значения параметра в файле параметров тем
     */
    public static final String DEFINE_PREFIX = "=";

    /**
     * Значение параметра по умолчанию
     */
    private final String defaultValue;

    ThemeParameters(String defaultValue)
    {
        this.defaultValue = defaultValue;
    }

    String getDefaultValue()
    {
        return defaultValue;
    }

    public static String inherit(ThemeParameters themeParameter)
    {
        return DEPENDENCY_PREFIX + themeParameter.name();
    }
}
