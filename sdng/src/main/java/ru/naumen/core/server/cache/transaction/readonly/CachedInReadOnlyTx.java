package ru.naumen.core.server.cache.transaction.readonly;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import org.springframework.cache.annotation.Cacheable;

import ru.naumen.core.server.cache.transaction.TransactionCacheKeyGenerator;
import ru.naumen.core.server.cache.transaction.TransactionCacheManager;

/**
 * В случае вызова метода, помеченного данной аннотаций, 
 * результат будет кэшироваться в рамках текущей транзакции, если она ReadOnly
 * 
 * <AUTHOR>
 * @since Apr 22, 2016
 */
@Documented
@Retention(RUNTIME)
@Target(METHOD)
//@formatter:off
@Cacheable(
        value = TransactionReadOnlyCacheImpl.NAME, 
        cacheManager = TransactionCacheManager.NAME, 
        keyGenerator = TransactionCacheKeyGenerator.NAME)
//@formatter:on
public @interface CachedInReadOnlyTx
{

}
