package ru.naumen.core.server.mbean.employee;

import java.util.Map;

import jakarta.inject.Inject;
import javax.management.openmbean.TabularData;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.IDao;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.employee.EmployeeDao;
import ru.naumen.core.server.mbean.OpenTypeAdapter;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.filters.Filters;

import com.google.common.collect.Maps;

/**
 * Реализация MBean {@link EmployeeStatisticsMBean}
 *
 * <AUTHOR>
 * @since 15.02.2019
 */
@Component
public class EmployeeStatistics implements EmployeeStatisticsMBean
{
    private final EmployeeDao<Employee> employeeDao;
    private final DaoFactory daoFactory;

    @Inject
    public EmployeeStatistics(EmployeeDao<Employee> employeeDao, DaoFactory daoFactory)
    {
        this.employeeDao = employeeDao;
        this.daoFactory = daoFactory;
    }

    @Override
    public long getCountUsers()
    {
        IDao dao = daoFactory.get(Constants.Employee.FQN);
        DtoCriteria dtoCriteria = new DtoCriteria(Constants.Employee.FQN);
        return dao.count(dtoCriteria);
    }

    @Override
    public TabularData getCountUsersByLicense()
    {
        Map<String, Long> countOfUsersPerLicense = Maps.newHashMap();
        employeeDao.countEmployeesByLicense().forEach((k, v) ->
                countOfUsersPerLicense.put(k, (long)v));

        IDao dao = daoFactory.get(Constants.Employee.FQN);
        DtoCriteria dtoCriteria = new DtoCriteria(Constants.Employee.FQN);
        dtoCriteria.addFilters(Filters.eq(Constants.Employee.LICENSE, "notLicensed"));
        countOfUsersPerLicense.put("notLicensed", (long)dao.count(dtoCriteria));

        return OpenTypeAdapter.intoTabularData(countOfUsersPerLicense);
    }
}
