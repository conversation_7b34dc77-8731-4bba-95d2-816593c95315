package ru.naumen.core.server.mbean.hibernate;

import java.io.File;
import java.io.IOException;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.core.type.AnnotatedTypeMetadata;

import ru.naumen.commons.shared.FxException;

/**
 * Условие для поднятия бина, все классы с аннотацией
 * /@Conditional(HibernateStatisticsEnabledCondition.class) будут инициализироваться только, если
 * включена хибернейт статистика
 * <AUTHOR>
 * @since 16.08.2021
 */
public class HibernateStatisticsEnabledCondition implements Condition
{
	private static final String HIBERNATE_STATISTICS_PARAM = "hibernate.generate_statistics";

	private static final String EXT_PROP_DIR = "ext.prop.dir";
	private static final String DBACCESS_PROPERTIES = "dbaccess.properties";

	private static boolean isHibernateStatisticsEnabled(ConditionContext context)
	{
		final String extpropDirPath = context.getEnvironment().getProperty(EXT_PROP_DIR);
		final FileSystemResource fileSystemResource = new FileSystemResource(
				extpropDirPath + File.separatorChar + DBACCESS_PROPERTIES);
		if (fileSystemResource.exists())
		{
			try
			{
				final String property = PropertiesLoaderUtils.loadProperties(fileSystemResource)
						.getProperty(HIBERNATE_STATISTICS_PARAM);
				return Boolean.parseBoolean(property);
			}
			catch (IOException e)
			{
				throw new FxException(e);
			}
		}
		return false;
	}

	@Override
	public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata)
	{
		return isHibernateStatisticsEnabled(context);
	}
}