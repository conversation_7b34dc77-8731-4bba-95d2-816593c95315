package ru.naumen.core.server.filters.handlers;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

import jakarta.inject.Inject;

import ru.naumen.commons.server.utils.DateUtils;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.filters.ObjectFilterHandler;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.shared.filters.TodayFilter;
import ru.naumen.metainfo.shared.Constants.DateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Обработчик фильтра "сегодня" по дате/времени.
 * <AUTHOR>
 * @since Jul 20, 2016
 */
@ObjectFilterHandler(filters = TodayFilter.class)
public class TodayFilterHandler extends AbstractNamedFilterHandler<TodayFilter>
{
    /**
     * Получить отрезок времени для, удовлетворяющий фильтру
     */
    public static Pair<Date, Date> getDateRange()
    {
        ru.naumen.core.server.DateUtils dateUtils = SpringContext.getInstance()
                .getBean(ru.naumen.core.server.DateUtils.BEAN_NAME, ru.naumen.core.server.DateUtils.class);
        Calendar calendar = createCalendar(dateUtils);
        Date sdate = DateUtils.truncateDate(calendar).getTime();
        calendar.add(Calendar.DATE, 1);
        calendar.add(Calendar.MILLISECOND, -1);
        Date edate = calendar.getTime();
        return Pair.create(sdate, edate);
    }

    private static Calendar createCalendar(ru.naumen.core.server.DateUtils dateUtils)
    {
        Calendar calendar = Calendar.getInstance();
        TimeZone tz = dateUtils.getCurrentUserTimeZone();
        if (null != tz)
        {
            calendar.setTimeZone(tz);
        }
        calendar.setTime(new Date());
        return calendar;
    }

    @Inject
    private ru.naumen.core.server.DateUtils dateUtils;

    @Override
    public List<HCriterion> getCriterions(HCriteria criteria)
    {
        Attribute attribute = getAttribute();

        if (DateAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            // Дата (без времени) требует особой уличной магии, т.к. не привязана к часовому поясу.
            // Нужно получить текущую дату в часовом поясе пользователя и передать ее в запрос без преобразований.
            Calendar calendar = createCalendar(dateUtils);
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            format.setCalendar(calendar);
            java.sql.Date sqlDate = java.sql.Date.valueOf(format.format(calendar.getTime()));
            return Collections.singletonList(restrictionsFactory.getStrategy(attribute).eq(criteria, attribute,
                    sqlDate));
        }

        Pair<Date, Date> range = getDateRange();
        return Collections.singletonList(restrictionsFactory.getStrategy(attribute).between(criteria, attribute,
                null, range.left, range.right));
    }
}
