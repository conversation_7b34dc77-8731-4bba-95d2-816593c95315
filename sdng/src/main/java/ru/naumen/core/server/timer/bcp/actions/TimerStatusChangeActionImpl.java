/**
 * 
 */
package ru.naumen.core.server.timer.bcp.actions;

import jakarta.inject.Inject;

import ru.naumen.core.server.timer.AbstractTimer;
import ru.naumen.core.server.timer.TimerUtils;
import ru.naumen.core.shared.timer.AbstractTimerDto;

/**
 * <AUTHOR>
 * @since 21.05.2013
 *
 */
public abstract class TimerStatusChangeActionImpl<T extends AbstractTimer, DT extends AbstractTimerDto> implements
        TimerStatusChangeAction<T, DT>
{
    @Inject
    protected TimerUtils timerUtils;
}