package ru.naumen.core.server.mbean;

import jakarta.annotation.Nullable;
import javax.management.ObjectName;

import org.springframework.jmx.export.MBeanExportException;
import org.springframework.jmx.export.MBeanExporter;

/**
 * Расширение {@link MBeanExporter}, поддерживающее <code>null</code> при регистрации бинов.
 * <AUTHOR>
 * @since Mar 01, 2020
 */
public class NauMBeanExporter extends MBeanExporter
{
    @Override
    @Nullable
    protected ObjectName registerBeanNameOrInstance(@Nullable Object mapValue, String beanKey) throws MBeanExportException
    {
        return null == mapValue ? null : super.registerBeanNameOrInstance(mapValue, beanKey);
    }
}
