package ru.naumen.core.server.cache.transaction;

import java.util.Collection;

import jakarta.inject.Inject;

import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.cache.transaction.readonly.TransactionReadOnlyCacheImpl;

import com.google.common.collect.ImmutableSet;

/**
 * Оптимизируем equals над name, хранить кэши в мапе слишком дорого,
 * так как придется все время вычислять хэши строк.
 * 
 * Имена бинов и имена кэшей в их аннотации отличаются адресами,
 * поэтому подстраиваемся под адреса строк в аннотациях, чтобы equals проходил по == 
 * 
 * <AUTHOR>
 * @since Apr 22, 2016
 */
@Component(TransactionCacheManager.NAME)
public class TransactionCacheManager implements CacheManager
{
    public static final String NAME = "TransactionCacheManager";

    @Inject
    private TransactionReadOnlyCacheImpl readOnlyCache;

    private String readOnlyCacheName = TransactionReadOnlyCacheImpl.NAME;

    @Override
    public Cache getCache(String name)
    {
        if (readOnlyCacheName.equals(name))
        {
            readOnlyCacheName = name;
            return readOnlyCache;
        }
        return null;
    }

    @Override
    public Collection<String> getCacheNames()
    {
        return ImmutableSet.of(readOnlyCacheName);
    }
}
