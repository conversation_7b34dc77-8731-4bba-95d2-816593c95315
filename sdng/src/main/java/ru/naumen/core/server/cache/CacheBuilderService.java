package ru.naumen.core.server.cache;

import org.infinispan.configuration.cache.CacheMode;
import org.infinispan.configuration.cache.ConfigurationBuilder;
import org.infinispan.eviction.EvictionStrategy;
import org.infinispan.manager.EmbeddedCacheManager;
import org.infinispan.transaction.TransactionMode;
import org.infinispan.util.concurrent.IsolationLevel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.InfinispanCacheManager;
import ru.naumen.core.server.cache.google.GoogleCacheWrapper;
import ru.naumen.core.server.cache.google.GoogleLoadingCacheWrapper;

/**
 * Сервис отвечающий за создание кэша в зависимости от режима запуска приложения.
 * Если приложение запущено в режиме кластеризации - создается реплицируемый кэш Infinispan
 * В противном случае кэш создается с помощью {@link CacheBuilder} и не является реплицируемым.
 * 
 * <AUTHOR>
 * @since 17.10.14
 */
@Component(CacheBuilderService.CACHE_BUILDER_SERVICE_BEAN)
public class CacheBuilderService
{
    static final String CACHE_BUILDER_SERVICE_BEAN = "NaumenCacheBuilder";

    private static final Logger logger = LoggerFactory.getLogger(CacheBuilderService.class);

    private final InfinispanCacheManager cacheManager;

    @Inject
    public CacheBuilderService(InfinispanCacheManager cacheManager)
    {
        this.cacheManager = cacheManager;
    }

    public <K, V> Cache<K, V> build(ru.naumen.core.server.cache.CacheBuilder<K, V> builder)
    {
        if (!builder.isTransactional())
        {
            return buildSimpleGoogleCache(builder);
        }
        else
        {
            return buildInfinispanCache(builder, null);
        }
    }

    public <K, V> LoadingCache<K, V> build(ru.naumen.core.server.cache.CacheBuilder<K, V> builder,
            CacheLoader<K, V> loader)
    {
        if (!builder.isTransactional())
        {
            return buildSimpleLoadedGoogleCache(builder, loader);
        }
        else
        {
            return buildInfinispanCache(builder, loader);
        }
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    <K, V> LoadingCache<K, V> buildInfinispanCache(ru.naumen.core.server.cache.CacheBuilder configuration,
            @Nullable CacheLoader loader)
    {
        EmbeddedCacheManager manager = cacheManager.getManager();

        if (manager.getCacheConfiguration(configuration.getCacheName()) == null)
        {
            logger.info("Build infinispan cache [{}]", configuration.getCacheName());
            ConfigurationBuilder configurationBuilder = new ConfigurationBuilder();
            configurationBuilder.clustering().cacheMode(CacheMode.LOCAL);

            if (null != configuration.getRemovalListener())
            {
                throw new UnsupportedOperationException("Infinispan cache doesn`t support removal listeners");
            }

            if (notDefault(configuration.getMaxEntries()))
            {
                configurationBuilder.memory().maxCount(configuration.getMaxEntries()).whenFull(EvictionStrategy.REMOVE);
            }
            if (notDefault(configuration.getExpireAfterWriteTime()))
            {
                configurationBuilder.expiration().lifespan(configuration.getExpireAfterWriteTime(),
                        configuration.getExpireAfterWriteUnit());
            }
            if (notDefault(configuration.getExpireAfterAccessTime()))
            {
                configurationBuilder.expiration().maxIdle(configuration.getExpireAfterAccessTime(),
                        configuration.getExpireAfterAccessUnit());
            }
            if (configuration.isTransactional())
            {
                configurationBuilder.transaction().transactionMode(TransactionMode.TRANSACTIONAL);
                configurationBuilder.locking().isolationLevel(IsolationLevel.READ_COMMITTED);
            }

            manager.defineConfiguration(configuration.getCacheName(), configurationBuilder.build());
        }
        else
        {
            logger.info("Using already defined infinispan cache [{}]", configuration.getCacheName());
        }

        org.infinispan.Cache<K, V> cache = manager.getCache(configuration.getCacheName());
        return new InfinispanCacheWrapper<>(cache, loader);
    }

    private static <K, V> Cache<K, V> buildSimpleGoogleCache(ru.naumen.core.server.cache.CacheBuilder<K, V> configuration)
    {
        CacheBuilder<? super K, ? super V> cacheBuilder = initBuilder(configuration); //NOPMD 7 PMD крашится с ошибкой здесь
        return new GoogleCacheWrapper<>(cacheBuilder.build()); //NOPMD
    }

    private static <K, V> LoadingCache<K, V> buildSimpleLoadedGoogleCache(
            ru.naumen.core.server.cache.CacheBuilder<K, V> configuration, CacheLoader<K, V> loader)
    {
        CacheBuilder<? super K, ? super V> cacheBuilder = initBuilder(configuration); //NOPMD 7 PMD крашится с ошибкой здесь
        return new GoogleLoadingCacheWrapper<>(cacheBuilder.build(loader)); //NOPMD
    }

    private static <K, V> CacheBuilder<? super K, ? super V> initBuilder(
            ru.naumen.core.server.cache.CacheBuilder<K, V> configuration)
    {
        logger.info("Build simple cache [{}]", configuration.getCacheName());
        CacheBuilder<? super K, ? super V> cacheBuilder = CacheBuilder.newBuilder();

        if (notDefault(configuration.getMaxEntries()))
        {
            cacheBuilder.maximumSize(configuration.getMaxEntries());
        }
        if (notDefault(configuration.getExpireAfterWriteTime()))
        {
            cacheBuilder.expireAfterWrite(configuration.getExpireAfterWriteTime(),
                    configuration.getExpireAfterWriteUnit());
        }
        if (notDefault(configuration.getExpireAfterAccessTime()))
        {
            cacheBuilder.expireAfterAccess(configuration.getExpireAfterAccessTime(),
                    configuration.getExpireAfterAccessUnit());
        }
        // В соответствие с javadoc необходимо использовать возвращаемый CacheBuilder после установки
        // removal listener
        if (null != configuration.getRemovalListener())
        {
            cacheBuilder = cacheBuilder.removalListener(configuration.getRemovalListener());
        }
        return cacheBuilder;

    }

    private static boolean notDefault(long maxEntries)
    {
        return maxEntries != -1;
    }
}
