package ru.naumen.core.server.theme;

import static ru.naumen.core.server.theme.ThemeParameters.DEPENDENCY_PREFIX;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Predicate;

import org.apache.commons.fileupload2.core.FileItem;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Charsets;
import com.google.common.collect.Maps;

import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.cluster.external.ClusterServiceManager;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.cluster.external.ClusterEventHelper;
import ru.naumen.core.server.cluster.external.ClusteredService;
import ru.naumen.core.server.cluster.external.events.InvalidateCacheEntryEvent;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileUtils;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.interfacesettings.InterfaceSettings;
import ru.naumen.core.shared.interfacesettings.ThemeBaseSettings;
import ru.naumen.core.shared.personalsettings.Theme;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.MetainfoUtils;

/**
 * Сервис тем интерфейса.
 * Содержит также некоторые утилитарные методы, связанные с темами.
 *
 * <AUTHOR>
 * @since 05.07.22
 */
@Component
public class ThemeService implements ClusteredService
{
    public static final String CACHE_ID = "ThemePropertiesCache";
    public static final String FAKE_STYLE_ATTRIBUTE = "styleAttribute";
    private static final Logger LOG = LoggerFactory.getLogger(ThemeService.class);

    @Resource
    private ThemeService selfThemeService;
    private final MetainfoService metainfoService;
    private final FileUtils fileUtils;
    private final MessageFacade messages;
    private final UploadService uploadService;
    private final MetainfoUtils metainfoUtils;
    private final ClusterServiceManager clusterManager;
    private final boolean isClusterMode;

    @Inject
    public ThemeService(MetainfoService metainfoService, FileUtils fileUtils, MessageFacade messages,
            UploadService uploadService, MetainfoUtils metainfoUtils,
            ClusterServiceManager clusterManager, ClusterInfoService clusterInfoService)
    {
        this.metainfoService = metainfoService;
        this.fileUtils = fileUtils;
        this.messages = messages;
        this.uploadService = uploadService;
        this.metainfoUtils = metainfoUtils;
        this.clusterManager = clusterManager;
        this.isClusterMode = clusterInfoService.isNormalClusterMode();
    }

    private final Map<String, String> themesCache = new ConcurrentHashMap<>();

    /**
     * Параметры, общие для всех тем.
     * В конкретных темах их изменять нельзя.
     */
    private static final Set<String> THEMES_BASE_PARAM_NAMES = Set.of(
        ThemeParameters.searchResultsColor.name(),
        ThemeParameters.searchResultsBackground.name(),
        ThemeParameters.modeMarkerBackground.name(),
        ThemeParameters.modeMarkerHoverBackground.name(),
        ThemeParameters.modeMarkerPressedBackground.name(),
        ThemeParameters.messageErrorColor.name(),
        ThemeParameters.messageErrorBackground.name(),
        ThemeParameters.messageInfoColor.name(),
        ThemeParameters.messageInfoBackground.name(),
        ThemeParameters.messageWarningColor.name(),
        ThemeParameters.messageWarningButtonColor.name(),
        ThemeParameters.messageWarningBackground.name(),
        ThemeParameters.messageActionWarningBackground.name(),
        ThemeParameters.messageActionInformationBackground.name(),
        ThemeParameters.messageActionSuccessColor.name(),
        ThemeParameters.messageActionSuccessBackground.name(),
        ThemeParameters.messageInformationBackground.name(),
        ThemeParameters.messageInformationColor.name(),
        ThemeParameters.privateCommentBadgeBackground.name(),
        ThemeParameters.privateCommentBadgeColor.name());

    //@formatter:on

    @Override
    public void bindToCluster(ClusterEventHelper bus)
    {
        bus.registerEventHandler(InvalidateCacheEntryEvent.class, (event, eventSource, selfEvent) ->
        {
            if (!CACHE_ID.equals(event.getCacheId()) || !(event.getKey() instanceof String key))
            {
                return;
            }
            evictThemes(key, false);
        });
    }

    /**
     * Очистить кэш с параметрами тем
     */
    public void evictThemes()
    {
        evictThemes(null);
    }

    /**
     * Удалить из кэша параметры темы с указанным кодом
     */
    public void evictThemes(@Nullable String themeCode)
    {
        evictThemes(themeCode, true);
    }

    /**
     * Удалить из кэша параметры темы с указанным кодом
     */
    private void evictThemes(@Nullable String themeCode, boolean fireClusterEvent)
    {
        if (themeCode == null)
        {
            themesCache.clear();
        }
        else
        {
            themesCache.remove(themeCode);
        }
        if (isClusterMode && fireClusterEvent)
        {
            clusterManager.sendEvent(new InvalidateCacheEntryEvent(CACHE_ID, themeCode));
        }
    }

    /**
     * Получить полный список параметров для темы в виде JSON
     * @param themeCode код темы
     */
    public String getThemeParametersJson(String themeCode)
    {
        return themesCache.computeIfAbsent(themeCode, key -> mapToJson(calculateThemeParams(key)));
    }

    /**
     * Проверяет возможность сделать тему доступной/недоступной для выбора пользователями.
     * При попытке отключения темы, выбранной по умолчанию, возникает {@link FxException исключение}.
     */
    public void validateThemeAvailabilitySwitch(String themeCode, boolean isEnabled, InterfaceSettings settings)
    {
        if (!isEnabled && themeCode.equals(settings.getThemeOperator()))
        {
            String themeTitle = getThemeTitle(themeCode, settings);
            throw new FxException(messages.getMessage("metainfo.SwitchThemeError", themeTitle));
        }
    }

    /**
     * Получить параметры, общие для всех тем
     */
    private Map<String, String> getThemesBaseParamsMap()
    {
        String baseProperties = getThemesBaseParams();
        return getThemeParamsMap(baseProperties, false);
    }

    /**
     * Получить параметры, общие для всех тем в виде строки
     */
    private String getThemesBaseParams()
    {
        return fileUtils.getFileContentAsString(getInterfaceSettingsSafe().getThemesBaseParamsFileUuid());
    }

    /**
     * Получить безопасно (не null) настройки интерфейса
     */
    private InterfaceSettings getInterfaceSettingsSafe()
    {
        InterfaceSettings interfaceSettings = metainfoService.getInterfaceSettings();
        return interfaceSettings == null ? new InterfaceSettings() : interfaceSettings;
    }

    /**
     * Проверка корректности параметров тем
     * @param paramsStr параметры в виде строки
     * @param isBaseParams признак того, что параметры - общие для всех тем
     */
    public void validateThemeParams(String paramsStr, boolean isBaseParams)
    {
        Map<String, String> themeParams = isBaseParams
                ? null
                : getThemeParamsMap(paramsStr, true);

        Map<String, String> allThemesBaseParams = isBaseParams
                ? getThemeParamsMap(paramsStr, true)
                : getThemesBaseParamsMap();

        mapToJson(calculateThemeParams(allThemesBaseParams, themeParams, true));
    }

    /**
     * Получение мапы со стилями из файла (в виде строки).
     * @param themeProperties параметры темы в виде строки
     * @param needValidateParams признак того, что необходима валидация содержимого с выбросом исключения
     */
    private HashMap<String, String> getThemeParamsMap(String themeProperties, boolean needValidateParams)
    {
        HashMap<String, String> resultThemeProperties = Maps.newHashMap();
        String[] lines = themeProperties.split("\n");
        List<String> invalidLines = new ArrayList<>();
        for (String line : lines)
        {
            line = StringUtilities.trim(line);

            // Пустую строку, либо строку с комментарием обрабатывать не нужно
            if (line.isEmpty() || line.startsWith(ThemeParameters.COMMENT_PREFIX))
            {
                continue;
            }

            if (!line.contains(ThemeParameters.DEFINE_PREFIX))
            {
                invalidLines.add(line);
                continue;
            }

            String[] paramEntry = line.split(ThemeParameters.DEFINE_PREFIX);
            if (paramEntry.length != 2)
            {
                invalidLines.add(line);
                continue;
            }

            resultThemeProperties.put(paramEntry[0].trim(), paramEntry[1].trim());
        }
        if (needValidateParams)
        {
            checkInvalidParamNames(invalidLines);
        }
        return resultThemeProperties;
    }

    /**
     * Получить мапу с параметрами тем по коду темы
     */
    private HashMap<String, String> getThemeParamsMapForTheme(String themeCode)
    {
        String themeProperties = selfThemeService.getThemeParametersString(themeCode);
        return getThemeParamsMap(themeProperties, false);
    }

    @Cacheable(CACHE_ID)
    public String getThemeParametersString(String themeCode)
    {
        try
        {
            Theme theme = metainfoService.getTheme(themeCode);
            if (theme.isSystem())
            {
                ClassPathResource resource = new ClassPathResource(theme.getParamsFile());
                return IOUtils.toString(resource.getInputStream(), Charsets.UTF_8);
            }
            else
            {
                return fileUtils.getFileContentAsString(theme.getParamsFile());
            }
        }
        catch (Exception e)
        {
            LOG.warn("Theme properties were not loaded for theme '" + themeCode + "'. Reason: " + e.getMessage(), e);
            return "";
        }
    }

    /**
     * Сохранить файл с параметрами темы
     * @param uuid временного файла (загруженного на форме)
     */
    public String saveThemeParamsFile(@Nullable String uuid)
    {
        if (StringUtilities.isEmpty(uuid))
        {
            return uuid;
        }
        FileItem tempFile = uploadService.get(uuid);
        String fileName = tempFile.getName();
        String contentType = tempFile.getContentType();
        uploadService.delete(uuid);
        File file = fileUtils.saveSystemFile(null, FAKE_STYLE_ATTRIBUTE, fileName, contentType, tempFile.getString().getBytes());
        return file.getUUID();
    }

    /**
     * Получить название темы
     * @param themeCode код темы
     * @param settings настройки интерфейса
     */
    public String getThemeTitle(String themeCode, @Nullable InterfaceSettings settings)
    {
        ThemeBaseSettings baseSettings = settings == null ? null : settings.getThemeBaseSettings().get(themeCode);
        if (null == baseSettings || baseSettings.isThemeTitleStandard())
        {
            Optional<Theme> themeOptional = metainfoService.getThemes().stream()
                    .filter(p -> p.getCode().equals(themeCode))
                    .findFirst();
            return themeOptional.map(theme -> metainfoUtils.getLocalizedValue(theme.getTitle())).orElse(null);
        }
        return metainfoUtils.getLocalizedValue(baseSettings.getTitle());
    }

    /**
     * Вычисление параметров тем
     * @param themeCode код темы
     */
    private Map<String, String> calculateThemeParams(String themeCode)
    {
        return calculateThemeParams(getThemesBaseParamsMap(),
                getThemeParamsMapForTheme(themeCode), false);
    }

    /**
     * Вычислить окончательные параметры темы
     * @param allThemesBaseParams общие параметры для всех тем
     * @param themeParams параметры для конкретной темы
     * @param needValidateParams нужно ли проверять корректность параметров тем
     *        (для существующих тем - не проверять)
     * @return результирующие параметры
     */
    private Map<String, String> calculateThemeParams(@Nullable Map<String, String> allThemesBaseParams,
            @Nullable Map<String, String> themeParams, boolean needValidateParams)
    {
        Map<String, String> result = initDefaultParams();

        if (allThemesBaseParams != null)
        {
            if (needValidateParams)
            {
                // Проверить, что все используемые имена параметров существуют
                validateThemeParamNames(allThemesBaseParams);
                // Проверить, что все значения не пустые
                checkEmptyValues(allThemesBaseParams);
                // Временно запретить использование специфических параметров тем в общих параметрах
                forbidSpecificParams(allThemesBaseParams);
                // Временно запретить зависимости в файлах общих параметров
                forbidDependencies(allThemesBaseParams);
            }
            overrideParams(result, allThemesBaseParams, false);
        }

        if (themeParams != null)
        {
            if (needValidateParams)
            {
                validateThemeParamNames(themeParams);
                // Проверить, что нет пустых значений
                checkEmptyValues(themeParams);
                // Запретить использование общих параметров в параметрах конкретной темы
                forbidBaseParams(themeParams);
                // Запретить зависимости в файлах конкретных тем
                forbidDependencies(themeParams);
            }
            overrideParams(result, themeParams, true);
        }

        return resolveDependencies(result);
    }

    /**
     * Запретить использование базовых параметров тем
     * @param themeParams параметры
     */
    private void forbidBaseParams(Map<String, String> themeParams)
    {
        checkParamNames(themeParams, name -> !THEMES_BASE_PARAM_NAMES.contains(name));
    }

    /**
     * Запретить использование специфических параметров тем (все, кроме базовых)
     * @param themeParams параметры
     */
    private void forbidSpecificParams(Map<String, String> themeParams)
    {
        checkParamNames(themeParams, THEMES_BASE_PARAM_NAMES::contains);
    }

    /**
     * Проверка на отсутствие зависимостей ($) в значениях параметров (от других параметров)
     * @param themeParams параметры
     */
    private void forbidDependencies(Map<String, String> themeParams)
    {
        checkParamValues(themeParams, value -> !value.contains(DEPENDENCY_PREFIX));
    }

    /**
     * Проверка на отсутствие зависимостей ($) в значениях параметров (от других параметров)
     * @param themeParams параметры
     */
    private void checkEmptyValues(Map<String, String> themeParams)
    {
        checkParamValues(themeParams, value -> !value.isEmpty());
    }

    /**
     * Проверить значения параметров
     * @param themeParams параметры
     * @param validator предикат валидации
     */
    private void checkParamValues(Map<String, String> themeParams, Predicate<String> validator)
    {
        final List<String> invalidParams = new ArrayList<>();
        themeParams.forEach((key, value) ->
        {
            if (!validator.test(value))
            {
                invalidParams.add(key);
            }
        });
        checkInvalidParamValues(invalidParams);
    }

    /**
     * Проверить названия параметров
     * @param themeParams параметры
     * @param validator предикат валидации
     */
    private void checkParamNames(Map<String, String> themeParams, Predicate<String> validator)
    {
        final List<String> invalidParams = new ArrayList<>();
        themeParams.keySet().forEach(key ->
        {
            if (!validator.test(key))
            {
                invalidParams.add(key);
            }
        });
        checkInvalidParamNames(invalidParams);
    }

    /**
     * Проверить корректность имен параметров тем
     */
    private void validateThemeParamNames(Map<String, String> params)
    {
        final List<String> invalidParamNames = new ArrayList<>();
        params.keySet().forEach(param -> validateThemeParam(param, invalidParamNames));
        checkInvalidParamNames(invalidParamNames);
    }

    private void checkInvalidParamNames(List<String> invalidParamNames)
    {
        checkInvalidParams(invalidParamNames, "themeParamsInvalidNames");
    }

    private void checkInvalidParamValues(List<String> invalidParamNames)
    {
        checkInvalidParams(invalidParamNames, "themeParamsInvalidValues");
    }

    /**
     * Выбросить исключение о наличии в файле некорректных параметров,
     * если invalidParamNames не пуст
     * @param invalidParamNames список недопустимых параметров
     * @param msgCode код сообщения из messages
     */
    private void checkInvalidParams(List<String> invalidParamNames, String msgCode)
    {
        if (invalidParamNames.isEmpty())
        {
            return;
        }
        throw new FxException(messages.getMessage(msgCode, StringUtilities.joinQuoted(invalidParamNames)));
    }

    /**
     * Разрешить зависимости параметров тем друг от друга
     */
    private Map<String, String> resolveDependencies(Map<String, String> unresolvedParams)
    {
        Map<String, String> resolvedParams = new HashMap<>();

        Set<String> unresolvedKeys = Set.copyOf(unresolvedParams.keySet());
        unresolvedKeys.forEach(key ->
        {
            String value = unresolvedParams.get(key);
            if (!value.contains(DEPENDENCY_PREFIX))
            {
                unresolvedParams.remove(key);
                resolvedParams.put(key, value);
            }
        });

        while (!unresolvedParams.isEmpty())
        {
            int initUnresolvedSize = unresolvedParams.size();

            unresolvedKeys = Set.copyOf(unresolvedParams.keySet());
            List<String> invalidParams = new ArrayList<>();
            unresolvedKeys.forEach(key ->
            {
                String paramName = stripDependency(unresolvedParams.get(key));
                validateThemeParam(paramName, invalidParams);
                String resovedValue = resolvedParams.get(paramName);
                if (StringUtilities.isNotEmpty(resovedValue))
                {
                    unresolvedParams.remove(key);
                    resolvedParams.put(key, resovedValue);
                }
            });
            checkInvalidParamNames(invalidParams);

            checkCyclicDependency(unresolvedParams, initUnresolvedSize);
        }

        return resolvedParams;
    }

    /**
     * Проверить круговые зависимости в параметрах тем
     */
    private void checkCyclicDependency(Map<String, String> unresolvedMap, int initUnresolvedSize)
    {
        if (unresolvedMap.size() == initUnresolvedSize)
        {
            List<String> circleParams = getCyclicDependencyParams(unresolvedMap);
            throw new FxException(messages.getMessage("themeParamsCyclicDependency"
                    + StringUtilities.joinQuoted(circleParams)));
        }
    }

    /**
     * Получить список параметров, участвующих в циклической зависимости
     */
    private static List<String> getCyclicDependencyParams(Map<String, String> unresolvedMap)
    {
        List<String> circledParams = new ArrayList<>();
        String key = unresolvedMap.keySet().iterator().next();
        while (!circledParams.contains(key))
        {
            circledParams.add(key);
            key = stripDependency(unresolvedMap.get(key));
        }

        // Удалить все элементы до начала "круговой" зависимости
        while (circledParams.indexOf(key) != 0)
        {
            circledParams.remove(0);
        }

        return circledParams;
    }

    /**
     * Валидация зависимости:
     * Проверяется то, что зависимость является указанием на реально существующий параметр.
     * Например $textColor - валидная зависимость
     */
    private static void validateThemeParam(String paramName, Collection<String> invalidParams)
    {
        try
        {
            ThemeParameters.valueOf(paramName);
        }
        catch (Exception e)
        {
            invalidParams.add(paramName);
        }
    }

    /**
     * Получить имя параметра из зависимости
     */
    private static String stripDependency(String dependency)
    {
        return dependency.substring(DEPENDENCY_PREFIX.length());
    }

    /**
     * Переопределить параметры тем
     * @param result результирующая мапа
     * @param paramsToOverride значения для переопределения
     * @param checkThemesBaseParams учесть базовые(общие) параметры тем
     */
    private static void overrideParams(Map<String, String> result, Map<String, String> paramsToOverride,
            boolean checkThemesBaseParams)
    {
        paramsToOverride.forEach((key, value) ->
        {
            if (!result.containsKey(key))
            {
                return;
            }
            // Если нужно учитывать параметры для всех тем (checkAllThemesParams)
            // то параметры, имеющиеся в списке allThemesParamNames вносить в результат не нужно.
            if (checkThemesBaseParams && THEMES_BASE_PARAM_NAMES.contains(key))
            {
                return;
            }
            result.put(key, value);
        });
    }

    /**
     * Инициализация мапы со всеми параметрами тем
     * с захардкоженными значениями по умолчанию
     */
    private static Map<String, String> initDefaultParams()
    {
        Map<String, String> params = Maps.newHashMap();
        Arrays.stream(ThemeParameters.values())
                .forEach(param -> params.put(param.name(), param.getDefaultValue()));
        return params;
    }

    private static String mapToJson (Map<String, String> map)
    {
        try
        {
            return new ObjectMapper().writeValueAsString(map);
        }
        catch (JsonProcessingException e)
        {
            throw new FxException(e.getLocalizedMessage(), e);
        }
    }
}
