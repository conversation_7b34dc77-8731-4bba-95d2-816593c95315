/**
 * 
 */
package ru.naumen.core.server.timer.bcp.actions;

import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.timer.AbstractTimer;
import ru.naumen.core.server.timer.bcp.TimerCalculationContext;
import ru.naumen.core.server.timer.bcp.operations.TimerStatusChangeOperations;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.timer.AbstractTimerDto;

/**
 * Действие, выполняемое при смене статуса счетчика времени
 * {@link AbstractTimer}
 * 
 * <AUTHOR>
 * @since 21.05.2013
 *
 */
public interface TimerStatusChangeAction<T extends AbstractTimer, DT extends AbstractTimerDto>
{
    void execute(TimerCalculationContext<T, DT> timerCalcContext, IHasObjectBOContext<IHasMetaInfo> context,
            TimerStatusChangeOperations<T, DT> operations);
}