package ru.naumen.core.server.mbean.keystore;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.keystore.KeyStoreService;
import ru.naumen.core.server.mbean.OpenTypeAdapter;

import jakarta.inject.Inject;
import javax.management.openmbean.TabularData;

/**
 * Реализация MBean {@link AppKeyStoreMBean}
 * <AUTHOR>
 * @since 11.09.2019
 */
@Component
public class AppKeyStore implements AppKeyStoreMBean
{
    private final KeyStoreService keyStoreService;

    @Inject
    public AppKeyStore(KeyStoreService keyStoreService)
    {
        this.keyStoreService = keyStoreService;
    }

    @Override
    public TabularData getCertificates()
    {
        return OpenTypeAdapter.intoTabularData(keyStoreService.getAppKeyStoreCertificates());
    }
}
