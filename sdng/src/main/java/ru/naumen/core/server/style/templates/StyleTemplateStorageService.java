package ru.naumen.core.server.style.templates;

import java.util.List;

import ru.naumen.core.server.cache.ClusterCacheService;
import ru.naumen.metainfo.shared.style.templates.StyleTemplate;

/**
 * Хранилище шаблонов стилей.
 * <AUTHOR>
 * @since Dec 12, 2016
 */
public interface StyleTemplateStorageService extends ClusterCacheService
{
    /**
     * Сохраняет новый шаблон стилей. Проверяет код шаблона на уникальность.
     * @param template шаблон стилей
     */
    void addTemplate(StyleTemplate template);
    
    /**
     * Удаляет шаблоны стилей с указанными кодами.
     * @param codes коды шаблонов стилей
     */
    void deleteTemplates(String... codes);

    /**
     * Возвращает все имеющиеся в системе шаблоны стилей.
     * @return все шаблоны стилей
     */
    List<StyleTemplate> getAll();

    /**
     * Возвращает шаблон стилей, имеющий указанный код.
     * @param code код шаблона
     * @return шаблон стилей или null, если шаблона с указанным кодом не существует
     */
    StyleTemplate getTemplate(String code);

    /**
     * Сохраняет указанный шаблон стилей.
     * @param template шаблон стилей
     */
    void saveTemplate(StyleTemplate template);
}
