/**
 * 
 */
package ru.naumen.core.server.timer.bcp.conditionstrategies;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.timer.bcp.TimerCalculationContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.timer.Status;
import ru.naumen.core.shared.timer.definition.MetaClassState;
import ru.naumen.core.shared.timer.definition.StatusTimerCondition;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Реализация {@link TimerConditionStrategy} для {@link StatusTimerCondition условия} запуска счетчика "По смене статуса" 
 */
@Component
public class TimerConditionStrategyStateImpl implements TimerConditionStrategy<StatusTimerCondition>
{
    @Inject
    private MetainfoService metainfoService;
    @Inject
    protected AccessorHelper accessorHelper;

    @Override
    public Status getState(TimerCalculationContext<?, ?> timerCalcContext, IHasObjectBOContext<IHasMetaInfo> context)
    {
        IHasMetaInfo object = context.getObject();
        ClassFqn fqn = object.getMetaClass();
        String objState = accessorHelper.getAttributeValueWithoutPermission(object, Constants.HasState.STATE);
        StatusTimerCondition condition = (StatusTimerCondition)timerCalcContext.getTimerDefinition()
                .getTimerCondition();
        boolean stopDeniedInChild = false;
        while (null != fqn)
        {
            MetaClassState metaClassState = new MetaClassState(fqn, objState);
            if (condition.getStopDenied().contains(metaClassState))
            {
                stopDeniedInChild = true;
            }
            if (condition.getStopStates().contains(metaClassState) && !stopDeniedInChild)
            {
                return Status.STOPED;
            }
            if (condition.getDenied().contains(metaClassState))
            {
                return Status.NOTSTARTED.equals(timerCalcContext.getCurrentStatus()) ? Status.NOTSTARTED
                        : Status.PAUSED;
            }
            if (condition.getSelected().contains(metaClassState))
            {
                return Status.ACTIVE;
            }
            fqn = metainfoService.getMetaClass(fqn).getParent();
        }
        return Status.NOTSTARTED.equals(timerCalcContext.getCurrentStatus()) ? Status.NOTSTARTED : Status.PAUSED;
    }
}