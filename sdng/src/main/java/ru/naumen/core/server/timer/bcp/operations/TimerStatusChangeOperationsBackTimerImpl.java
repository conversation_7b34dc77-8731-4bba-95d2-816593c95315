/**
 *
 */
package ru.naumen.core.server.timer.bcp.operations;

import java.util.Date;
import java.util.Set;

import org.apache.commons.lang.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.collect.ImmutableSet;

import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.core.server.timer.AbstractBackTimerAccessor;
import ru.naumen.core.server.timer.BackTimer;
import ru.naumen.core.server.timer.TimerUtils.TimerContext;
import ru.naumen.core.server.timer.bcp.TimerCalculationContext;
import ru.naumen.core.server.timer.bcp.TimerLoggingHelper;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.timer.BackTimerDto;
import ru.naumen.core.shared.timer.Status;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * <AUTHOR>
 * @since 21.05.2013
 *
 */
@Component
public class TimerStatusChangeOperationsBackTimerImpl extends TimerStatusChangeOperationsImpl<BackTimer, BackTimerDto>
{
    public static final Logger LOG = LoggerFactory.getLogger(TimerStatusChangeOperationsBackTimerImpl.class);
    private static final Set<Status> ACTIVE_STATUSES = ImmutableSet.of(Status.ACTIVE);
    private static final Set<Status> PASSIVE_STATUSES = ImmutableSet.of(Status.NOTSTARTED, Status.PAUSED,
            Status.STOPED);

    @Override
    public BackTimer createTimer(Attribute attribute)
    {
        return timerUtils.newBackTimerInstance(attribute);
    }

    @Override
    public void doActivate(TimerCalculationContext<BackTimer, BackTimerDto> timerCalcContext,
            IHasObjectBOContext<IHasMetaInfo> context)
    {
        String prefixLogs = TimerLoggingHelper.getPrefixLogs(timerCalcContext.getAttribute(), context);
        timerCalcContext.setOldStartTime(timerCalcContext.getTimer().getStartTime());
        long startTime = null == timerCalcContext.getLastElapsedComputationTime() ? System.currentTimeMillis()
                : timerCalcContext.getLastElapsedComputationTime().getTime();
        LOG.debug("{} Set start time: {}", prefixLogs, startTime);
        timerCalcContext.getTimer().setStartTime(startTime);
        if (timerCalcContext.getTimerDefinition().isEnableRecalcOnServiceTimeChange() &&
                PASSIVE_STATUSES.contains(timerCalcContext.getCurrentStatus()) &&
                ACTIVE_STATUSES.contains(timerCalcContext.getNewStatus()))
        {
            if (timerCalcContext.getTimer().isLastIntervalStarted())
            {
                //Данная ситуация может возникнуть в случае, если в TimerDefinition для имеющихся счетчиков
                //(в состоянии, отличном от NOTSTARTED) выставили свойство isEnableRecalcOnServiceTimeChange.
                //Такая ситуация маловероятна в нормальном бизнес-процессе, поэтому договорились
                //закрывать текущий активный интервал текущим временем и ругаться в лог.
                LOG.info("{} {} It will be finalized with current time.", prefixLogs,
                        BackTimer.UNFINALIZED_ACTIVE_INTERVAL_MSG);
                timerCalcContext.getTimer().finalizeActiveInterval(System.currentTimeMillis());
            }
            if (LOG.isDebugEnabled())
            {
                LOG.debug("{} Set start active interval: {}", prefixLogs,
                        timerCalcContext.getTimer().getStartTime());
            }
            timerCalcContext.getTimer().startActiveInterval(timerCalcContext.getTimer().getStartTime());
        }
        IHasMetaInfo object = context.getObject();
        Long deadLine = timerUtils.getDeadLine(object, timerCalcContext.getAttribute(), timerCalcContext.getTimer(),
                Status.ACTIVE);
        timerCalcContext.setOldDeadLineTime(timerCalcContext.getTimer().getDeadLineTime());
        if (LOG.isDebugEnabled())
        {
            LOG.debug("{} Old deadline time: {} , New deadline time: {}", prefixLogs,
                    timerCalcContext.getTimer().getDeadLineTime(), deadLine);
        }
        timerCalcContext.getTimer().setDeadLineTime(deadLine);
        if (null != deadLine && deadLine < System.currentTimeMillis())
        {
            LOG.debug("{} Deadline passed,set status EXCEED", prefixLogs);
            timerCalcContext.getTimer().setStatus(Status.EXCEED);
        }
    }

    @Override
    public void doPassivate(TimerCalculationContext<BackTimer, BackTimerDto> timerCalcContext,
            IHasObjectBOContext<IHasMetaInfo> context)
    {
        String prefixLogs = TimerLoggingHelper.getPrefixLogs(timerCalcContext.getAttribute(), context);
        if (timerCalcContext.getTimerDefinition().isEnableRecalcOnServiceTimeChange() &&
                ACTIVE_STATUSES.contains(timerCalcContext.getCurrentStatus()) &&
                PASSIVE_STATUSES.contains(timerCalcContext.getNewStatus()))
        {
            if (timerCalcContext.getTimer().isLastIntervalFinalized())
            {
                //Данная ситуация может возникнуть в случае, если в TimerDefinition для имеющихся счетчиков
                //(в состоянии, отличном от NOTSTARTED) выставили свойство isEnableRecalcOnServiceTimeChange.
                //Такая ситуация маловероятна в нормальном бизнес-процессе, поэтому договорились
                //открывать интервал текущим временем и ругаться в лог.
                LOG.info("{} {} New interval will be started with current time.",
                        prefixLogs, BackTimer.NO_ACTIVE_INTERVAL_MSG);
                timerCalcContext.getTimer().startActiveInterval(timerCalcContext.getTimer().getStartTime());
            }
            long endActiveInterval = System.currentTimeMillis();
            LOG.debug("{} Finalize active interval: {}", prefixLogs,
                    endActiveInterval);
            timerCalcContext.getTimer().finalizeActiveInterval(endActiveInterval);
        }

        if (isCanStart(timerCalcContext.getTimerDefinition(), context))
        {
            Long deadLineTime = timerCalcContext.getTimer().getDeadLineTime();
            if (deadLineTime != null && deadLineTime < System.currentTimeMillis())
            {
                LOG.debug("{} Deadline passed, set status EXCEED", prefixLogs);
                timerCalcContext.getTimer().setStatus(Status.EXCEED);
                return;
            }
            Date elapsedComputationTime = new Date();
            timerCalcContext.setLastElapsedComputationTime(elapsedComputationTime);
            TimerContext timerContext = timerUtils.getTimerContext(context, timerCalcContext.getObjectMetaClass(),
                    timerCalcContext.getTimerDefinition(), elapsedComputationTime);
            LOG.debug("{} Received timer context: {}", prefixLogs,
                    timerContext.toString());
            Long elapsed = timerUtils.getElapsed(timerCalcContext.getAttribute(), timerCalcContext.getTimer(),
                    timerContext);
            timerCalcContext.getTimer().setElapsed(elapsed);
        }
        else
        {
            timerCalcContext.getTimer().setElapsed(0L);
        }
        if (LOG.isDebugEnabled())
        {
            LOG.debug("{} Set old start time: {}", prefixLogs,
                    timerCalcContext.getTimer().getStartTime());
        }
        timerCalcContext.setOldStartTime(timerCalcContext.getTimer().getStartTime());
        timerCalcContext.getTimer().setStartTime(0L);

        DateTimeInterval interval = timerUtils.getTimerContext(false, context.getObject(), timerCalcContext
                .getAttribute()).getResolutionTime();
        if (interval != null
                && interval.toMiliseconds() != null
                && (interval.toMiliseconds() - timerCalcContext.getTimer().getElapsed() < 0))
        {
            long newDeadline = System.currentTimeMillis() - 1;
            LOG.debug("{} Interval exceeded", prefixLogs);
            timerCalcContext.getTimer().setStatus(Status.EXCEED);
            timerCalcContext.getTimer().setDeadLineTime(newDeadline);
        }
        LOG.debug("{} Timer after stopping process {}", prefixLogs,
                timerCalcContext.getTimer());
    }

    @Override
    public Status getNewStatus(TimerCalculationContext<BackTimer, BackTimerDto> timerCalcContext)
    {
        return Status.EXCEED.equals(timerCalcContext.getTimer().getStatus()) ? Status.EXCEED
                : timerCalcContext
                        .getNewStatus();
    }

    @Override
    public BackTimerDto getOldAttributeValue(TimerCalculationContext<BackTimer, BackTimerDto> timerCalcContext,
            IHasObjectBOContext<IHasMetaInfo> context)
    {
        BackTimer timer = timerCalcContext.getTimer();
        if (null == timer)
        {
            return null;
        }
        TimerDefinition timerDefinition = timerUtils.getTimerDefinition(timerCalcContext.getAttribute());
        if (timerDefinition == null)
        {
            return null;
        }
        Date allowanceStart = new Date();
        TimerContext timerContext = timerUtils.getTimerContext(context, timerCalcContext.getObjectMetaClass(),
                timerDefinition, allowanceStart);

        Status status = timer.getStatus();
        Long deadLine = timer.getDeadLineTime();
        if (Status.ACTIVE.equals(status) && deadLine != null && deadLine < System.currentTimeMillis())
        {
            status = Status.EXCEED;
        }

        Date deadLineTime = AbstractBackTimerAccessor.extractDeadLine(timer, status);
        Long allowance = timerUtils.getAllowance(timerCalcContext.getAttribute(), timer, status, timerContext);
        Long elapsedFromOverdue = timerUtils.getElapsedFromOverdue(timerCalcContext.getAttribute(), timer,
                timerContext, status, timer.getStartTime());
        return new BackTimerDto(status, deadLineTime, allowance, elapsedFromOverdue, allowanceStart);
    }

    @Override
    public boolean isCanStart(TimerDefinition timerDefinition, IHasObjectBOContext<IHasMetaInfo> context)
    {
        // @formatter:off
        return     super.isCanStart(timerDefinition, context)
               &&  null != getAttributeValue(timerDefinition.getResolutionTimeAttribute(), context);
        // @formatter:on
    }

    @Override
    public boolean isChanged(TimerCalculationContext<BackTimer, BackTimerDto> timerCalcContext)
    {
        BackTimerDto oldTimer = timerCalcContext.getOldTimer();
        Long oldDeadLine = timerCalcContext.getOldDeadLineTime();
        if (null != oldTimer && null != oldTimer.getDeadLineTime())
        {
            oldDeadLine = oldTimer.getDeadLineTime().getTime();
        }
        return super.isChanged(timerCalcContext)
                || !ObjectUtils.equals(timerCalcContext.getTimer().getDeadLineTime(), oldDeadLine);
    }

    @Override
    public void logChange(TimerCalculationContext<BackTimer, BackTimerDto> timerCalcContext,
            IHasObjectBOContext<IHasMetaInfo> context)
    {
        super.logChange(timerCalcContext, context);
        String prefixLogs = TimerLoggingHelper.getPrefixLogs(timerCalcContext.getAttribute(), context);
        if (!ObjectUtils.equals(timerCalcContext.getTimer().getDeadLineTime(), timerCalcContext.getOldDeadLineTime())
                && LOG.isDebugEnabled())
        {
            LOG.debug("{} Timer change deadline {} => {}", prefixLogs,
                    timerCalcContext.getOldDeadLineTime(), timerCalcContext.getTimer().getDeadLineTime());
        }
    }
}