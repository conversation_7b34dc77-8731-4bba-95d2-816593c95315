package ru.naumen.core.server.style.templates;

import static ru.naumen.metainfo.server.Constants.STYLE_TEMPLATE;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.infinispan.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.cache.HasISCache;
import ru.naumen.core.server.cache.infinispan.ISCacheProvider;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.spi.IServiceInitializer;
import ru.naumen.metainfo.shared.style.templates.StyleTemplate;

/**
 * Реализация хранилища шаблонов стилей. Хранит элементы в кэше.
 * <AUTHOR>
 * @since Dec 12, 2016
 */
@Component
public class StyleTemplateStorageServiceImpl implements StyleTemplateStorageService, HasISCache
{
    private static final Logger LOG = LoggerFactory.getLogger(StyleTemplateStorageServiceImpl.class);

    private final ISCacheProvider cacheProvider;
    private final PlatformTransactionManager txManager;
    private final MessageFacade messages;
    private final List<IServiceInitializer<StyleTemplateStorageService>> initializers;

    @Inject
    public StyleTemplateStorageServiceImpl(
            ISCacheProvider cacheProvider,
            PlatformTransactionManager txManager,
            MessageFacade messages,
            @Named(StyleTemplateStorageConfiguration.STYLE_TEMPLATE_STORAGE_INITIALIZERS_NAME)
            List<IServiceInitializer<StyleTemplateStorageService>> initializers)
    {
        this.cacheProvider = cacheProvider;
        this.txManager = txManager;
        this.messages = messages;
        this.initializers = initializers;
    }

    @Override
    public void addTemplate(StyleTemplate template)
    {
        Objects.requireNonNull(template);
        if (cacheProvider.containsKey(STYLE_TEMPLATE, template.getCode()))
        {
            throw new FxException(messages.getMessage("styletemplates-unableToAddNonUniqueCode", template.getCode()));
        }
        cacheProvider.put(STYLE_TEMPLATE, template.getCode(), template);
    }

    @Override
    public void deleteTemplates(String... codes)
    {
        for(String code : codes)
        {
            cacheProvider.remove(STYLE_TEMPLATE, code);
        }
    }

    @Override
    public List<StyleTemplate> getAll()
    {
        return new ArrayList<>(cacheProvider.getAllValues(STYLE_TEMPLATE));
    }

    @Override
    public <K, V> Cache<K, V> getCache()
    {
        return cacheProvider.getCache(STYLE_TEMPLATE);
    }

    @Override
    public StyleTemplate getTemplate(String code)
    {
        return cacheProvider.get(STYLE_TEMPLATE, code);
    }

    @PostConstruct
    public void init()
    {
        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.setTimeout(600); // время инициализации сервиса не должно превышать 10 минут
        tt.execute((TransactionCallback<Void>)status ->
        {
            cacheProvider.initCache(STYLE_TEMPLATE);
            processInitializers();
            return null;
        });
    }

    @Override
    public void reloadCache(Set<String> keys)
    {
        initializers.forEach(initializer -> initializer.initialize(this, keys));
    }

    @Override
    public void saveTemplate(StyleTemplate template)
    {
        Objects.requireNonNull(template);
        cacheProvider.put(STYLE_TEMPLATE, template.getCode(), template);
    }

    private void processInitializers()
    {
        for (IServiceInitializer<StyleTemplateStorageService> initializer : initializers)
        {
            LOG.debug("Process initializing {}", initializer.getClass().getSimpleName());
            initializer.initialize(this);
            LOG.debug("Finished initializing {}", initializer.getClass().getSimpleName());
        }
    }

    @Override
    public String getMetaRegion()
    {
        return STYLE_TEMPLATE;
    }
}