package ru.naumen.core.client.kendo;

import static ru.naumen.core.shared.Constants.COMMON_RESOURCE_PATH;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.inject.resource.ResourcesInitializerBase;
import ru.naumen.core.client.utils.LocaleInfoImpl;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.core.shared.utils.ReadyState;

/**
 * Инициализация ресурсов для Kendo
 *
 * <AUTHOR>
 * @since 20.12.19
 */
@Singleton
public class KendoInitializer extends ResourcesInitializerBase
{
    @Inject
    private KendoResources kendoResources;

    @Override
    public String getResourcePath()
    {
        return COMMON_RESOURCE_PATH + "kendo/";
    }

    @Override
    protected String[] getDefaultResources()
    {
        //@formatter:off
        return new String[] {
                "kendo.common-material.min.css",
                "kendo.material.min.css",
                "kendo.all.min.js",
                "kendo.core.min.js" };
        //@formatter:on
    }

    @Override
    protected void initAdditionalResources(ReadyState readyState)
    {
        if (!ILocaleInfo.ENGLISH.equals(LocaleInfoImpl.getLang()))
        {
            // Эти скрипты важно заинжектить именно после основных
            String[] langScripts = new String[] {"kendo.messages.ru-RU.min.js", "kendo.culture.ru-RU.min.js"};
            injectResourcesAsync(langScripts, readyState);
        }

        kendoResources.nauCss().ensureInjected();

        super.initAdditionalResources(readyState);
    }
}
