package ru.naumen.core.client.common.impl;

import java.util.Map;

import jakarta.annotation.Nullable;

import com.google.common.collect.Maps;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.KeyCodes;

import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.buttons.Button;

/**
 * Реализация {@link Dialogs}
 *
 * <AUTHOR>
 */
public class DialogsImpl implements Dialogs
{
    private static DialogResources RESOURCES;
    private static DialogMessages MESSAGES;
    private static Map<Buttons, ButtonFactory> BTNS_FACTORY;

    /**
     * NULL Callback для диалогов без указанного callback
     */
    private static final DialogCallback NULL_CALLBACK = new DialogCallback()
    {
    };

    /**
     * Конструктор.
     */
    // TODO dzevako (на будущее) стили на кнопках, типа MESSAGES.ok(), скорее всего не нужны.
    public DialogsImpl()
    {
        if (null == RESOURCES)
        {
            RESOURCES = GWT.create(DialogResources.class);
            RESOURCES.styles().ensureInjected();
        }
        if (null == MESSAGES)
        {
            MESSAGES = GWT.create(DialogMessages.class);
        }
        if (null == BTNS_FACTORY)
        {
            String cancelBtnStyle = WidgetResources.INSTANCE.buttons().cancelButton();
            BTNS_FACTORY = Maps.newHashMap();
            BTNS_FACTORY.put(Buttons.CONFIRM, new ButtonFactory(MESSAGES.confirm(), ""));
            BTNS_FACTORY.put(Buttons.CONFIRMATION, new ButtonFactory(MESSAGES.confirmation(), ""));
            BTNS_FACTORY.put(Buttons.OK, new ButtonFactory(MESSAGES.ok(), ""));
            BTNS_FACTORY.put(Buttons.CANCEL, new ButtonFactory(MESSAGES.cancel(), cancelBtnStyle));
            BTNS_FACTORY.put(Buttons.CANCELATION, new ButtonFactory(MESSAGES.cancelation(), cancelBtnStyle));
            BTNS_FACTORY.put(Buttons.YES, new ButtonFactory(MESSAGES.yes(), ""));
            BTNS_FACTORY.put(Buttons.NO, new ButtonFactory(MESSAGES.no(), cancelBtnStyle));
            BTNS_FACTORY.put(Buttons.SAVE, new ButtonFactory(MESSAGES.save(), ""));
            BTNS_FACTORY.put(Buttons.CONTINUE_WITHOUT_SAVING, new ButtonFactory(MESSAGES.continueWithoutSaving(),
                    cancelBtnStyle));
            BTNS_FACTORY.put(Buttons.RETURN_CANCELLATION,
                    new ButtonFactory(MESSAGES.returnToMassEditLinks(), cancelBtnStyle));
            BTNS_FACTORY.put(Buttons.RETURN_TO_MASS_EDIT_LINKS,
                    new ButtonFactory(MESSAGES.returnToMassEditLinks(), ""));
            BTNS_FACTORY.put(Buttons.CONTINUE_SAVING,
                    new ButtonFactory(MESSAGES.continueSaving(), cancelBtnStyle));
            BTNS_FACTORY.put(Buttons.CONTINUE,
                    new ButtonFactory(MESSAGES.continueBtn(), ""));
            BTNS_FACTORY.put(Buttons.CLOSE,
                    new ButtonFactory(MESSAGES.closeBtn(), cancelBtnStyle));
            BTNS_FACTORY.put(Buttons.STAY_ON_PAGE,
                    new ButtonFactory(MESSAGES.stayOnPage(), cancelBtnStyle));
            BTNS_FACTORY.put(Buttons.REFRESH,
                    new ButtonFactory(MESSAGES.refreshBtn(), ""));
            BTNS_FACTORY.put(Buttons.RESTORE, new ButtonFactory(MESSAGES.restore(), ""));
            BTNS_FACTORY.put(Buttons.UNDERSTAND, new ButtonFactory(MESSAGES.understand(), ""));
        }
    }

    @Override
    public Dialog error(String description, Buttons... btns)
    {
        return error(MESSAGES.error(), description, btns);
    }

    @Override
    public Dialog error(String description, DialogCallback callback)
    {
        return create(MESSAGES.error(), description, null, null, callback,
                RESOURCES.styles().error() + ' ' + WidgetResources.INSTANCE.all().actionsForceEnabled(),
                new Buttons[] {}, "errorDialog", null, Buttons.OK);
    }

    @Override
    public Dialog error(String message, String description, Buttons... btns)
    {
        return error(message, description, NULL_CALLBACK, btns);
    }

    @Override
    public Dialog errorLimitHeight(String message, String description, Buttons... btns)
    {
        return create(message, description, null, null, NULL_CALLBACK,
            RESOURCES.styles().error() + ' ' + WidgetResources.INSTANCE.all().actionsForceEnabled()
                + ' ' + RESOURCES.styles().formHeightLimited(),
            btns, "errorDialog", null, Buttons.OK);
    }

    @Override
    public Dialog error(String message, String description, DialogCallback callback, Buttons... btns)
    {
        return create(message, description, null, null, callback,
                RESOURCES.styles().error() + ' ' + WidgetResources.INSTANCE.all().actionsForceEnabled(),
                btns, "errorDialog", null, Buttons.OK);
    }

    @Override
    public Dialog errorWithDetails(String description, @Nullable String details, Buttons... btns)
    {
        return create(MESSAGES.error(), description, null, details, NULL_CALLBACK,
                RESOURCES.styles().error() + ' ' + WidgetResources.INSTANCE.all().actionsForceEnabled(),
                btns, "errorDialog", null, Buttons.OK);
    }

    @Override
    public Dialog errorWithDetails(String description, @Nullable String details, DialogCallback callback,
            Buttons... btns)
    {
        return create(MESSAGES.error(), description, null, details, callback,
                RESOURCES.styles().error() + ' ' + WidgetResources.INSTANCE.all().actionsForceEnabled(),
                btns, "errorDialog", null, Buttons.OK);
    }

    @Override
    public Dialog info(String description)
    {
        return info(MESSAGES.info(), description);
    }

    @Override
    public Dialog info(String description, DialogCallback callback)
    {
        return info(MESSAGES.info(), description, callback, new Buttons[0]);
    }

    @Override
    public Dialog info(String message, String description)
    {
        return info(message, description, NULL_CALLBACK, new Buttons[0]);
    }

    @Override
    public Dialog info(String message, String description, DialogCallback callback, Buttons... btns)
    {
        return create(message, description, null, null, callback,
                RESOURCES.styles().info() + ' ' + WidgetResources.INSTANCE.all().actionsForceEnabled(),
                btns, "infoDialog", null, Buttons.OK);
    }

    @Override
    public Dialog question(String message, String description, DialogCallback callback, Buttons... btns)
    {
        return question(message, description, null, callback, btns);
    }

    @Override
    public Dialog question(String message, String description, DialogCallback callback, Buttons defaultButton)
    {
        return create(message, description, null, null, callback, RESOURCES.styles().question(), new Buttons[] {},
                "questionDialog", defaultButton, Buttons.YES, Buttons.NO);
    }

    @Override
    public Dialog question(String message, String description, @Nullable String additionalInfo,
            DialogCallback callback, Buttons... btns)
    {
        return create(message, description, additionalInfo, null, callback, RESOURCES.styles().question(), btns,
                "questionDialog", null, Buttons.YES, Buttons.NO);
    }

    @Override
    public Dialog warning(String description, Buttons... btns)
    {
        return warning(MESSAGES.warning(), description, btns);
    }

    @Override
    public Dialog warning(String message, String description, Buttons... btns)
    {
        return warning(message, description, NULL_CALLBACK, btns);
    }

    @Override
    public Dialog warning(String message, String description, DialogCallback callback, Buttons... btns)
    {
        return warning(message, description, "", callback, btns);
    }

    @Override
    public Dialog warning(String message, String description, String additionalInfo, DialogCallback callback,
            Buttons... btns)
    {
        return create(message, description, additionalInfo, null, callback,
                RESOURCES.styles().warning() + ' ' + WidgetResources.INSTANCE.all().actionsForceEnabled(),
                btns, "questionDialog", null, Buttons.OK);

    }

    @Override
    public Dialog warningOnMassEditForm(String message, String description, DialogCallback callback, Buttons... btns)
    {
        DialogWidget dialog = (DialogWidget)create(message, description, "", null, callback,
                RESOURCES.styles().warningMass(), btns, "questionMassSelectionDialog", null, Buttons.OK);
        return dialog;
    }

    /**
     * Добавляет на форму диалога кнопку "ОК"
     *
     * @param btn добавляемая кнопка
     * @param w диалог на который добавляется кнопка
     * @param callback  callback диалога
     */
    protected Button addBtn(final Buttons btn, final DialogWidget w, final DialogCallback callback)
    {
        Button b = BTNS_FACTORY.get(btn).create();
        b.addClickHandler(event -> callback.onSuccess(new DialogResult(btn, w)));
        b.addKeyDownHandler(event ->
        {
            if (event.getNativeKeyCode() == KeyCodes.KEY_ENTER)
            {
                callback.onSuccess(new DialogResult(btn, w));
            }
            if (event.getNativeKeyCode() == KeyCodes.KEY_ESCAPE)
            {
                w.hide();
            }
        });

        w.add(b);
        b.ensureDebugId(btn.name().toLowerCase());
        return b;
    }

    private Button addBtns(Buttons btns[], DialogWidget w, DialogCallback callback, Buttons defaultButton)
    {
        Button lastButton = null;
        for (Buttons btn : btns)
        {
            lastButton = btn == defaultButton ? addDefaultBtn(btn, w, callback) : addBtn(btn, w, callback);
        }
        return lastButton;
    }

    private Button addDefaultBtn(Buttons btn, DialogWidget w, DialogCallback callback)
    {
        Button defaultBtn = addBtn(btn, w, callback);
        TabOrderHelper.setFocusDeffered(defaultBtn, true);
        return defaultBtn;
    }

    private Dialog create(String message, String description, @Nullable String additionalInfo,
            @Nullable String details, DialogCallback callback, String style, Buttons btns[], String debugId,
            @Nullable Buttons defaultButton, Buttons... defaultBtns)
    {
        DialogWidget w = new DialogWidget(message, description, additionalInfo, details, style);
        Button lastButton = null;
        if (0 == btns.length)
        {
            lastButton = addBtns(defaultBtns, w, callback, defaultButton);
        }
        else
        {
            lastButton = addBtns(btns, w, callback, defaultButton);
        }
        w.updateTabOrder();
        TabOrderHelper.addTabOrderCycle(lastButton, w);
        if (defaultButton == null)
        {
            TabOrderHelper.setFocusDeffered(w.getFirstFocusElement(), true);
        }
        w.center();
        w.ensureDebugId(debugId);
        return w;
    }
}
