package ru.naumen.core.client.common.impl;

import ru.naumen.core.client.common.MenuItemRegistration;

import com.google.gwt.user.client.ui.MenuBar;
import com.google.gwt.user.client.ui.MenuItem;

/**
 * Реализация {@link MenuItemRegistration}
 * 
 * <AUTHOR>
 * 
 */
public class DefaultMenuItemRegistration implements MenuItemRegistration
{
    MenuBar menu;
    MenuItem item;

    public DefaultMenuItemRegistration(MenuBar menu, MenuItem item)
    {
        this.menu = menu;
        this.item = item;
    }

    @Override
    public void removeMenuItem()
    {
        menu.removeItem(item);
    }
}
