package ru.naumen.core.client.events;

import ru.naumen.metainfo.shared.ui.Content;

import com.google.gwt.event.shared.GwtEvent;

/**
 * <AUTHOR>
 * @since 21.10.2011
 */
public class SetRemovedEvent extends GwtEvent<SetRemovedHandler>
{
    private static final Type<SetRemovedHandler> TYPE = new Type<SetRemovedHandler>();

    public static Type<SetRemovedHandler> getType()
    {
        return TYPE;
    }

    private final boolean removed;
    private final Content content;
    private boolean canceled = false;

    public SetRemovedEvent(boolean removed, Content content)
    {
        this.content = content;
        this.removed = removed;
    }

    @Override
    public Type<SetRemovedHandler> getAssociatedType()
    {
        return TYPE;
    }

    /**
     * @return контент который необходимо обновить
     */
    public Content getContent()
    {
        return content;
    }

    public boolean isCanceled()
    {
        return canceled;
    }

    public boolean isRemoved()
    {
        return removed;
    }

    public boolean isSuitable(Content content)
    {
        return getContent().equals(content);
    }

    public void setCanceled(boolean canceled)
    {
        this.canceled = canceled;
    }

    @Override
    protected void dispatch(SetRemovedHandler handler)
    {
        handler.setRemoved(this);
    }
}
