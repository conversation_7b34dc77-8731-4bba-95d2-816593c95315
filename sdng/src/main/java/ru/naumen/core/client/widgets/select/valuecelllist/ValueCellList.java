package ru.naumen.core.client.widgets.select.valuecelllist;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.function.Predicate;

import com.google.gwt.cell.client.Cell;
import com.google.gwt.core.shared.GWT;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.cellview.client.CellList;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.DefaultSelectionEventManager.EventTranslator;
import com.google.gwt.view.client.DefaultSelectionEventManager.SelectAction;
import com.google.gwt.view.client.Range;
import com.google.gwt.view.client.RowCountChangeEvent;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SetSelectionModel;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.widgets.HasValueOrThrow;

/**
 * Widget множественного выбора элементов. Отображается в виде списка с checkbox-ами.
 * <p>
 * Список возможных значение устанавливается методом {@link #setAcceptableValues(Collection)}
 * <p>
 * Коллекцию выделенных значений получают метдом {@link #getValue()}, а устанавливают выделенные
 * значения методом {@link #setValue(Collection)} 
 * 
 * @see MultiSelectListWidget
 * 
 * <AUTHOR>
 *
 * @param <T>
 */
public class ValueCellList<T> extends Composite implements HasValueOrThrow<Collection<T>>, HasEnabled, Focusable
{
    public interface Resources extends CellList.Resources
    {
        @Override
        @Source("ValueCellList.css")
        Style cellListStyle();
    }

    public interface Style extends CellList.Style
    {
    }

    private class SelectionEventTranslator implements EventTranslator<T>
    {

        private final Predicate<T> selectablePredicate;

        public SelectionEventTranslator(Predicate<T> selectablePredicate)
        {
            this.selectablePredicate = selectablePredicate;
        }

        @Override
        public boolean clearCurrentSelection(CellPreviewEvent<T> event)
        {
            return false;
        }

        @Override
        public SelectAction translateSelectionEvent(CellPreviewEvent<T> event)
        {
            if (enabled && selectablePredicate.test(event.getValue()))
            {
                return SelectAction.TOGGLE;
            }
            return SelectAction.IGNORE;
        }
    }

    private static final Resources RESOURCES = GWT.create(Resources.class);

    private final CellList<T> cellList;
    private Collection<T> value;
    private boolean enabled = true;

    /**
     * Общий конструктор. Позволяет определить нестандартную логику отрисовки ячейки
     * 
     * @see #MultiValueCellList(Renderer, ProvidesKey)
     * @see #MultiValueCellList(Renderer, ProvidesKey, Predicate)
     * 
     * @param cell ячейка списка
     * @param selectionModel
     */
    public ValueCellList(Cell<T> cell, SetSelectionModel<T> selectionModel, Predicate<T> selectablePredicate)
    {
        this.cellList = new CellList<T>(cell, RESOURCES);
        this.cellList.setSelectionModel(selectionModel,
                DefaultSelectionEventManager.createCustomManager(new SelectionEventTranslator(selectablePredicate)));
        initWidget(this.cellList);
        this.cellList.getSelectionModel().addSelectionChangeHandler(new SelectionChangeEvent.Handler()
        {
            @Override
            @SuppressWarnings("unchecked")
            public void onSelectionChange(SelectionChangeEvent event)
            {
                setValue(((SetSelectionModel<T>)cellList.getSelectionModel()).getSelectedSet(), true, false);
            }
        });

        //Отключаем постраничное отображение
        this.cellList.addRowCountChangeHandler(new RowCountChangeEvent.Handler()
        {
            @Override
            public void onRowCountChange(RowCountChangeEvent event)
            {
                cellList.setVisibleRange(new Range(0, event.getNewRowCount()));
            }
        });
    }

    @Override
    public HandlerRegistration addValueChangeHandler(ValueChangeHandler<Collection<T>> handler)
    {
        return addHandler(handler, ValueChangeEvent.getType());
    }

    @Override
    public int getTabIndex()
    {
        return cellList.getTabIndex();
    }

    @Override
    public Collection<T> getValue()
    {
        return value;
    }

    @Override
    public Collection<T> getValueOrThrow() throws ParseException
    {
        return getValue();
    }

    @Override
    public boolean isEnabled()
    {
        return enabled;
    }

    public void setAcceptableValues(Collection<T> values)
    {
        cellList.setRowCount(values.size(), true);
        cellList.setRowData(0, new ArrayList<T>(values));
    }

    @Override
    public void setAccessKey(char key)
    {
        cellList.setAccessKey(key);
    }

    @Override
    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
        this.cellList.redraw();
    }

    @Override
    public void setFocus(boolean focused)
    {
        cellList.setFocus(focused);
    }

    @Override
    public void setTabIndex(int index)
    {
        cellList.setTabIndex(index);
    }

    @Override
    public void setValue(Collection<T> value)
    {
        setValue(value, true);
    }

    @Override
    public void setValue(Collection<T> value, boolean fireEvents)
    {
        setValue(value, fireEvents, true);
    }

    protected void setValue(Collection<T> value, boolean fireEvents, boolean needUpdateSelection)
    {
        Collection<T> current = getValue();
        if (current == value)
        {
            return;
        }
        if (null != current && CollectionUtils.subtract(value, current).isEmpty()
                && CollectionUtils.subtract(current, value).isEmpty())
        {
            return;
        }
        this.value = value;
        if (needUpdateSelection)
        {
            SetSelectionModel<? super T> selectionModel = (SetSelectionModel<? super T>)cellList.getSelectionModel();
            selectionModel.clear();
            for (T v : value)
            {
                selectionModel.setSelected(v, true);
            }
        }
        if (fireEvents)
        {
            ValueChangeEvent.fire(this, value);
        }
    }
}
