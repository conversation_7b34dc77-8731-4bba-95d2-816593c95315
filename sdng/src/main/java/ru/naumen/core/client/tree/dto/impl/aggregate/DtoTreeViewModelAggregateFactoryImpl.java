package ru.naumen.core.client.tree.dto.impl.aggregate;

import jakarta.inject.Singleton;

import ru.naumen.core.client.tree.dto.DtoTreeGinModule.Aggregate;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithoutFolders;
import ru.naumen.core.client.tree.dto.view.DtoTreeFilteredViewModelFactoryImpl;
import ru.naumen.core.client.tree.selection.FilteredSelectionModel;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Генератор модели дерева объектов по набору AttributeDescription (для агрегирующих атрибутов)
 * 
 * <AUTHOR>
 * @since 29.03.2012
 */
@Singleton
public class DtoTreeViewModelAggregateFactoryImpl<M extends FilteredSelectionModel<DtObject>> extends
        DtoTreeFilteredViewModelFactoryImpl<Aggregate, WithoutFolders, M, DtoTreeFactoryAggregateContext>
{

}
