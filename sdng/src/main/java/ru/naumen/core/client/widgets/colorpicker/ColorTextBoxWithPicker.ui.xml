<?xml version="1.0" encoding="UTF-8"?>
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui"
	xmlns:n="urn:import:ru.naumen.core.client.widgets.colorpicker">
<ui:with field='res' type='ru.naumen.core.client.widgets.WidgetResources' />
<ui:with field='cpRes' type='ru.naumen.core.client.widgets.colorpicker.ColorPickerResources' />
<g:FlowPanel>
	<g:FocusPanel ui:field="colorPanel" styleName="{cpRes.css.colorPanel}"/>
	<g:FlowPanel styleName="{cpRes.css.textPanel}">
		<n:ColorPickerTextBox ui:field="input"/>
	</g:FlowPanel> 
</g:FlowPanel>
</ui:UiBinder>
