package ru.naumen.core.client.common.impl;

import static ru.naumen.core.shared.utils.DateUtils.MILLISECONDS_IN_A_MINUTE;

import java.util.Date;
import java.util.Map;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.common.collect.Maps;
import com.google.gwt.core.client.JsArray;
import com.google.gwt.i18n.client.TimeZone;
import com.google.gwt.i18n.client.TimeZoneInfo;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.general.StringResult;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.TimeZoneRegistry;
import ru.naumen.core.shared.dispatch.GetGwtTimeZoneAction;

/**
 * Реализация Реестра Временных Зон на клиенте.
 * 
 * <AUTHOR>
 */
@Singleton
public class TimeZoneRegistryImpl implements TimeZoneRegistry
{
    private static Date convertDate(@Nullable Date date, TimeZone from, TimeZone to)
    {
        return date == null ? null : new Date(date.getTime() + (from.getOffset(date) - to.getOffset(date))
                * MILLISECONDS_IN_A_MINUTE);
    }

    private static native String getClientTimeZoneID()
    /*-{
        return $wnd.gwt_client_tzid;
    }-*/;

    private static native JsArray<TimeZoneInfo> getPreLoadedGwtTimeZones()
    /*-{
        return $wnd.gwt_timezone_infos;
    }-*/;

    @Inject
    DispatchAsync dispatch;

    private Map<String, TimeZone> zones = null;

    protected TimeZoneRegistryImpl()
    {
    }

    @Override
    public TimeZone getClientTimeZone()
    {
        return getTimeZone(getClientTimeZoneID());
    }

    @Override
    public TimeZone getTimeZone(@Nullable String id)
    {
        enshureInitialised();
        return zones.get(id);
    }

    @Override
    public void getTimeZone(String id, AsyncCallback<TimeZone> callback)
    {
        enshureInitialised();
        TimeZone tz = zones.get(id);
        if (tz != null)
        {
            callback.onSuccess(tz);
            return;
        }
        dispatch.execute(new GetGwtTimeZoneAction(id), new CallbackDecorator<StringResult, TimeZone>(callback)
        {
            @Override
            protected TimeZone apply(StringResult from)
            {
                TimeZone tz = TimeZone.createTimeZone(from.get());
                zones.put(tz.getID(), tz);
                return tz;
            }
        });
    }

    @Override
    public Date toBrowserTimeZone(Date date)
    {
        return getClientTimeZone() == null || date == null ? date : convertDate(date, getClientTimeZone(),
                createBrowserTimeZone(date));
    }

    @SuppressWarnings("deprecation")
    private TimeZone createBrowserTimeZone(Date date)
    {
        return TimeZone.createTimeZone(date.getTimezoneOffset());
    }

    private void enshureInitialised()
    {
        if (null != zones)
        {
            return;
        }

        zones = Maps.newHashMap();
        JsArray<TimeZoneInfo> preloaded = getPreLoadedGwtTimeZones();
        for (int i = 0; i < preloaded.length(); ++i)
        {
            TimeZoneInfo tzi = preloaded.get(i);
            if (null != tzi)
            {
                TimeZone tz = TimeZone.createTimeZone(tzi);
                zones.put(tz.getID(), tz);
            }
        }
    }

    @Override
    public Date toClientTimeZone(Date date)
    {
        return null == date || null == getClientTimeZone() ? date
                : convertDate(date, createBrowserTimeZone(date), getClientTimeZone());
    }
}
