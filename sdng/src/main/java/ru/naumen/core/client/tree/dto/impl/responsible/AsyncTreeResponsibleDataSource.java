package ru.naumen.core.client.tree.dto.impl.responsible;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.Multimaps;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.view.client.Range;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.responsible.HasCanTakes;
import ru.naumen.core.client.common.DataSourceReadyEvent;
import ru.naumen.core.client.common.HasUnsavedObjects;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.HasUnbind;
import ru.naumen.core.client.tree.datasource.AbstractHierarchicalAsyncTreeDataSource;
import ru.naumen.core.client.tree.dto.DtoKeyProvider;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.Folders;
import ru.naumen.core.client.tree.dto.datasource.HasReset;
import ru.naumen.core.client.tree.dto.impl.responsible.action.GetResponsibleTreeActionFactory;
import ru.naumen.core.client.tree.dto.searcher.TreeSearcher;
import ru.naumen.core.client.tree.selection.FilteredSelectionModel;
import ru.naumen.core.client.utils.UnsavedObjectsHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.HasResponsible;
import ru.naumen.core.shared.dispatch.CanTakeResponsiblePermission;
import ru.naumen.core.shared.dispatch.GetPossibleResponsibleSingleValueAction;
import ru.naumen.core.shared.dispatch.GetPossibleResponsibleSingleValueResponse;
import ru.naumen.core.shared.dispatch.GetPossibleResponsibleTreeAction;
import ru.naumen.core.shared.dispatch.GetPossibleResponsibleTreeResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectedValueDtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Источник данных для дерева возможных ответственных.
 * Все права и настройки проверяются на сервере.
 * Папки отображаются только те, в которых есть доступные для выбора команды.
 * <p>
 * Команды без папок показываются в корне, с папками не показываются.
 * 
 * <AUTHOR>
 * @since 03.04.2013
 */
public class AsyncTreeResponsibleDataSource<M extends FilteredSelectionModel<DtObject>, F extends Folders> extends
        AbstractHierarchicalAsyncTreeDataSource<DtObject, DtoTreeFactoryResponsibleContext> implements HasUnbind,
        HasReset, HasCanTakes, HasUnsavedObjects
{
    private static final int PAGE_SIZE = Constants.DEFAULT_PAGE_SIZE;

    private final DtoResponsibleTreeSearcher searcher;
    private final DispatchAsync dispatch;
    private final GetResponsibleTreeActionFactory<M, F> actionFactory;
    private final UnsavedObjectsHelper unsavedObjectsHelper;

    //Флаг указывающий что когда данные будут готовы, значение дерева нужно сбросить
    private boolean dropValueOnReady = false;
    private Map<String, CanTakeResponsiblePermission> canTakes;
    private final Map<String, DtObject> editedObjects = new LinkedHashMap<>();

    Multimap<DtObject, DtObject> hierarchyMap = Multimaps.newListMultimap(Maps.newHashMap(), Lists::newArrayList);

    @Inject
    public AsyncTreeResponsibleDataSource(
            @Assisted DtoTreeFactoryResponsibleContext context,
            DtoKeyProvider keyProvider,
            DtoResponsibleTreeSearcher searcher,
            DispatchAsync dispatch,
            GetResponsibleTreeActionFactory<M,F> actionFactory,
            UnsavedObjectsHelper unsavedObjectsHelper)
    {
        super(context, keyProvider);
        this.searcher = searcher;
        searcher.setDataSource(this);
        this.dispatch = dispatch;
        this.actionFactory = actionFactory;
        this.unsavedObjectsHelper = unsavedObjectsHelper;
        if (null != context.getPrsContext())
        {
            setUnsavedObjects(context.getPrsContext().getUnsavedObjects());
        }
    }

    public void clearLoadedHierarchy()
    {
        hierarchyMap.clear();
    }

    public GetPossibleResponsibleTreeAction getAction()
    {
        GetPossibleResponsibleTreeAction action = actionFactory.create(context);
        action.setWithCanTakes(canTakes == null);
        action.getUnsavedObjects().putAll(unsavedObjectsHelper.prepareForHierarchyRequest(editedObjects));
        return action;
    }

    @Override
    public Map<String, CanTakeResponsiblePermission> getCanTakes()
    {
        return canTakes;
    }

    @Override
    public TreeSearcher<DtObject> getSearcher()
    {
        return searcher;
    }

    @Override
    public List<DtObject> getUnsavedObjects()
    {
        return new ArrayList<>(editedObjects.values());
    }

    @Override
    public void init(AsyncCallback<Void> callback)
    {
        if (context.isNeedSelectSingleValue())
        {
            dispatch.execute(new GetPossibleResponsibleSingleValueAction(getAction()),
                    new CallbackDecorator<GetPossibleResponsibleSingleValueResponse, Void>(callback)
                    {
                        @Override
                        protected Void apply(GetPossibleResponsibleSingleValueResponse response)
                        {
                            context.setSingleValue(response.getSingleValue());
                            return null;
                        }
                    });
        }
        else
        {
            callback.onSuccess(null);
        }
    }

    @Override
    public void reset(AsyncCallback<DtObject> callback)
    {
        hierarchyMap.clear();
        callback.onSuccess(null);
    }

    @Override
    public void setUnsavedObjects(List<DtObject> unsavedObjects)
    {
        editedObjects.clear();
        
        for (DtObject obj : unsavedObjects)
        {
            if (!UuidHelper.isTempUuid(obj.getUUID()))
            {
                editedObjects.put(obj.getUUID(), obj);
            }
        }

        Collection<DtObject> objects = hierarchyMap.values();
        UnsavedObjectsHelper.updateEditedObjects(objects, editedObjects);
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        callback.onSuccess(null);
    }

    @Override
    protected void asyncGetChildren(DtObject parent, AsyncCallback<List<DtObject>> callback)
    {
        // ничего не делаем
    }

    @Override
    protected void asyncGetChildren(final DtObject parent, Range range, final AsyncCallback<List<DtObject>> callback)
    {
        if (searcher.isSearch() && searcher.getSearchedChilds(parent) != null
                && !searcher.getSearchedChilds(parent).isEmpty())
        {
            List<DtObject> searchedChilds = searcher.getSearchedChilds(parent);
            callback.onSuccess(searchedChilds != null ? searchedChilds : Lists.newArrayList());
            return;
        }

        final List<DtObject> loadedObject = (List<DtObject>)hierarchyMap.get(parent);

        if (!hasMore(loadedObject))
        {
            callback.onSuccess((List<DtObject>)hierarchyMap.get(parent));
            return;
        }

        if (!loadedObject.isEmpty())
        {
            loadedObject.remove(loadedObject.size() - 1);
        }

        GetPossibleResponsibleTreeAction action = getAction();
        action.setParent(parent);
        action.setFirst(getPageStart(range.getLength()));
        action.setLast(range.getLength());

        if (checkSelectedValue(range, parent))
        {
            action.setCheckSelectedValue(true);
            action.setSelectedValue(getSelectedValue());
        }
        else
        {
            action.setCheckSelectedValue(false);
        }

        Context parentContext = context.getPrsContext().getParentContext();
        if (parentContext == null)
        {
            throw new NullPointerException();
        }
        dispatch.execute(action,
                new BasicCallback<GetPossibleResponsibleTreeResponse>(parentContext.getReadyState())
        {
            @Override
            public void handleSuccess(GetPossibleResponsibleTreeResponse response)
            {
                List<DtObject> objects = response.getHierarchy().get(parent);
                UnsavedObjectsHelper.updateEditedObjects(objects, editedObjects);

                loadedObject.addAll(objects);

                //Указываем виджету что есть еще элементы для отображения,
                //добавляя пустой элемент
                if (response.hasMoreInAWay())
                {
                    loadedObject.add(null);
                }

                //В случае, если выбранный элемент больше не доступен 
                if (response.isSelectionChanged())
                {
                    dropValueOnReady = true;
                }

                if (response.getCanTakes() != null)
                {
                    canTakes = response.getCanTakes();
                }

                callback.onSuccess(loadedObject);
            }
        });
    }

    @Override
    protected void fireReady(DtObject parent)
    {
        DataSourceReadyEvent<DtObject> event = new DataSourceReadyEvent<>(parent);
        if (dropValueOnReady)
        {
            dropValueOnReady = false;
            event.setSelectedValues(new SelectedValueDtObject(context.getSingleValue() == null, true));
        }
        fireEvent(event);
    }

    /**
     * Определяет нужно ли проверять доступность текущего выбранного узла в дереве
     */
    private boolean checkSelectedValue(Range range, DtObject parent)
    {
        Attribute attribute = context.getPrsContext().getAttribute();
        return range.getStart() == 0 && isGetRootChildren(parent) &&  attribute != null
                && attribute.isFilteredByScript();
    }

    private static int getPageStart(int length)
    {
        int start = length - PAGE_SIZE;
        return Math.max(start, 0);
    }

    private DtObject getSelectedValue()
    {
        DtObject object = context.getPrsContext().getObject();
        if (object == null)
        {
            throw new NullPointerException();
        }
        return object.getProperty(HasResponsible.RESPONSIBLE);
    }

    private static boolean hasMore(List<DtObject> childs)
    {
        return childs.isEmpty() || childs.get(childs.size() - 1) == null;
    }
}