package ru.naumen.core.client.jsinterop.froala.editor.methods;

import jsinterop.annotations.JsMethod;
import jsinterop.annotations.JsPackage;
import jsinterop.annotations.JsType;

/**
 * Группа методов Froala для работы с размерами редактора
 *
 * @see <a href="https://froala.com/wysiwyg-editor/docs/methods/#size">Описание в документации Froala</a>
 * <AUTHOR>
 * @since 07.02.2023
 */
@JsType(isNative = true, namespace = JsPackage.GLOBAL, name = "Object")
public class FroalaSize
{
    /**
     * Синхронизировать высоту iframe с высотой контента в редакторе
     *
     * @see <a href="https://froala.com/wysiwyg-editor/docs/methods/#size.syncIframe">Описание в документации <PERSON></a>
     * <AUTHOR>
     * @since 07.02.2023
     */
    @JsMethod
    public native void syncIframe();
}