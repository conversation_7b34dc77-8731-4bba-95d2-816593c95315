package ru.naumen.core.client.common.impl;

import java.util.Set;

import jakarta.annotation.Nullable;

import com.google.common.collect.Sets;
import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.JavaScriptObject;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.Style;
import com.google.gwt.dom.client.Style.Display;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.json.client.JSONObject;
import com.google.gwt.json.client.JSONParser;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Event.NativePreviewEvent;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.DecoratedPopupPanel;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.InlineHTML;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.RootPanel;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.forms.HasTabOrder;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.jsinterop.xss.JsXss;
import ru.naumen.core.client.widgets.DialogEventBus;
import ru.naumen.core.client.widgets.FormCss;
import ru.naumen.core.client.widgets.NDialogBox;
import ru.naumen.core.client.widgets.ShowDialogBoxEvent;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.shared.Constants.SharedSettings;
import ru.naumen.core.shared.HasBadge;

/**
 * <AUTHOR>
 * @since 08.09.2011
 *
 */
//TODO dzevako переделать на использование DialogBoxWidget в NSDPRD-12486 Рефакторинг форм
@Deprecated
public class DialogWidget extends DecoratedPopupPanel implements Dialog, HasTabOrder, HasBadge
{
    interface DialogWidgetUiBinder extends UiBinder<FlowPanel, DialogWidget>
    {
    }

    private static DialogWidgetUiBinder uiBinder = GWT.create(DialogWidgetUiBinder.class);

    private static DialogResources RESOURCES = GWT.create(DialogResources.class);
    private static CommonMessages MESSAGES = GWT.create(CommonMessages.class);

    private static JavaScriptObject whiteList;

    @UiField
    FlowPanel modalForm;
    @UiField
    FlowPanel headPanel;
    @UiField
    InlineHTML badge;
    @UiField
    FlowPanel buttons;
    @UiField
    Label descriptionElement;
    @UiField
    Label additionalInfoElement;
    @UiField
    Label head;
    @UiField
    FlowPanel body;
    @UiField
    FlowPanel mainPanel;
    @UiField
    FlowPanel detailsBox;
    @UiField
    Anchor detailsBtn;
    @UiField
    Label details;

    private int baseTabIndex = 1;
    private int nextBaseTabIndex = baseTabIndex;

    private final Set<HasEnabled> focusWidgets = Sets.newHashSet();
    private Element previousScrollableRootPanelElement;

    //TODO: Костыль для получения значения проперти, будет выпилен в NSDPRD-12486 при рефакторинге форм
    private static native JavaScriptObject getSettingsNative()/*-{
        return $wnd.sharedSettings;
    }-*/;

    private static JSONObject getSettings()
    {
        return new JSONObject(getSettingsNative());
    }

    private static boolean isEnableSanitize()
    {
        return Boolean.parseBoolean(
                getSettings().get(SharedSettings.ENABLED_SANITIZE_HTML).toString().replace("\"", ""));
    }

    private static JavaScriptObject getWhiteList()
    {
        if (whiteList == null)
        {
            whiteList = JSONParser.parseStrict(
                            getSettings().get(SharedSettings.SANITIZE_WHITE_LIST).isString().stringValue())
                    .isObject().getJavaScriptObject();
        }
        return whiteList;
    }

    /**
     * Диалог с кнопками
     */
    public DialogWidget(String message, String description, @Nullable String additionalInfo, @Nullable String details,
            String style)
    {
        super(false, true);
        RESOURCES.styles().ensureInjected();
        setGlassEnabled(true);
        uiBinder.createAndBindUi(this);
        fillWidgets(message, description, additionalInfo, details, style);
    }

    @Override
    public void add(Widget w)
    {
        buttons.add(w);
        if (w instanceof HasEnabled)
        {
            focusWidgets.add((HasEnabled)w);
        }
        w.addStyleName(WidgetResources.INSTANCE.buttons().buttonBothSide());
    }

    @Override
    public void destroy()
    {
        removeFromParent();
    }

    @Override
    public int getBaseTabIndex()
    {
        return baseTabIndex;
    }

    public FlowPanel getButtons()
    {
        return buttons;
    }

    public Label getDescription()
    {
        return descriptionElement;
    }

    @Override
    public Focusable getFirstFocusElement()
    {
        return buttons.getWidgetCount() > 0 ? (Focusable)buttons.getWidget(0) : null;
    }

    public Label getHead()
    {
        return head;
    }

    @Override
    public int getNextBaseTabIndex()
    {
        return nextBaseTabIndex;
    }

    @Override
    public void hide(boolean autoClosed)
    {
        RootPanel.setScrollableRootPanel(null, this, false);
        super.hide(autoClosed);
        RootPanel.setScrollableRootPanel(previousScrollableRootPanelElement, null, false);
    }

    @Override
    public void resetTabOrder()
    {
        for (Widget w : buttons)
        {
            TabOrderHelper.resetTabIndex(w);
        }
        nextBaseTabIndex = baseTabIndex;
    }

    @Override
    public void setBaseTabIndex(int baseTabIndex)
    {
        this.baseTabIndex = baseTabIndex;

    }

    @Override
    public void show()
    {
        previousScrollableRootPanelElement = RootPanel.getScrollableRootPanelElement();
        RootPanel.setScrollableRootPanel(null, this, false);

        super.show();
        DialogEventBus.INSTANCE.fireEvent(new ShowDialogBoxEvent());
    }

    @Override
    public void startProcessing()
    {
        setEnabled(false);
    }

    @Override
    public void stopProcessing()
    {
        setEnabled(true);
    }

    @Override
    public void updateTabOrder()
    {
        resetTabOrder();
        int nextIndex = baseTabIndex;
        for (Widget w : buttons)
        {
            nextIndex = TabOrderHelper.setTabIndex(w, nextIndex);
        }
        nextBaseTabIndex = nextIndex;
    }

    @Override
    protected void onLoad()
    {
        super.onLoad();

        detailsBtn.addClickHandler((event) ->
        {
            Style detailsStyle = details.getElement().getStyle();
            detailsStyle.setMarginTop(8, Unit.PX);
            if (Display.NONE.getCssName().equals(detailsStyle.getDisplay()))
            {
                detailsStyle.setDisplay(Display.BLOCK);
                detailsBtn.setText(DialogWidget.MESSAGES.dialogHideDetails());
            }
            else
            {
                detailsStyle.setDisplay(Display.NONE);
                detailsBtn.setText(DialogWidget.MESSAGES.dialogShowDetails());
            }
        });
    }

    @Override
    protected void onPreviewNativeEvent(NativePreviewEvent npe)
    {
        NDialogBox.ensureConsumeEvent(npe, getElement());
        super.onPreviewNativeEvent(npe);
    }

    private void fillWidgets(String message, String description, @Nullable String additionalInfo,
            @Nullable String details, String style)
    {
        headPanel.setStyleName("Caption");
        badge.setVisible(false);
        addStyleName(RESOURCES.styles().dialog());
        addStyleName(style);
        FormCss formCss = WidgetResources.INSTANCE.form();
        setGlassStyleName(formCss.bLightboxFormDarkening());
        addStyleName(formCss.bLightboxFormInner());
        body.addStyleName(formCss.fieldset());
        DebugIdBuilder.ensureDebugId(head, "dialogWidgetHead");
        DebugIdBuilder.ensureDebugId(body, "dialogWidgetBody");
        DebugIdBuilder.ensureDebugId(descriptionElement, "dialogWidgetDescriptionElement");
        DebugIdBuilder.ensureDebugId(additionalInfoElement, "dialogWidgetAdditionalInfoElement");
        head.setText(message);

        //TODO: костыль, необходимо следить, чтобы метод был такой же как в HtmlSanitizeUtils (исправление NSDPRD-12486)
        String desc = isEnableSanitize() ? JsXss.filterXSS(description, getWhiteList()) : description;
        descriptionElement.getElement().setInnerHTML(desc);
        if (StringUtilities.isEmptyTrim(additionalInfo))
        {
            additionalInfoElement.getElement().getStyle().setDisplay(Display.NONE);
        }
        else
        {
            //TODO: костыль, необходимо следить, чтобы метод был такой же как в HtmlSanitizeUtils
            // (исправление NSDPRD-12486)
            String addInfo = isEnableSanitize() ? JsXss.filterXSS(additionalInfo, getWhiteList()) : additionalInfo;
            additionalInfoElement.getElement().setInnerHTML(addInfo);
        }

        if (StringUtilities.isEmptyTrim(details))
        {
            this.detailsBox.getElement().getStyle().setDisplay(Display.NONE);
        }
        else
        {
            this.details.setText(details);
            this.details.getElement().getStyle().setDisplay(Display.NONE);
            this.detailsBtn.setText(DialogWidget.MESSAGES.dialogShowDetails());
        }

        super.add(mainPanel);
    }

    private void setEnabled(boolean enabled)
    {
        for (HasEnabled w : focusWidgets)
        {
            w.setEnabled(enabled);
        }
    }

    @Override
    public void setBadgeText(String badgeText)
    {
        badge.setVisible(StringUtilities.isNotEmpty(badgeText));
        badge.setText(badgeText);
    }

    @Override
    public void setBadgeBackGroundColor(String backgroundColor)
    {
        badge.getElement().getStyle().setBackgroundColor(backgroundColor);
    }
}
