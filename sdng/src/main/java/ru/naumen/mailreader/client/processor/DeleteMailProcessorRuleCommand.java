package ru.naumen.mailreader.client.processor;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.mailreader.client.MailreaderMessages;
import ru.naumen.mailreader.client.MailreaderMetainfoService;
import ru.naumen.mailreader.shared.MailProcessorRuleWithScript;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;

/**
 * Команда {@link Command} удаления правла обработки входящей почты
 * <AUTHOR>
 */
public class DeleteMailProcessorRuleCommand extends ObjectCommandImpl<MailProcessorRuleWithScript, Void>
{
    @Inject
    MailreaderMetainfoService mrMetainfoService;
    @Inject
    MailreaderMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    I18nUtil i18nUtil;

    @Inject
    public DeleteMailProcessorRuleCommand(@Assisted CommandParam<MailProcessorRuleWithScript, Void> param,
            Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(MailProcessorRuleWithScript mailProcessorRule)
    {
        return cmessages.confirmDeleteQuestion(messages.mailProcessingRule(),
                i18nUtil.getLocalizedTitle(mailProcessorRule));
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    protected void onDialogSuccess(CommandParam<MailProcessorRuleWithScript, Void> param)
    {
        mrMetainfoService.deleteMailProcessorRule(param.getValue().getObject(), param.getCallbackSafe());
    }
}
