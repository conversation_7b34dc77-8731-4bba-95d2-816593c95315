package ru.naumen.mailreader.client;

/**
 * Коды команд модуля Mailreader
 * <AUTHOR>
 */
public interface MailreaderCommandCode
{
    String ADD_INBOUND_MAILSERVER = "addInboundMailServerConfig";
    String EDIT_INBOUND_MAILSERVER = "editInboundMailServerConfig";
    String DELETE_INBOUND_MAILSERVER = "deleteInboundMailServerConfig";
    String ENABLE_INBOUND_MAILSERVER = "enableInboundMailServerConfig";
    String DISABLE_INBOUND_MAILSERVER = "disableInboundMailServerConfig";
    String VALIDATE_INBOUND_MAILSERVER = "validateInboundMailServerConfig";

    String ADD_MAILPROCESSOR = "addMailProcessorRule";
    String EDIT_MAILPROCESSOR = "editMailProcessorRule";
    String DELETE_MAILPROCESSOR = "deleteMailProcessorRule";
    String ENABLE_MAILPROCESSOR = "enableMailProcessorRule";
    String DISABLE_MAILPROCESSOR = "disableMailProcessorRule";
}
