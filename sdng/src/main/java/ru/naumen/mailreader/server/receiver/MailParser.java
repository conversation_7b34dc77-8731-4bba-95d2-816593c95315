package ru.naumen.mailreader.server.receiver;

import static ru.naumen.mailreader.server.processor.MailProcessHelper.getFileNameFromParamsMap;
import static ru.naumen.mailreader.server.processor.MailProcessHelper.getNamedParametersMap;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.io.IOUtils;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.mail.util.MimeMessageParser;
import org.eclipse.angus.mail.imap.IMAPBodyPart;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;

import jakarta.activation.DataSource;
import jakarta.annotation.Nullable;
import jakarta.mail.MessagingException;
import jakarta.mail.Multipart;
import jakarta.mail.Part;
import jakarta.mail.Session;
import jakarta.mail.internet.ContentType;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimePart;
import jakarta.mail.internet.MimeUtility;
import jakarta.mail.internet.ParseException;
import ru.naumen.commons.shared.utils.MimeTypeRegistry;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.mailreader.server.processor.MailProcessHelper;
import ru.naumen.mailreader.server.queue.IInboundMailAttachment;
import ru.naumen.mailreader.server.queue.InboundMailAttachment;

/**
 * Расширение функциональности парсера писем.
 * Разделение прикрипленных к письму вложений на 2 списка : attach-части и inline-части
 * <AUTHOR>
 * @since 13.11.2013
 */
public class MailParser extends MimeMessageParser
{
    private static final Logger LOG = LoggerFactory.getLogger(MailParser.class);
    private static final Pattern DELIMITER = Pattern.compile(":");
    private static final Pattern CONTENT_ID = Pattern.compile("[\\<\\>]");
    private static final Pattern FILENAME = Pattern.compile("filename=");
    private static final Pattern QUOTES = Pattern.compile("\"");
    private static final Pattern PARAMETER_DELIMITER = Pattern.compile(";");
    private static final Pattern KEY_VALUE_DELIMITER = Pattern.compile("=");
    private final MessageFacade message;
    private static final String NAME_PARAMETER = "name";
    private static final String FILENAME_PARAMETER = "filename";
    private static final String SUBJECT = "Subject";
    private static final String CONTENT_DISPOSITION = "Content-Disposition";

    /** Plain mail content from MimeMessage */
    private String plainContent;

    /** Html mail content from MimeMessage */
    private String htmlContent;

    /** List of attachments of MimeMessage */
    private final List<DataSource> attachmentList = new ArrayList<>();

    /** Is this a Multipart email */
    private boolean isMultiPart = false;

    //Список inline-частей
    private final List<DataSource> inlineList = new ArrayList<>();

    private final ConfigurationProperties configurationProperties;

    private final MailProcessHelper mailProcessHelper;

    /** Вложения, хранящиеся по их идентификатору контента */
    private final Map<String, DataSource> dataSourceMap = new HashMap<>();

    /**
     * Контейнер частей параметра в разбираемой строке
     */
    private record ParameterContainer(String key, String value)
    {
    }

    public MailParser(MimeMessage message, ConfigurationProperties configurationProperties,
            MailProcessHelper mailProcessHelper, MessageFacade messageFacade)
    {
        super(message);
        this.configurationProperties = configurationProperties;
        this.mailProcessHelper = mailProcessHelper;
        this.message = messageFacade;
    }

    @Override
    public List<DataSource> getAttachmentList()
    {
        return attachmentList;
    }

    @Override
    public String getHtmlContent()
    {
        return htmlContent;
    }

    public List<DataSource> getInlineList()
    {
        return inlineList;
    }

    @Override
    public String getPlainContent()
    {
        return plainContent;
    }

    @Override
    public boolean hasAttachments()
    {
        return !this.attachmentList.isEmpty();
    }

    @Override
    public boolean hasHtmlContent()
    {
        return this.htmlContent != null;
    }

    @Override
    public boolean hasPlainContent()
    {
        return plainContent != null;
    }

    @Override
    public boolean isMultipart()
    {
        return isMultiPart;
    }

    public List<IInboundMailAttachment> parseInlineAndAttachments() throws MessagingException, IOException
    {
        return mailProcessHelper.rejectUnacceptableAttachments(parseInlineAndAttachments(null, getMimeMessage()));
    }

    /**
     * Метод рекурсивно производит парсинг/предобработку вложений email-писем, считывая их из eml-файла
     * в объекты класса {@link InboundMailAttachment}, и заполняя нужные поля таких объектов
     * @param parent Родитель текущей части письма (multipart-структура)
     * @param part Текущая часть письма (блок multipart-структуры)
     * @return Список полученных и размеченных вложений - объектов класса {@link InboundMailAttachment}
     * @throws MessagingException При ошибках в работе с полями объектов {@link MimePart}
     * @throws IOException При ошибках чтения и записи
     */
    private List<IInboundMailAttachment> parseInlineAndAttachments(@Nullable Multipart parent, MimePart part)
            throws MessagingException, IOException
    {
        List<IInboundMailAttachment> inboundAttachs = Lists.newArrayList();
        if (part.isMimeType("text/*")
                && (part.getHeader(CONTENT_DISPOSITION) == null || (StringUtilities.contains(
                part.getHeader(CONTENT_DISPOSITION)[0], Part.INLINE))
                && !StringUtilities.contains(part.getHeader(CONTENT_DISPOSITION)[0], FILENAME_PARAMETER)))
        {
            return inboundAttachs;
        }
        if (part.isMimeType("multipart/*"))
        {
            Multipart mp = (Multipart)part.getContent();
            int count = mp.getCount();
            for (int i = 0; i < count; i++)
            {
                inboundAttachs.addAll(parseInlineAndAttachments(mp, (MimeBodyPart)mp.getBodyPart(i)));
            }
            return inboundAttachs;
        }

        InboundMailAttachment attachment = new InboundMailAttachment();

        DataSource ds = processDataSource(parent, part);
        try (InputStream is = ds.getInputStream())
        {
            String filename = getDataSourceName(part, ds);
            byte[] data = null;

            if (filename != null)
            {
                filename = MimeUtility.decodeText(MimeUtility.unfold(filename));
            }
            else
            {
                filename = part.getContentID() + getExtension(part.getContentType());
            }

            if (part.isMimeType("message/rfc822"))
            {
                MimeMessage attachedMessage = createAttachedMessage(is, part);
                //Корректная конвертация в массив байтов вложенного письма
                try (ByteArrayOutputStream baos = new ByteArrayOutputStream())
                {
                    attachedMessage.writeTo(baos);
                    data = baos.toByteArray();
                }
                filename = getNestedMessageFilename(attachedMessage, part);
                //Принудительно письма делаем вложениями
                attachment.setDisposition(Part.ATTACHMENT);
                inboundAttachs.addAll(parseInlineAndAttachments(null, attachedMessage));
            }
            if (null == data)
            {
                data = IOUtils.toByteArray(is);
            }
            attachment.setFilename(DELIMITER.matcher(filename).replaceAll("_"));
            attachment.setContentType(part.getContentType());
            attachment.setData(data);
            if (StringUtilities.isEmpty(attachment.getDisposition()))
            {
                String[] contentDispositionArr = part.getHeader(CONTENT_DISPOSITION);
                if (contentDispositionArr != null)
                {
                    String contentDisposition = contentDispositionArr[0];
                    if (StringUtilities.contains(contentDisposition, Part.ATTACHMENT))
                    {
                        attachment.setDisposition(Part.ATTACHMENT);
                    }
                    else if (StringUtilities.contains(contentDisposition, Part.INLINE))
                    {
                        attachment.setDisposition(Part.INLINE);
                    }
                    if (!StringUtilities.isEmpty(part.getContentID()))
                    {
                        attachment.setContentId(getPartContentID(part));
                    }
                }
                else
                {//если Content-Disposition не удалось установить однозначно, следуем алгоритму из NSDPRD-13437
                    if (!StringUtilities.isEmpty(part.getContentID()))
                    {//проверяем наличие ссылки на это вложение в HTML-коде письма
                        String contentID = getPartContentID(part);
                        attachment.setContentId(contentID);
                        if (parent != null && (StringUtilities.contains(parent.getContentType(), "multipart/related;")
                                || StringUtilities.contains(parent.getContentType(), "multipart/mixed;"))
                                && StringUtilities.contains(this.getHtmlContent(), "cid:" + contentID))
                        {//если ссылка на это вложение есть, и она в блоке multipart/related или multipart/mixed
                            attachment.setDisposition(Part.INLINE);// то вложение inline
                        }
                        else
                        {//иначе - attachment
                            attachment.setDisposition(Part.ATTACHMENT);
                        }
                    }
                    else
                    {//иначе - attachment
                        attachment.setDisposition(Part.ATTACHMENT);
                    }
                }
            }
            inboundAttachs.add(attachment);
            return inboundAttachs;
        }
    }

    /**
     * Сформировать вложенное письмо. <br>
     * Внутри костыль для кейса из NSDPRD-32666: если получилось так, что в случае IMAP во вложенном письме
     * нет обязательного заголовка, а в part письма он есть - сформировать письмо из потока, в котором
     * объединены заголовки и контент part.
     * @param inputStream поток байт вложенного письма
     * @param part часть письма, в котором находится вложенное письмо
     * @throws MessagingException если при получении заголовков произошла ошибка
     */
    private static MimeMessage createAttachedMessage(InputStream inputStream, MimePart part)
            throws MessagingException
    {
        MimeMessage attachedMessage = new MimeMessage(Session.getInstance(new Properties()), inputStream);
        if (part instanceof IMAPBodyPart imapBodyPart && hasRequiredHeadersInPart(attachedMessage, part))
        {
            return new MimeMessage(attachedMessage.getSession(), imapBodyPart.getMimeStream());
        }
        return attachedMessage;
    }

    private static boolean hasRequiredHeadersInPart(MimeMessage attachedMessage, MimePart part)
            throws MessagingException
    {
        return (attachedMessage.getHeader("From") == null && part.getHeader("From") != null)
                || (attachedMessage.getHeader("Sender") == null && part.getHeader("Sender") != null);
    }

    /**
     * Вернуть имя для вложения типа message/rfc822, взяв тему письма в качестве имени.
     * Смотрим тему (Subject) письма.
     * Если у письма нет темы, смотрим тему в заголовках part (см. NSDPRD-31782).
     *
     * @param message сформированное из вложения письмо
     * @param part часть (part) разбираемого {@link #getMimeMessage() письма} со сформированным письмом (message)
     */
    private static String getNestedMessageFilename(MimeMessage message, MimePart part)
            throws MessagingException, IOException
    {
        String extension = getExtension(part.getContentType());
        String subject = message.getSubject();
        if (subject != null)
        {
            return subject + extension;
        }
        return MimeUtility.decodeText(part.getHeader(SUBJECT, null)) + extension;
    }

    /**
     * Получить Content-ID вложения, находящийся в угловых скобках.
     * Пример: "<contentID>(*)" -> "contentID"
     * (*) - после угловых скобок могут быть ещё доп.параметры, их игнорируем.
     * @param part Часть письма, content-ID которого нужно получить
     * @return Content-ID вложения, без обрамляющих угловых скобок
     * @throws MessagingException На случай ошибки в методе {@link MimePart#getContentID()}
     */
    private String getPartContentID(MimePart part) throws MessagingException
    {
        try
        {
            String rawContentID = part.getContentID();
            if (rawContentID.contains("<") && rawContentID.contains(">"))
            {
                return rawContentID.substring(rawContentID.indexOf('<') + 1, rawContentID.indexOf('>'));
            }
            return rawContentID;
        }
        catch (StringIndexOutOfBoundsException ex)
        {
            throw new ParseException(message.getMessage("MailReader.ContentIDParseFailed"));
        }
    }

    @Override
    protected String getDataSourceName(Part part, DataSource dataSource) throws MessagingException,
            UnsupportedEncodingException
    {
        try
        {
            String filename = super.getDataSourceName(part, dataSource);
            if (filename == null)
            {
                String[] contentDisposition = part.getHeader(CONTENT_DISPOSITION);
                if (contentDisposition != null && contentDisposition.length > 0)
                {
                    Map<String, String> cdNamedParameters =
                            getNamedParametersMap(parseParameters(contentDisposition[0]), NAME_PARAMETER);
                    if (!cdNamedParameters.isEmpty())
                    {
                        Map<String, String> cdFileNameParameters = getNamedParametersMap(cdNamedParameters,
                                FILENAME_PARAMETER);
                        filename = getFileNameFromParamsMap(!cdFileNameParameters.isEmpty() ? cdFileNameParameters : cdNamedParameters);
                    }
                }
            }
            return filename;
        }
        catch (ParseException ex)
        {
            String filename = "";
            //Пробуем взять название через Content-Disposition
            String[] contentDisposition = part.getHeader(CONTENT_DISPOSITION);
            if (contentDisposition != null && contentDisposition.length > 0)
            {
                String[] tokensWithFilename = FILENAME.split(contentDisposition[0]);
                if (tokensWithFilename.length == 2)
                {
                    filename = StringUtilities.trimToEmpty(
                            QUOTES.matcher(PARAMETER_DELIMITER.matcher(tokensWithFilename[1]).replaceAll(""))
                                    .replaceAll(""));
                }
            }
            if (filename.isEmpty())
            {
                filename = part.toString() + dataSource.toString();
            }
            return filename;
        }
    }

    /**
     * Парсит строку вида "k1; k2="v2"; k3=; k4='v4'" в словарь, ключами которого являются все k*, а значениями соответствующие им v*.
     * Разделителем пар в строке считаем ";"
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$121182829
     * @param rawParameters строка, содержащая извлекаемые параметры
     * @return мапа <String:String>
     */
    private static Map<String, String> parseParameters(String rawParameters)
    {
        return Arrays.stream(PARAMETER_DELIMITER.split(rawParameters))
                .map(MailParser::parseParameter)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ParameterContainer::key, ParameterContainer::value));
    }

    @Nullable
    private static ParameterContainer parseParameter(String rawParameter)
    {
        String[] splitParameter = KEY_VALUE_DELIMITER.split(rawParameter);
        return splitParameter.length == 2
                ? new ParameterContainer(splitParameter[0], unquote(splitParameter[1].trim()))
                : null;
    }

    /**
     * Метод убирает символы кавычек (',"), в которые заключена передаваемая строка
     * @param s - строка (предп-о в кавычках)
     * @return строка без кавычек
     */
    private static String unquote(String s)
    {
        while (s.startsWith("\"") || s.startsWith("'"))
        {
            s = s.substring(1);
        }
        while (s.endsWith("\"") || s.endsWith("'"))
        {
            s = s.substring(0, s.length() - 1);
        }
        return s;
    }

    /**
     * Extracts the content of a MimeMessage recursively.
     *
     * @param parent the parent multi-part
     * @param part   the current MimePart
     * @throws MessagingException parsing the MimeMessage failed
     * @throws IOException        parsing the MimeMessage failed
     */
    @Override
    protected void parse(Multipart parent, MimePart part) throws MessagingException, IOException
    {
        if (part.isMimeType("text/plain") && (plainContent == null)
                && ((part.getDisposition() == null) || (part.getDisposition().equalsIgnoreCase(Part.INLINE))))
        {
            try
            {
                if (part.getContent() != null)
                {
                    plainContent = (String)part.getContent();
                }
            }
            catch (IOException e)
            {
                //fix  http://sd-jira.naumen.ru/browse/NSDPRD-3596  проблема с IMAP протоколом на стороне клиента при пустом контенте
                if (e.getMessage().contains("No content"))
                {
                    plainContent = "";
                    LOG.debug("MailParser No content error", e);
                }
                else
                {
                    throw e;
                }
            }
        }
        else
        {
            if (part.isMimeType("text/html") && (htmlContent == null)
                    && ((part.getDisposition() == null) || (part.getDisposition().equalsIgnoreCase(Part.INLINE))))
            {
                htmlContent = (String)part.getContent();
            }
            else
            {
                if (part.isMimeType("multipart/*"))
                {
                    isMultiPart = true;
                    Multipart mp = (Multipart)part.getContent();
                    int count = mp.getCount();

                    // iterate over all MimeBodyPart

                    for (int i = 0; i < count; i++)
                    {
                        parse(mp, (MimeBodyPart)mp.getBodyPart(i));
                    }
                }
                else
                {
                    attachmentList.add(processDataSource(parent, part));
                }
            }
        }
    }

    private static String getExtension(String contentType)
    {
        ContentType ct;
        try
        {
            ct = new ContentType(contentType);
            String baseType = ct.getBaseType();
            String ext = MimeTypeRegistry.getFileExtensionByMimeType(baseType);
            if (ext != null)
            {
                return "." + ext;
            }
        }
        catch (Exception e)
        {
            return StringUtilities.EMPTY;
        }
        return StringUtilities.EMPTY;
    }

    @Override
    public String getSubject() throws Exception
    {
        String messageSubject = getMimeMessage().getHeader("Subject", null);
        String subject = super.getSubject();
        return configurationProperties.isNeedCheckWrongSymbolsInSubject()
                && subject != null && subject.indexOf('\uFFFD') != -1
                && messageSubject != null && getEncoding(messageSubject) == 'B'
                ? universalDecodeString(MimeUtility.unfold(messageSubject))
                : subject;
    }

    private static boolean isEncodedSubject(String subject)
    {
        String[] parts = subject.split(" ");
        for (String part : parts)
        {
            if (part.charAt(part.lastIndexOf('?') - 1) != '=')
            {
                return false;
            }
        }
        return true;
    }

    private static char getEncoding(String string)
    {
        int pos = string.indexOf('?', 2);
        return string.charAt(pos + 1);
    }

    /**
     * Декодируем строку в кодировке mime+base64 в случае если дефолтное декодирование возвращает не читаемые символы
     */
    private static String decodeUnionString(String string) throws UnsupportedEncodingException
    {
        int encodedIndex = string.indexOf('B', 2);
        String start = string.substring(0, encodedIndex + 2);
        String end = string.substring(string.lastIndexOf('?'));
        StringBuilder complexBuilder = new StringBuilder();
        String[] parts = string.split(" ");
        for (String part : parts)
        {
            complexBuilder.append(part, start.length(), part.lastIndexOf('?'));
        }
        return MimeUtility.decodeText(start + complexBuilder + end);
    }

    /**
     * Декодирование темы письма независимо от корректности разделения на encoded-words
     */
    private static String universalDecodeString(String string) throws UnsupportedEncodingException
    {
        StringBuilder resultBuilder = new StringBuilder();
        String[] parts = string.split(" ");

        StringBuilder encodedPartBuilder = new StringBuilder();
        boolean incorrectlyEncodedPrecedingPart = false;
        for (String part : parts)
        {
            if (!incorrectlyEncodedPrecedingPart)
            {
                if (isEncodedSubject(part))
                {
                    resultBuilder.append(MimeUtility.decodeText(part));
                }
                else
                {
                    encodedPartBuilder.append(part);
                    encodedPartBuilder.append(' ');
                    incorrectlyEncodedPrecedingPart = true;
                }
            }
            else
            {
                if (isEncodedSubject(part))
                {
                    encodedPartBuilder.append(part);
                    resultBuilder.append(decodeUnionString(encodedPartBuilder.toString()));
                    encodedPartBuilder = new StringBuilder();
                    incorrectlyEncodedPrecedingPart = false;
                }
                else
                {
                    encodedPartBuilder.append(part);
                    encodedPartBuilder.append(' ');
                }
            }
        }
        if (!encodedPartBuilder.isEmpty())
        {
            resultBuilder.append(decodeUnionString(encodedPartBuilder.toString().trim()));
        }
        return resultBuilder.toString();
    }

    /**
     * Обработать положение входящего письма, есть вложения было обработано ранее вернуть, его иначе создать новое.
     * Если Content-ID равен null, используется hash части письма с вложением
     * @param parent контейнер, в котором хранится несколько частей тела письма.
     * @param part текущий MimePart
     * @return вложения в виде DataSource
     */
    @VisibleForTesting
    public DataSource processDataSource(@Nullable final Multipart parent, final MimePart part)
            throws MessagingException, IOException
    {
        String contentID = part.getContentID();
        String cid = StringUtilities.isNotEmpty(contentID) ?
                stripContentId(contentID) :
                String.valueOf(part.hashCode());
        DataSource dataSource = findAttachmentByCid(cid);
        if (dataSource == null)
        {
            dataSource = createDataSource(parent, part);
            dataSourceMap.put(cid, dataSource);
        }
        return dataSource;
    }

    /**
     * Найти вложение, используя его идентификатор
     * @param cid идентификатор вложения
     * @return соответствующий DataSource или null, если ничего не найдено
     */
    @Override
    public DataSource findAttachmentByCid(@Nullable final String cid)
    {
        return dataSourceMap.get(cid);
    }

    /**
     * Удаляет из идентификатора контента любые пробелы и угловые скобки.
     * @param contentId идентификатор, который нужно обработать
     * @return идентификатор без пробелов и угловых скобок
     */
    private static String stripContentId(@Nullable String contentId)
    {
        if (contentId == null)
        {
            return null;
        }
        return CONTENT_ID.matcher(contentId.trim()).replaceAll("");
    }

    /**
     * Получить коллекцию вложений
     */
    @VisibleForTesting
    public Map<String, DataSource> getDataSourceMap()
    {
        return dataSourceMap;
    }
}