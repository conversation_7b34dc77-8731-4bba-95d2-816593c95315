package ru.naumen.mailreader.shared.dispatch;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.sec.shared.actions.AdminAction;

/**
 * Удаление всех связанных с задаче сообщений из очереди
 * <AUTHOR>
 */
@AdminAction
public class DeleteInboundMessagesAction implements Action<EmptyResult>
{
    private HashSet<String> uuids;

    private String taskId;

    /**
     * Конструктор для сериализации.
     */
    public DeleteInboundMessagesAction()
    {
    }

    public DeleteInboundMessagesAction(Collection<String> messageUuids)
    {
        this.uuids = new HashSet<String>(messageUuids);
    }

    public DeleteInboundMessagesAction(String taskId)
    {
        this.taskId = taskId;
    }

    /**
     * @return UUID'ы сообщений для удаления
     */
    public Set<String> getMessageUuids()
    {
        return this.uuids;
    }

    /**
     * @return код связанной задачи
     */
    public String getTaskId()
    {
        return this.taskId;
    }

    @Override
    public String toString()
    {
        StringBuilder sb = new StringBuilder("DeleteInboundEMailsAction {");
        if (!StringUtilities.isEmpty(getTaskId()))
        {
            sb.append("task=").append(getTaskId());
        }
        if (!ObjectUtils.isEmpty(getMessageUuids()))
        {
            sb.append("email uuids=").append(getMessageUuids());
        }
        sb.append('}');
        return sb.toString();
    }
}
