package ru.naumen.fts.client.extended.events;

import ru.naumen.commons.shared.utils.StringUtilities;

import com.google.gwt.event.shared.GwtEvent;

/**
 * <AUTHOR>
 * @since 07 апр. 2016 г.
 *
 */
public class SimpleSearchEvent extends GwtEvent<SimpleSearchHandler>
{
    private static Type<SimpleSearchHandler> TYPE = new Type<SimpleSearchHandler>();

    public static Type<SimpleSearchHandler> getType()
    {
        return TYPE;
    }

    private final String searchString;

    public SimpleSearchEvent(String searchString)
    {
        this.searchString = StringUtilities.trimToEmpty(searchString);
    }

    @Override
    public GwtEvent.Type<SimpleSearchHandler> getAssociatedType()
    {
        return TYPE;
    }

    public String getSearchString()
    {
        return searchString;
    }

    @Override
    protected void dispatch(SimpleSearchHandler handler)
    {
        handler.onSimpleSearch(this);
    }
}