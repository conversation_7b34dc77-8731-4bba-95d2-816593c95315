package ru.naumen.fts.server.lucene;

import java.util.Set;

import jakarta.inject.Inject;

import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoModificationEvent;

/**
 * Обработчик событий изменений настроек поиска. Вынесен в отдельный класс, так как на сброс кэша повешена кластерная
 * аннотация. Для работы AOP необходимо делать вызов метода из другого объекта
 * <AUTHOR>
 * @since 03.08.2023
 */
@Component
public class SearchChangedEventListener
{
    private static final Set<MetainfoRegion> RESET_CACHE_REGIONS = Set.of(MetainfoRegion.METACLASS_DECLARATIONS,
            MetainfoRegion.METACLASS_PRESENTATIONS, MetainfoRegion.SECURITY);

    private final SearchCacheForUserInvalidateService cacheForUserInvalidateService;

    @Inject
    public SearchChangedEventListener(SearchCacheForUserInvalidateService cacheForUserInvalidateService)
    {
        this.cacheForUserInvalidateService = cacheForUserInvalidateService;
    }

    @EventListener
    public void onApplicationEvent(MetainfoModificationEvent event)
    {
        if (event.atCommit() && CollectionUtils.containsAny(event.getAffectedRegions(), RESET_CACHE_REGIONS))
        {
            cacheForUserInvalidateService.clearCache();
        }
    }
}