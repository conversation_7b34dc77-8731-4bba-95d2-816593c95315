package ru.naumen.sec.server.auth.external.oidc.credentials;

import static ru.naumen.sec.server.auth.external.ExternalConstants.LOGOUT_TOKEN;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Optional;

import org.pac4j.core.context.CallContext;
import org.pac4j.core.context.WebContext;
import org.pac4j.core.credentials.Credentials;
import org.pac4j.core.exception.TechnicalException;
import org.pac4j.core.util.Pac4jConstants;
import org.pac4j.oidc.client.OidcClient;
import org.pac4j.oidc.config.OidcConfiguration;
import org.pac4j.oidc.credentials.OidcCredentials;
import org.pac4j.oidc.credentials.extractor.OidcCredentialsExtractor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.nimbusds.jwt.JWT;
import com.nimbusds.oauth2.sdk.AuthorizationCode;
import com.nimbusds.oauth2.sdk.ParseException;
import com.nimbusds.oauth2.sdk.id.State;
import com.nimbusds.oauth2.sdk.token.AccessToken;
import com.nimbusds.openid.connect.sdk.AuthenticationErrorResponse;
import com.nimbusds.openid.connect.sdk.AuthenticationResponse;
import com.nimbusds.openid.connect.sdk.AuthenticationResponseParser;
import com.nimbusds.openid.connect.sdk.AuthenticationSuccessResponse;

import ru.naumen.sec.server.auth.external.ExternalAuthUtils;

/**
 * Частичная копипаста {@link OidcCredentialsExtractor} с изменением вычисления переменной logoutEndpoint
 *
 * <AUTHOR>
 * @since 28.07.2021
 */
public class NauOidcExtractor extends OidcCredentialsExtractor
{
    private static final Logger logger = LoggerFactory.getLogger(NauOidcExtractor.class);

    public NauOidcExtractor(OidcConfiguration configuration, OidcClient client)
    {
        super(configuration, client);
    }

    @Override
    public Optional<Credentials> extract(final CallContext callContext)
    {
        WebContext webContext = callContext.webContext();
        final var logoutEndpoint = webContext.getRequestURL().contains(ExternalAuthUtils.LOGOUT_URL);
        // всё что внутри этого ифа - ОЧЕНЬ плохое место. без острой необходимости не трогать, т.к. очень легко
        // сломать back-channel logout
        if (logoutEndpoint)
        {
            final var logoutToken = webContext.getRequestParameter(LOGOUT_TOKEN);
            // back-channel logout
            if (logoutToken.isEmpty())
            {
                final var sid = webContext.getRequestParameter(Pac4jConstants.OIDC_CLAIM_SESSIONID).orElse(null);
                logger.debug("Handling front-channel logout for sessionId: {}", sid);
                // front-channel logout
                client.findSessionLogoutHandler().destroySession(callContext, sid);
            }
            return Optional.empty();
        }
        else
        {
            final var computedCallbackUrl = client.computeFinalCallbackUrl(webContext);
            final var parameters = retrieveParameters(webContext);
            AuthenticationResponse response;
            try
            {
                response = AuthenticationResponseParser.parse(new URI(computedCallbackUrl), parameters);
            }
            catch (final URISyntaxException | ParseException e)
            {
                throw new TechnicalException(e);
            }
            if (response instanceof AuthenticationErrorResponse authenticationErrorResponse)
            {
                logger.error("Bad authentication response, error={}",
                        authenticationErrorResponse.getErrorObject());
                return Optional.empty();
            }
            logger.debug("Authentication response successful");
            var successResponse = (AuthenticationSuccessResponse)response;
            if (configuration.isWithState())
            {
                validateState(successResponse, callContext);
            }
            return Optional.of(extractCredentialsInternal(successResponse, callContext));
        }
    }

    /**
     * Непосредственная реализация извлечения учетных данных пользователя, таких как
     * access-токен, refresh-токен и код авторизации из ответа провайдера SSO-аутентификации.
     *
     * @param successResponse успешный ответ от IDP на запрос аутентификации пользователя
     */
    protected Credentials extractCredentialsInternal(
            AuthenticationSuccessResponse successResponse, CallContext callContext) // NOSONAR неиспользуемый
    // параметр callContext нужен в переопределенном методе
    {
        OidcCredentials credentials = new OidcCredentials();
        AuthorizationCode code = successResponse.getAuthorizationCode();
        if (code != null)
        {
            credentials.setCode(code.getValue());
        }
        JWT idToken = successResponse.getIDToken();
        if (idToken != null)
        {
            credentials.setIdToken(idToken.serialize());
        }
        AccessToken accessToken = successResponse.getAccessToken();
        if (accessToken != null)
        {
            credentials.setAccessTokenObject(accessToken);
        }
        return credentials;
    }

    /**
     * Валидация параметра {@code state}. Значение этого параметра передается при редиректе пользователя
     * в IDP для получения кода авторизации, и это же значение <b>должен</b> передать IDP в ответе, содержащем
     * код авторизации.
     *
     * @param successResponse успешный ответ от IDP на запрос аутентификации пользователя
     * @param callContext контекст HTTP-запроса
     */
    private void validateState(
            AuthenticationSuccessResponse successResponse,
            CallContext callContext) throws TechnicalException
    {
        State requestState = (State)configuration.getValueRetriever()
                .retrieve(callContext, client.getStateSessionAttributeName(), client)
                .orElseThrow(() -> new TechnicalException("State cannot be determined"));

        State responseState = successResponse.getState();
        if (responseState == null)
        {
            throw new TechnicalException("Missing state parameter");
        }

        logger.debug("Request state: {}; response state: {}", requestState, responseState);
        if (!requestState.equals(responseState))
        {
            throw new TechnicalException(
                    "State parameter is different from the one sent in authentication request.");
        }
    }
}
