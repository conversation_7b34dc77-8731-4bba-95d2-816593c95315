package ru.naumen.sec.server.admin.log.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import ru.naumen.admin.shared.Constants.Categories;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.eventcleaner.log.EventStorageRuleLogService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventcleaner.rule.EventStorageRule;
import ru.naumen.metainfo.shared.eventcleaner.rule.StateValue;

/**
 * Сервис логирования действия технолога, связанных с изменением правил хранения логов событий
 * <AUTHOR>
 * @since 27.03.2023
 */
@Component
public class EventStorageRuleLogServiceImpl extends AdminLogServiceBase implements EventStorageRuleLogService
{
    private static final String ACTION_TYPE_CODE = "eventStorageRule.actionType";

    public EventStorageRuleLogServiceImpl()
    {
        categoriesWithActions.add(Categories.EVENT_STORAGE_RULE);
        longChangeNames.add("eventStorageRule.events");
    }

    private static Map<ClassFqn, List<String>> prepareStates(EventStorageRule rule)
    {
        Map<ClassFqn, List<String>> data = new HashMap<>();
        for (Map.Entry<ClassFqn, StateValue> entry : rule.getStates().entrySet())
        {
            data.put(entry.getKey(), entry.getValue().getStates());
        }
        return data;
    }

    @Override
    public void eventStorageRuleAdded(EventStorageRule rule)
    {
        createAndSave(Categories.EVENT_STORAGE_RULE, "", "add",
                metainfoUtils.getLocalizedValue(rule.getTitle()), rule.getCode());
    }

    @Override
    public void eventStorageRuleChanged(EventStorageRule actualRule, EventStorageRule oldRule)
    {
        String changes = getChanges(convertToProperties(oldRule), convertToProperties(actualRule));
        if (!changes.isEmpty())
        {
            createAndSave(Categories.EVENT_STORAGE_RULE, "", "edit",
                    metainfoUtils.getLocalizedValue(actualRule.getTitle()), oldRule.getCode(), changes);
        }
    }

    @Override
    public void eventStorageRuleRemoved(EventStorageRule rule)
    {
        createAndSave(Categories.EVENT_STORAGE_RULE, "", "remove",
                metainfoUtils.getLocalizedValue(rule.getTitle()), rule.getCode());
    }

    @Override
    protected String getActionTypeCode(String category)
    {
        return ACTION_TYPE_CODE;
    }

    private MapProperties convertToProperties(EventStorageRule rule)
    {
        final MapProperties properties = new MapProperties(6);
        properties.setProperty("eventStorageRule.title", metainfoUtils.getLocalizedValue(rule.getTitle()));
        properties.setProperty("eventStorageRule.events", rule.getEvents());
        properties.setProperty("eventStorageRule.classes", rule.getClasses());
        properties.setProperty("eventStorageRule.storageTime", rule.getStorageTime());
        properties.setProperty("eventStorageRule.states", prepareStates(rule));
        properties.setProperty("eventStorageRule.enabled", rule.isEnabled());
        return properties;
    }
}
