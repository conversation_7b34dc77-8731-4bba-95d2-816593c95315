package ru.naumen.sec.server.admin.lite.authorize;

import java.util.List;

import jakarta.inject.Inject;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.sec.shared.actions.AdminLiteAction;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Аспект для изменения логики проверки прав и усечения ответов от сервера
 *
 * <AUTHOR>
 * @since 23.01.2023
 */
@Aspect
@Component
public class AdminLiteSecurityAspect
{
    private final List<AdminLiteExtendedAccessChecker> extendCheckers;

    @Inject
    public AdminLiteSecurityAspect(List<AdminLiteExtendedAccessChecker> extendCheckers)
    {
        this.extendCheckers = extendCheckers;
    }

    /**
     * Точка перехвата выполнения проверки прав AdminLite
     */
    @Pointcut("execution(* ru.naumen.sec.server.admin.lite.authorize.AdminLiteAccessChecker.executeCheck(..))")
    public void checkAdminLite()
    {
    }

    /**
     * После базовой проверки прав на админские действия, ищем дополнительные checker и выполняем их проверки
     */
    @AfterReturning(value = "checkAdminLite()")
    public void additionalCheck(JoinPoint jp)
    {
        if (!(jp.getArgs()[0] instanceof Action<? extends Result>))
        {
            return;
        }
        Action<? extends Result> action = (Action<? extends Result>)jp.getArgs()[0];
        extendCheckers.stream()
                .filter(checker -> checker.isPossible(action))
                .forEach(checker -> checker.executeCheck(action));
    }

    /**
     * Точка перехвата ответа от сервера
     */
    @Pointcut("execution(* net.customware.gwt.dispatch.server.Dispatch.execute(..))")
    public void executeRequest()
    {
    }

    /**
     * Добавляем манипуляции над ответом сервера для AdminLite
     */
    @AfterReturning(value = "executeRequest()", returning = "response")
    public <A extends Action<R>, R extends Result> void trimResponse(JoinPoint jp, Object response)
    {
        A action = (A)jp.getArgs()[0];
        if (isNeedTrim(action))
        {
            extendCheckers.stream()
                    .filter(interceptor -> interceptor.isPossible(action))
                    .forEach(interceptor -> interceptor.trimResponse(action, (R)response));
        }
    }

    /**
     * Усечение ответа от сервера должно производится для действий доступных AdminLite, только когда текущий
     * пользователь не является полноценным администратором
     */
    private <A extends Action<R>, R extends Result> boolean isNeedTrim(A action)
    {
        return action.getClass().getAnnotation(AdminLiteAction.class) != null
                && !CurrentEmployeeContext.isCurrentUserAdmin();
    }
}
