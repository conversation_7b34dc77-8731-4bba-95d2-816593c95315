package ru.naumen.sec.server.servlets.requestqueue;

import java.io.IOException;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * Интерфейс очереди для поступивших на обработку асинхронных запросов
 * <AUTHOR>
 *
 */
public interface RequestQueue
{
    /**
     * Включить статистику
     * @param enabled
     */
    void setStatisticsEnabled(boolean enabled);

    /**
     * Состояние сбора статистики
     */
    boolean isStatisticsEnabled();

    /**
     * Текущий размер очереди
     */
    int getSize();

    /**
     * Название очереди
     */
    String getName();

    /**
     * Добавить входящий запрос в очередь преобразовав его в асинхронный
     */
    void enqueueRequest(HttpServletRequest request, HttpServletResponse response, RequestHandler handler) throws ServletException;

    /**
     * Получить первый в очереди запрос на обработку
     * @return
     */
    RequestContext getRequestToProcess();

    /**
     * Получить число потоков обрабатывающих очередь
     * @return
     */
    int getExecutorThreads();

    /**
     * Увеличить число потоков обработчиков очереди
     * @param threads число потоков которое следует добавить
     */
    void extendExecutorThreads(int threads);

    /**
     * Уменьшить число потоков обработки очереди
     * @param threads
     */
    void shrinkExecutorThreads(int threads);

    /**
     * Запомнить время проведенное запросом в ожидании
     * @param time
     */
    void requestWaitTime(long time);

    /**
     * Запомнить время обработки запроса
     * @param time
     */
    void requestProcessingTime(long time);

    /**
     * Запомнить время полного цикла запроса от попадания в очередь до выхода из системы
     * @param time
     */
    void requestTotalTime(long time);

    /**
     * CPU time запроса за время его обработки потоком обработчиком
     * @param time
     */
    void requestCPUTime(long time);

    /**
     * Аллокации памяти, байты, потоком выполнившем запрос
     * @param bytes
     */
    void requestAllocated(long bytes);

    /**
     * Увеличить значение счетчика "число отвергнутых запросов"
     */
    void requestRejected();

    /**
     * Увеличить значение счетчика "число вошедших запросов"
     */
    void requestInbound();

    /**
     * Очистить очередь, принудительно
     */
    void clear() throws IOException;

    /**
     * Приостановить обработку очереди, новые запросы не будут попадать в очередь а будут отклонятся
     */
    void pauseProcessing();

    /**
     * Запустить работу очереди
     */
    void continueProcessing();

    /**
     * Включить режим обратной совместимости. Запросы начинают обрабатываться синхронно вместо помещения в очередь
     */
    void fallbackModeOn();

    /**
     * Выключить режим обратной совместимости. Запросы начинают обрабатываться асинхронного
     */
    void fallbackModeOff();

    /**
     * Передать очереди ссылку
     * @param manager
     */
    void setQueueManager(RequestQueueManager manager);

    /**
     * Узнать лимит запросов в очереди ожидания обработки
     * @return
     */
    int getQueueLimit();

    /**
     * Установить возможность пула потоков обработки запросов убивать основные (core) потоки
     */
    void allowExecutorPoolCoreThreadTimeOut(boolean allow);
}
