package ru.naumen.selenium.core.config;

import java.util.Locale;
import java.util.Properties;

import ru.naumen.selenium.core.BrowserTS.WebBrowserType;

/**
 * Класс обертка для доступа к методам классов реализующих интерфейсы для взаимодействия с конфигурацией системы
 * <AUTHOR>
 * @since 17.01.2012
 */
public class Config
{
    /**
     * Получить экземпляр класса конфигурации
     * @return Возвращает экземпляр класса конфигурации
     */
    public static Configuration get()
    {
        return ConfigurationSingleton.getInstance().getConfiguration();
    }

    /**
     * Получить текущий тип браузера
     * @return {@link WebBrowserType}
     */
    public static WebBrowserType getBrowserType()
    {
        return WebBrowserType.valueOf(get().getWebBrowserType().toUpperCase(Locale.ENGLISH));
    }

    /**
     * Получить текущий тип БД
     * @return {@link DbType}
     */
    public static DbType getDbType()
    {
        String dialect = Config.getProperty("ddlDialect").toLowerCase(Locale.ENGLISH);
        if (dialect.contains("oracle"))
        {
            return DbType.ORACLE;
        }
        else if (dialect.contains("msddl"))
        {
            return DbType.MSSQL;
        }
        else
        {
            return DbType.POSTGRESQL;
        }
    }

    /**
     * Получить дополнительные параметры конфигурации. 
     */
    public static Properties getProperties()
    {
        return ConfigurationSingleton.getInstance().getProperties();
    }

    /**
     * Получить параметр конфигурации.
     * @param ключ, по которому получаем
     */
    public static String getProperty(String key)
    {
        return ConfigurationSingleton.getInstance().getProperties().getProperty(key);
    }

    /**
     * Проверить является ли текущий браузер браузером chrome
     * @return true/false
     */
    public static boolean isChrome()
    {
        return WebBrowserType.CHROME.equals(getBrowserType());
    }

    /**
     * Проверить является ли текущий браузер браузером firefox
     * @return true/false
     */
    public static boolean isFirefox()
    {
        return WebBrowserType.FIREFOX.equals(getBrowserType());
    }

    /**
     * Проверить является ли текущий тип БД - MSSQL
     * @return true/false
     */
    public static boolean isMssql()
    {
        return DbType.MSSQL.equals(getDbType());
    }

    /**
     * Проверить является ли текущий тип БД - oracle
     * @return true/false
     */
    public static boolean isOracle()
    {
        return DbType.ORACLE.equals(getDbType());
    }

}
