package ru.naumen.selenium.casesutil.mobile.rest.search;

import static org.hamcrest.CoreMatchers.is;

import io.restassured.response.ValidatableResponse;

import ru.naumen.selenium.casesutil.mobile.rest.DSLMobileRest;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;

/**
 * Методы для работы с поиском объектов в МК АПИ
 *
 * <AUTHOR>
 * @since 03.05.2023
 */
public class DSLMobileSearch
{
    private static final String SEARCH_BASE_PATH = "/search";

    private static final String QUERY = "query";

    /**
     * Осуществить поиск
     *
     * @param searchString строка для поиска
     * @param auth access key пользователя
     */
    public static ValidatableResponse search(String searchString, MobileAuthentication auth)
    {
        return DSLMobileRest.buildBaseRequest(SEARCH_BASE_PATH, auth)
                    .queryParam(QUERY, searchString)
                .when()
                    .get()
                .then();
    }

    /**
     * Проверяет, что запрос на поиск вернул только один объект
     *
     * @param response ответ сервера с результатом поиска
     * @param searchedObject ожидаемый объект
     */
    public static void assertSearch(ValidatableResponse response, Bo searchedObject)
    {
        response.body("items[0].count", is(1));
        response.body("items[0].items[0].uuid", is(searchedObject.getUuid()));
    }

    /**
     * Проверяет, что запрос на поиск вернул только один объект, общее количество найденных объектов соответствует
     * ожидаемому
     *
     * @param response ответ сервера с результатом поиска
     * @param searchedObject ожидаемый объект, название которого ожидается получить
     * @param count ожидаемое общее количество найденных объектов
     */
    public static void assertSearchTotalBOCount(ValidatableResponse response, Bo searchedObject, int count)
    {
        response.body("itemsCount", is(1));
        response.body("items[0].count", is(count));
        response.body("items[0].items[0].title", is(searchedObject.getTitle()));
    }
}
