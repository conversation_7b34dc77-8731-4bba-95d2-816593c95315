package ru.naumen.selenium.casesutil.role;

import java.util.Map;

import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.security.role.AbstractRoleContext;

/**
 * Класс описывает роль "Поставщик услуги"
 * 
 * <AUTHOR>
 * @since 29.04.2016
 */
public class SlmServiceSupplierRole extends AbstractRoleContext
{
    public static final String CODE = "slmServiceSupplier";

    public SlmServiceSupplierRole()
    {
        super(CODE);
    }

    public SlmServiceSupplierRole(boolean licensed)
    {
        super(CODE, licensed);
    }

    @Override
    public void addRelationWithRightContext(Map<String, Object> params)
    {
        Bo agreement = (Bo)params.get(SlmServiceRecipientRole.AGREEMENT_MODEL);
        Bo service = (Bo)params.get(SlmServiceRecipientRole.SERVICE_MODEL);
        Bo team = (Bo)params.get(TeamParticipantRole.TEAM_MODEL);
        Bo currentUser = getCurrentUser();
        DSLTeam.addEmployees(team, currentUser);
        DSLAgreement.setSupplier(agreement, team, currentUser);
        DSLAgreement.addServices(agreement, service);
    }
}
