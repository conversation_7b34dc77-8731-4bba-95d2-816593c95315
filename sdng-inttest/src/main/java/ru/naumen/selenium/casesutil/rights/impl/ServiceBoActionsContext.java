package ru.naumen.selenium.casesutil.rights.impl;

import java.util.Map;

import com.google.common.collect.Maps;

import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.rights.interfaces.IBoActionsContext;
import ru.naumen.selenium.casesutil.role.SlmServiceRecipientRole;
import ru.naumen.selenium.casesutil.role.TeamParticipantRole;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.rights.AbstractRightContext;
import ru.naumen.selenium.security.role.AbstractRoleContext;

/**
 * Необходимый контекст для тестирования прав из блока "Работа с объектами" в классе Услуга
 * <AUTHOR>
 * @since 04.05.2016
 */
public class ServiceBoActionsContext extends AbstractRightContext implements IBoActionsContext
{
    private MetaClass serviceCase;
    private Bo service;
    private Bo agreement;
    private Bo team;
    private BoStatus status;

    private ContentForm eventList;

    public ServiceBoActionsContext()
    {
        super();
    }

    @Override
    public void addRightsToEmployee(Bo currentUser, AbstractRoleContext... roles)
    {
        createProfile(currentUser, serviceCase, roles);
        //Добавляем связь между ролями и контекстом прав
        Map<String, Object> params = Maps.newHashMap();
        params.put(SlmServiceRecipientRole.AGREEMENT_MODEL, agreement);
        params.put(SlmServiceRecipientRole.SERVICE_MODEL, service);
        params.put(TeamParticipantRole.TEAM_MODEL, team);
        for (AbstractRoleContext role : roles)
        {
            role.addRelationWithRightContext(params);
        }
    }

    @Override
    public ContentForm getEventList()
    {
        return eventList;
    }

    @Override
    public Bo getNewClient(NewClientType type)
    {
        throw new ErrorInCodeException("У услуги нет основной привязки.");
    }

    @Override
    public Bo getObject()
    {
        return service;
    }

    @Override
    public MetaClass getObjectCase()
    {
        return serviceCase;
    }

    @Override
    public MetaClass getParentCase()
    {
        return null;
    }

    @Override
    public Bo getRespEmpl()
    {
        return null;
    }

    @Override
    public Bo getRespTeam()
    {
        return null;
    }

    @Override
    public BoStatus getStatus()
    {
        return status;
    }

    @Override
    protected void prepareImmutableData()
    {
        //Создаем типы объектов
        serviceCase = DAOServiceCase.create();

        MetaClass teamCase = DAOTeamCase.create();
        DSLMetaClass.add(serviceCase, teamCase);
        team = DAOTeam.create(teamCase);
        DSLBo.add(team);

        agreement = SharedFixture.agreement();

        eventList = DAOContentCard.createEventList(serviceCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(eventList);
    }

    @Override
    protected void prepareMutableData()
    {
        //Создаем объекты
        if (serviceCase == null)
        {
            prepareImmutableData();
        }
        service = DAOService.create(serviceCase);
        DSLBo.add(service);

        BoStatus registered = DAOBoStatus.createRegistered(serviceCase.getFqn());
        status = DAOBoStatus.createUserStatus(serviceCase.getFqn());
        DSLBoStatus.add(status);
        DSLBoStatus.setTransitions(registered, status, registered);
    }
}
