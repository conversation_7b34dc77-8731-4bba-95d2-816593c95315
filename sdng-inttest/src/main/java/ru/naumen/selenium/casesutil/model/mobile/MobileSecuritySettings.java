package ru.naumen.selenium.casesutil.model.mobile;

import static ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication.EmbeddedApplicationType.CustomLoginFormApplication;
import static ru.naumen.selenium.casesutil.model.mobile.DAOMobile.DEFAULT_PASSWORD_STORAGE_TIME;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.mobile.rest.auth.MobileLoginType;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;

/**
 * Модель настроек безопасности мобильного приложения
 *
 * <AUTHOR>
 * @since 25.03.2019
 */
public class MobileSecuritySettings extends AbstractMobileView
{
    public static final String ACCESS_KEY_LIFETIME = "accessKeyLifetime";
    public static final String PASSWORD_STORAGE_TIME = "passwordStorageTime";
    public static final String ADDITIONAL_AUTHENTICATION = "additionalAuthentication";
    public static final String LOGIN_ATTEMPTS_COUNT = "loginAttemptsCount";
    public static final String LOGIN_TYPE = "loginType";
    public static final String CUSTOM_LOGIN_FORM_CODE = "customLoginFormCode";
    public static final String CUSTOM_LOGIN_MODULE_CODE = "customLoginModuleCode";

    private long accessKeyLifetime = 900L;
    private long passwordStorageTime = DEFAULT_PASSWORD_STORAGE_TIME;
    private boolean additionalAuthentication = false;
    private long loginAttemptsCount = 5L;
    private MobileLoginType loginType = MobileLoginType.SYSTEM;
    private String customLoginFormCode;
    private String customLoginModuleCode;

    public long getAccessKeyLifetime()
    {
        return accessKeyLifetime;
    }

    public long getPasswordStorageTime()
    {
        return passwordStorageTime;
    }

    public boolean getAdditionalAuthentication()
    {
        return additionalAuthentication;
    }

    public long getLoginAttemptsCount()
    {
        return loginAttemptsCount;
    }

    public MobileLoginType getLoginType()
    {
        return loginType;
    }

    public String getCustomLoginFormCode()
    {
        return customLoginFormCode;
    }

    public String getCustomLoginModuleCode()
    {
        return customLoginModuleCode;
    }

    public void setAccessKeyLifetime(long accessKeyLifetime)
    {
        this.accessKeyLifetime = accessKeyLifetime;
    }

    public void setPasswordStorageTime(long passwordStorageTime)
    {
        this.passwordStorageTime = passwordStorageTime;
    }

    public void setAdditionalAuthentication(boolean additionalAuthentication)
    {
        this.additionalAuthentication = additionalAuthentication;
    }

    public void setLoginAttemptsCount(long loginAttemptsCount)
    {
        this.loginAttemptsCount = loginAttemptsCount;
    }

    public void setLoginType(MobileLoginType loginType)
    {
        this.loginType = loginType;
    }

    public void setCustomLoginForm(EmbeddedApplication application)
    {
        Assert.assertEquals(CustomLoginFormApplication.getCode(), application.getApplicationType());

        this.loginType = MobileLoginType.CUSTOM_FORM;
        this.customLoginFormCode = application.getCode();
    }

    public void setCustomLoginModule(ModuleConf module)
    {
        this.loginType = MobileLoginType.CUSTOM_MODULE;
        this.customLoginModuleCode = module.getCode();
    }
}
