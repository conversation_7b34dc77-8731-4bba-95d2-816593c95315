package ru.naumen.selenium.cases.admin.system.administration;

import static ru.naumen.selenium.casesutil.messages.EventListMessages.USER_SHALL_NOT_PASS_CONCURRENT_LICENSES_EXCEEDED;

import org.junit.Assert;
import org.junit.Test;

import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLSession;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование входа суперпользователя под другим сотрудником
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00120
 * <AUTHOR>
 * @since 09.10.2014
 *
 */
public class SessionTest extends AbstractTestCase
{
    public final static Gson GSON = new GsonBuilder().serializeNulls().create();

    /**
     * Тестирование перенаправления на страницу входа при попытке перейти на другую страницу
     * в интерфейсе администратора после истечения сессии
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00120
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42793116
     * <br>
     * <ol>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Завершить его сессию, оставив открытым в текущей вкладке интерфейс администратора</li>
     * <li>Перейти на страницу "Администрирование"</li>
     * <li>Проверить, что появилось диалоговое окно "Операция не может быть выполнена" и нажать "Авторизоваться"</li>
     * <li>Проверить, что произошел переход на страницу входа в систему с сообщением
     * "Превышен период ожидания сессии или данная учетная запись используется на другом компьютере."</li>
     * </ol>
     */
    @Test
    public void testCorrectLogoutOnSuperUserSessionExpired()
    {
        GUILogon.asSuper();
        //TODO убрать после NSDPRD-30443 (https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$228693144)
        GUINavigational.goToAdministration();
        GUINavigational.goToCatalogs();
        DSLSession.disconectTesterSession(tester);
        GUINavigational.goToAdministration();
        tester.waitAsyncCall();
        GUIForm.assertQuestionAppear("Сообщение с текстом 'Операция не может быть выполнена' не появилось.");
        GUIForm.clickNo();

        GUIError.assertContainsInErrorMessage(GUILogon.ID_ERROR_MESSAGE, ErrorMessages.ERROR_TIME_OUT);
    }

    /**
     * Тестирование  входа в систему суперпользователя под сотрудником, обладающим лицензией, которая уже занята другим
     * пользователем.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00120
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Загрузить лицензионный файл "lisencse10.xml", в котором присутствует лицензия «Конкурентная 2» в 
     * единственном экземпляре</li>
     * <li>Создать 2 сотрудников: employee1 с лицензией «Конкурентная 2» employee2 с лицензией «Конкурентная 2»</li>
     * <li>Зайти в систему под сотрудником employee1</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Залогиниться как суперпользователь<li>
     * <li>Перейти в раздел "Администрирование"<li>
     * <li>Нажать кнопку "Войти под сотрудником"<li>
     * <li>Выбрать сотрудника employee2</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что появляется сообщение: "Для конкурентных лицензий 'Конкурентная2' превышено максимально
     * допустимое количество пользователей."</li>
     * </ol>
     */
    @Test
    public void testLoginWithConcurrentLicenseTakenByDifferentEmployees()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.LICENSE10_PATH);

        Bo employee1 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        employee1.setLicenseCode(GSON.toJson(Sets.newHashSet("concurrent2")));
        Bo employee2 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        employee2.setLicenseCode(GSON.toJson(Sets.newHashSet("concurrent2")));
        DSLBo.add(employee1, employee2);

        DSLSession.getNewSessionCookie(employee1);

        //Выполнение действий
        GUILogon.asSuper();
        GUIAdmin.goToCard();
        GUIAdmin.openLoginForm();
        GUIAdmin.selectUserInSelect(SharedFixture.ou(), employee2);

        //Проверка
        GUIForm.applyFormAssertAttention(ConfirmMessages.CONCURRENT_LICENSE_ERROR_MESSAGE, "Конкурентная2", employee2.getTitle());
    }

    /**
     * Тестирование входа в систему суперпользователя под сотрудником, который уже залогинен в системе и занимает
     * последнюю конкурентную лицензию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00120
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Загрузить лицензионный файл "lisencse10.xml", в котором присутствует лицензия «Конкурентная 2» в
     * единственном экземпляре</li>
     * <li>Создать сотрудника: employee1 с лицензией «Конкурентная 2»</li>
     * <li>Зайти в систему под сотрудником employee1</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Залогиниться как суперпользователь<li>
     * <li>Перейти в раздел "Администрирование"<li>
     * <li>Нажать кнопку "Войти под сотрудником"<li>
     * <li>Выбрать сотрудника employee1</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что текущий пользователь employee1</li>
     * </ol>
     */
    @Test
    public void testLoginWithConcurrentLicenseTakenByTheSameEmployee()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.LICENSE10_PATH);

        Bo employee1 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        employee1.setLicenseCode(GSON.toJson(Sets.newHashSet("concurrent2")));
        DSLBo.add(employee1);

        DSLSession.getNewSessionCookie(employee1);

        //Выполнение действий
        GUILogon.asSuper();
        GUIAdmin.goToCard();
        GUIAdmin.loginAsUser(SharedFixture.ou(), employee1);

        //Проверка
        GUILogon.assertAuthorizedLogin(employee1.getTitle());
    }

    /**
     * Тестирование корректного отображения логина пользователя в логах при аутентификации через accessKey с
     * конкурентной лицензией, если сессия в это время занята
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00388
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$103131879
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Загрузить лицензионный файл "lisencse10.xml", в котором присутствует лицензия «Конкурентная 2» в
     * единственном экземпляре</li>
     * <li>Создать 2 сотрудников: employee1 с лицензией «Конкурентная 2», employee2 с лицензией «Конкурентная 2»</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Зайти в систему под сотрудником employee1</li>
     * <li>Сделать попытку входа в систему под сотрудником employee2 с помощью Access Key</li>
     * <li>Выполнить скрипт, получающий сообщение последнего события с категорией loginFailure из таблицы tbl_event<li>
     * <li>Проверить, что сообщение соответствует шаблону "Пользователю 'username' не удалось войти в систему: Для
     * конкурентных лицензий "Конкурентная2" превышено максимально допустимое количество пользователей." и корректно
     * указан логин пользователя<li>
     */
    @Test
    public void testFailureAccessKeyAuthCorrectRecordInLog()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.LICENSE10_PATH);

        Bo employee1 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        employee1.setLicenseCode(GSON.toJson(Sets.newHashSet("concurrent2")));
        Bo employee2 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        employee2.setLicenseCode(GSON.toJson(Sets.newHashSet("concurrent2")));
        DSLBo.add(employee1, employee2);

        //Выполнение действий
        DSLSession.getNewSessionCookie(employee1);
        try
        {
            DSLSession.loginByAccessKey(employee2);
            Assert.fail();
        }
        catch (ErrorInCodeException ex)
        {
            // Не обрабатываем ошибку, так как корректную её запись в лог нам и нужно проверить
            String getLastLoginFailureLogMessageScript = "api.db.query(\"select e.eventMessages from Event e where e"
                    + ".eventCategory = 'loginFailure' order by e.eventDate desc\").setMaxResult(1).list()";
            String scriptResult = new ScriptRunner(getLastLoginFailureLogMessageScript).runScript().get(0);
            Assert.assertTrue("Некорректное сообщение в логе",
                    scriptResult.contains(String.format(USER_SHALL_NOT_PASS_CONCURRENT_LICENSES_EXCEEDED,
                            employee2.getLogin(), "Конкурентная2")));
        }
    }
}
