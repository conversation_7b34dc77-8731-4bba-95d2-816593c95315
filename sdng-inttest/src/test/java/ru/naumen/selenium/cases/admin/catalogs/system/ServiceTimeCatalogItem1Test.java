package ru.naumen.selenium.cases.admin.catalogs.system;

import java.util.List;

import org.junit.Assert;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUIServiceTime;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.ActionType;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.DateTimeUtils;
import ru.naumen.selenium.util.StringUtils;

/**
 * Тестирование элементов системного справочника Классы обслуживания
 * <AUTHOR>
 * @since 16.12.2011
 */
public class ServiceTimeCatalogItem1Test extends AbstractTestCase
{
    /**
     * Тестирование добавления периода в исключения - проверка на больше меньше дата
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00146
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавляем элемент item в справочник "Классы обслуживания"</li>
     * <li>Добавляем в блок Исключения в графике обслуживания дату 08.03.2012</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку элемента  item</li>
     * <li>В блоке Исключения в графике обслуживания добавляем период обслуживания для даты 08.03.2012</li>
     * <li>Указываем период: 15.45-15.15. Проверяем, что появилось сообщение об ошибке.</li>
     * <li>Указываем период: 15.45-15.45. Проверяем, что появилось сообщение об ошибке.</li>
     * <li>Указываем период: 15.45-16.30. Проверяем, что период добавился.</li>
     * <li>В блоке Исключения в графике обслуживания добавляем период обслуживания для даты 08.03.2012</li>
     * <li>Указываем период: 13.00-16.15. Проверяем, что появилось сообщение об ошибке.</li>
     * <li>Указываем период: 16.00-17.15. Проверяем, что появилось сообщение об ошибке.</li>
     * <li>Указываем период: 0.00-15.45. Проверяем, что период добавился.</li>
     * <li>Указываем период: 16.30-23.45. Проверяем, что период добавился.</li>
     * </ol>
     */
    @Test
    public void testAddExclutionPeriodToServiceTime()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(item);
        GUILogon.asSuper();
        GUIServiceTime.addExclusion(item, "08.03.2012");
        //Выполнение действия
        GUICatalogItem.goToCard(item);
        //Добавляем период 15.45-15.15
        GUIServiceTime.clickPictogramExclusion("08.03.2012", "add");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("15", "45", "15", "15");
        GUIForm.applyFormAssertError(ErrorMessages.EXCL_ERROR);
        //Добавляем период 15.45-15.45
        GUIServiceTime.fillPeriodOnForm("15", "45", "15", "45");
        GUIForm.applyFormAssertError(ErrorMessages.EXCL_ERROR);
        //Добавляем период 15.45-16.30
        GUIServiceTime.fillPeriodOnForm("15", "45", "16", "30");
        GUIForm.applyForm();
        //Проверям, что период 15.45-16.30 добавился
        GUIServiceTime.assertExclusion(item, "08.03.2012", "15:45-16:30");
        //Добавляем период 13.00-16.15
        GUIServiceTime.clickPictogramExclusion("08.03.2012", "add");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("13", "00", "16", "15");
        GUIForm.applyFormAssertError(ErrorMessages.EXCL_ERROR);
        //Добавляем период 16.00-17.15
        GUIServiceTime.fillPeriodOnForm("16", "00", "17", "15");
        GUIForm.applyFormAssertError(ErrorMessages.EXCL_ERROR);
        //Добавляем период 0.00-15.45
        GUIServiceTime.fillPeriodOnForm("0", "00", "15", "45");
        GUIForm.applyForm();
        //Проверям, что период 0.00-15.45 добавился
        GUIServiceTime.assertExclusion(item, "08.03.2012", "0:00-15:45, 15:45-16:30");
        //Добавляем период 16.30-23.45
        GUIServiceTime.clickPictogramExclusion("08.03.2012", "add");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("16", "30", "23", "45");
        GUIForm.applyForm();
        //Проверям, что период 16.30-23.45 добавился
        GUIServiceTime.assertExclusion(item, "08.03.2012", "0:00-15:45, 15:45-16:30, 16:30-23:45");
    }

    /**
     * Тестирование добавления периода - проверка на больше меньше дата
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00146
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавляем элемент item в справочник "Классы обслуживания"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку элемента  item</li>
     * <li>В блоке График обслуживания добавляем период обслуживания для Вторника</li>
     * <li>Указываем период: 3.30-3.15. Проверяем, что появилось сообщение об ошибке.</li>
     * <li>Указываем период: 3.30-3.30. Проверяем, что появилось сообщение об ошибке.</li>
     * <li>Указываем период: 3.30-4.30. Проверяем, что период добавился.</li>
     * <li>В блоке График обслуживания добавляем период обслуживания для Вторника</li>
     * <li>Указываем период: 3.00-4.00. Проверяем, что появилось сообщение об ошибке.</li>
     * <li>Указываем период: 4.00-5.00. Проверяем, что появилось сообщение об ошибке.</li>
     * <li>Указываем период: 0.00-3.30. Проверяем, что период добавился.</li>
     * <li>Указываем период: 4.30-23.45. Проверяем, что период добавился.</li>
     * </ol>
     */
    @Test
    public void testAddPeriodToServiceTime()
    {
        //Подготовка

        CatalogItem item = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(item);
        //Выполнение действия
        GUILogon.asSuper();
        GUICatalogItem.goToCard(item);
        //Добавляем период 3.30-3.15
        GUIServiceTime.clickPictogramST("tuesday", "add");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("3", "30", "3", "15");
        GUIForm.applyFormAssertError(ErrorMessages.PERIOD_ERR);
        //Добавляем период 3.30-3.30
        GUIServiceTime.fillPeriodOnForm("3", "30", "3", "30");
        GUIForm.applyFormAssertError(ErrorMessages.PERIOD_ERR);
        //Добавляем период 3.30-4.30
        GUIServiceTime.fillPeriodOnForm("3", "30", "4", "30");
        GUIForm.applyForm();
        //Проверям, что период 3.30-4.30 добавился
        GUIServiceTime.assertTSPeriod(item, "tuesday", "3:30-4:30");
        //Добавляем период 3.00-4.00
        GUIServiceTime.clickPictogramST("tuesday", "add");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("3", "00", "4", "00");
        GUIForm.applyFormAssertError(ErrorMessages.PERIOD_ERR);
        //Добавляем период 4.00-5.00
        GUIServiceTime.fillPeriodOnForm("4", "00", "5", "00");
        GUIForm.applyFormAssertError(ErrorMessages.PERIOD_ERR);
        //Добавляем период 0.00-3.30
        GUIServiceTime.fillPeriodOnForm("0", "00", "3", "30");
        GUIForm.applyForm();
        //Проверям, что период 0.00-3.30 добавился
        GUIServiceTime.assertTSPeriod(item, "tuesday", "0:00-3:30, 3:30-4:30");
        //Добавляем период 4.30-23.45
        GUIServiceTime.clickPictogramST("tuesday", "add");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("4", "30", "23", "45");
        GUIForm.applyForm();
        //Проверям, что период 4.30-23.45 добавился
        GUIServiceTime.assertTSPeriod(item, "tuesday", "0:00-3:30, 3:30-4:30, 4:30-23:45");
    }

    /**
     * Тестирование добавления элемента справочника Классы обслуживания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника Классы обслуживания</li>
     * <li>В блоке Элементы справочника нажимаем кнопку Добавить</li>
     * <li>На форме добавления атрибута заполнить поля: название, код, описание</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника, блок Элементы справочника: название(ссылка на карточку элемента) и код элемента, описание</li>
     * </ol>
     */
    @Test
    public void testAddSimpleServiceTimeCatalogItem()
    {
        //Выполнение действия
        GUILogon.asSuper();
        CatalogItem item = DAOCatalogItem.createServiceTime();
        GUICatalogItem.callAddForm(item);
        GUIForm.assertDialogAppear("Форма добавления элемента справочника \"Классы обслуживания\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, item.getCode());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.DESCR_VALUE, item.getDescription());
        GUIForm.applyModalForm();
        //Проверки
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование обновления привязки к элементу справочника Классы обслуживания в таблице соответствий при смене версии
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00338
     * http://sd-jira.naumen.ru/browse/NSDPRD-4765
     * <br>
     * <ol>
     * <li>Создать типы scCase, ouCase, agreementCase классов "Запрос", "Отдел", "Соглашение" соответственно</li>
     * <li>Создать атрибут attr (тип: Элемент справочника, справочник: Классы обслуживания) в типе scCase</li>
     * <li>Установить значения по умолчанию для атрибутов "Приоритет" (22) и "Нормативное время обслуживания" (2 HOUR)
     * в типе scCase</li>
     * <li>Добавить элемент serviceTimeItem в справочник "Классы обслуживания"</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под суперпользователем</li>
     * <li>Добавить период обслуживания (понедельник/11:15-18:00) для serviceTimeItem</li>
     * <li>Создать соглашение agreement типа agreementCase с классом обслуживания serviceTimeItem</li>
     * <li>Создать отдел ou типа ouCase и добавить к нему соглашение agreement</li>
     * <li>Создать элемент таблицы соответствий ruleItem (объект: scCase, определяемый атрибут:
     * attr, определяющий атрибут: Соглашение; таблица соответствий: attr - serviceTimeItem, Соглашение - agreement)</li>
     * <li>Сделать атрибут attr определяемым по таблице соответствий и установить ему правило определения ruleItem</li>
     * <li>Создать запрос sc1(тип: scCase, контрагент: ou, соглашение: agreement)</li>
     * <li>Перейти на карточку serviceTimeItem</li>
     * <li>Нажать на кнопку "Редактировать" и в появившемся диалоговом окне ввести новое название для serviceTimeItem</li>
     * <li>Нажать кнопку сохранить на диалоговом окне и проверить, что форма закрылась</li>
     * <li>Нажать на пиктограмму "Редактировать" напротив дня недели "понедельник"</li>
     * <li>Заполнить период обслуживания "05:00-22:45" и нажать на кнопку "Сохранить"</li>
     * <li>Подтвердить создание черновика, нажав на кнопку "Да" в открывшемся диалоговом окне</li>
     * <li>Утвердить класс обслуживания serviceTimeItem</li>
     * <li>Создать запрос sc2(тип: scCase, контрагент: ou, соглашение: agreement)</li>
     * <li>Выполнить скрипт:
     *      <pre>
     *      ----------------------------------------------------------------
     *      utils.getFromValueMap('$str1', '$str2', '$str3');
     *      ----------------------------------------------------------------
     *      где $str1 - код атрибута attr
     *          $str2 - uuid ruleItem
     *          $str3 - uuid sc2
     *      </pre>
     * </li>
     * <li>Проверить результат выполнения скрипта (должен быть равен новому названию serviceTimeItem)</li>
     * </ol>
     */
    @Test
    public void testApproveServiceTimeCatalogItem()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass agreementCase = SharedFixture.agreementCase();

        Attribute attr = DAOAttribute.createCatalogItem(scCase.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.SERVICETIME), null);
        DSLMetainfo.add(scCase, attr);

        Attribute priorityAttr = SysAttribute.priority(scCase);
        Attribute resolutionTimeAttr = SysAttribute.resolutionTime(scCase);

        CatalogItem priorityItem = DAOCatalogItem.createPriority(22);
        DSLCatalogItem.add(priorityItem);
        resolutionTimeAttr.setDefaultValue("2 HOUR");

        priorityAttr.setDefaultValue(priorityItem.getUuid());
        DSLAttribute.edit(priorityAttr, resolutionTimeAttr);

        CatalogItem timeZoneItem = SharedFixture.timeZone();
        CatalogItem serviceTimeItem = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(serviceTimeItem);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIServiceTime.addSTPeriod(serviceTimeItem, "monday", "11", "15", "18", "00");

        Bo agreement = DAOAgreement.create(agreementCase, serviceTimeItem, serviceTimeItem);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou, agreement);

        DSLAgreement.addToRecipients(agreement, ou);

        CatalogItem ruleItem = DAOCatalogItem.createRulesSettings(scCase, attr.getCode(),
                SysAttribute.agreement(scCase).getCode());
        DSLCatalogItem.add(ruleItem);
        RsRow row = DAORsRow.create(ruleItem, attr.getCode(), serviceTimeItem.getUuid(),
                SysAttribute.agreement(scCase).getCode(), agreement.getUuid());
        DSLRsRows.addRowToRSItem(row);

        attr.setDeterminable("true");
        attr.setDeterminer(ruleItem.getCode());
        DSLAttribute.edit(attr);

        Bo sc1 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc1);

        GUICatalogItem.goToCard(serviceTimeItem);
        String newServiceTimeTitle = ModelUtils.createTitle();
        tester.click(GUIXpath.Div.EDIT);
        GUIForm.assertDialogAppear("Форма редактирования элемента справочника \"Классы обслуживания\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, newServiceTimeTitle);
        GUIForm.applyModalForm();
        GUIServiceTime.clickPictogramST("monday", "edit");
        GUIForm.confirmByYes();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("5", "00", "22", "45");
        GUIForm.applyForm();
        GUICatalogItem.resolveSTItem();

        Bo sc2 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc2);

        String scriptTemplate = String.format("utils.getFromValueMap('%s', '%s', '%s');", attr.getCode(),
                ruleItem.getUuid(), sc2.getUuid());
        ScriptRunner script = new ScriptRunner(scriptTemplate);
        String result = script.runScript().get(0).trim();
        Assert.assertEquals("Скрипт вернул наименование архивной версии класса обслуживания", newServiceTimeTitle,
                result);
    }

    /**
     * Тестирование архивации элемента справочника Классы обслуживания с открытым соглашением NSDPRD-2401
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавляем элемент в справочник "Классы обслуживания"</li>
     * <li>Создаем соглашение и привязываем его к созданному элементу справочника "Классы обслуживания"</li>
     * <br>
     * <b>Выполнение действий.</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти в карточку созданного элемента справочника "Классы обслуживания"</li>
     * <li>Нажать "Переместить в архив"</li>
     * <br>
     * <b>Проверки.</b>
     * <li>Появилось сообщение:
     * Элемент справочника 'Классы обслуживания' 'classname'
     * не может быть помещен в архив по следующим причинам:
     * 1. Элемент связан со следующими объектами:
     * соглашение 'agreementname'.</li>
     * </ol>
     */
    @Test
    public void testArchiveWithActiveAgreement()
    {
        //Подготовка
        //Создаем элемент справочника "Классы обслуживания"
        CatalogItem item = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(item);
        //Создаем соглашение
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(agreementCase);
        Bo agreement = DAOAgreement.create(agreementCase, item, item);
        DSLBo.add(agreement);
        //Выполнение действия
        GUILogon.asSuper();
        GUICatalogItem.goToCard(item);
        tester.click(ActionType.ARCHIVE.getXpath());
        GUIForm.assertQuestionAppear("Форма подтверждения архивирования элемента справочника не появилась.");
        GUIForm.clickYes();
        //Проверка
        String expectedMessage = String.format(ErrorMessages.CAT_ITEM + "'Классы обслуживания' '%s' "
                + ErrorMessages.NOT_BE_REM + "\n" + ErrorMessages.NO_FORMATTED_FIRST + ErrorMessages.CAT_ITEM
                + "'Классы обслуживания' " + ErrorMessages.HAS_REL_OBJS + "соглашение '%s'.", item.getTitle(),
                agreement.getTitle());
        GUIForm.assertErrorMessageOnForm(expectedMessage);
    }

    /**
     * Тестирование архивации элемента справочника Классы обслуживания с открытым запросом NSDPRD-176
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00146
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавляем элемент serviceTimeItem в справочник "Классы обслуживания"</li>
     * <li>Создаем запрос sc</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Перейти в карточку serviceTimeItem</li>
     * <li>Нажать "Переместить в архив"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появилось сообщение:
     * Элемент справочника 'Таблицы соответствий' serviceTimeItem
     * не может быть помещен в архив
     * Элемент справочника 'Таблицы соответствий' связан со
     * следующими объектами: запрос sc.</li>
     * <li>ruleItem1 не в архиве.</li>
     * </ol>
     */
    @Test
    public void testArchiveWithOpenedSc()
    {
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass scCase = DAOScCase.create();
        MetaClass emplCase = SharedFixture.employeeCase();
        MetaClass agreementCase = SharedFixture.agreementCase();
        DSLMetaClass.add(scCase);

        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(scCase.getFqn()),
                DAOBoStatus.createClosed(scCase.getFqn()));

        CatalogItem timeZoneItem = SharedFixture.timeZone();
        CatalogItem serviceTimeItem = DAOCatalogItem.createServiceTime();
        CatalogItem priorityItem = SharedFixture.priority();

        MetaClass scClass = DAOScCase.createClass();
        String targetAttr1 = "priority";
        String targetAttr2 = "resolutionTime";
        String sourceAttr = "metaClass";
        CatalogItem ruleItem1 = DAOCatalogItem.createRulesSettings(scClass, targetAttr1, sourceAttr);
        CatalogItem ruleItem2 = DAOCatalogItem.createRulesSettings(scClass, targetAttr2, sourceAttr);

        DSLCatalogItem.add(serviceTimeItem, ruleItem1, ruleItem2);

        RsRow row1 = DAORsRow.create(ruleItem1, targetAttr1, priorityItem.getUuid(), sourceAttr, scCase.getFqn());
        DSLRsRows.addRowToRSItem(row1);
        RsRow row2 = DAORsRow.create(ruleItem2, targetAttr2, "10 HOUR", sourceAttr, scCase.getFqn());
        DSLRsRows.addRowToRSItem(row2);

        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, ruleItem2,
                ruleItem1);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(agreement, ou);

        DSLAgreement.addToRecipients(agreement, ou);

        Bo employee = DAOEmployee.create(emplCase, ou, true);
        DSLBo.add(employee);
        DSLTeam.addEmployees(SharedFixture.team(), employee);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);

        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCard(serviceTimeItem);
        tester.click(ActionType.ARCHIVE.getXpath());
        GUIForm.assertQuestionAppear("Форма подтверждения архивирования соглашения не появилась.");
        GUIForm.clickYes();
        String expectedMessage = String.format(ErrorMessages.CAT_ITEM + "'Классы обслуживания' '%s' "
                + ErrorMessages.NOT_BE_REM + "\n" + ErrorMessages.NO_FORMATTED_FIRST + ErrorMessages.CAT_ITEM
                + "'Классы обслуживания' " + ErrorMessages.HAS_REL_OBJS + "%%s.", serviceTimeItem.getTitle());
        String relation1 = String.format("запрос '%s'", sc.getTitle());
        String relation2 = String.format("соглашение '%s'", agreement.getTitle());
        GUIForm.assertShuffleErrorMessageOnForm(expectedMessage, ", ", relation1, relation2);
    }

    /**
     * Тестирование копирования элемента справочника Классы обслуживания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент класса обслуживания A. Создать в нем период и исключение.</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника классы обслуживания</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму копировать</li>
     * <li>На форме копирования элемента: вводим новое название, код и описание соотвтествующие элементу справочника B.</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма копирования закрылась</li>
     * <li>Карточка справочника, блок Элементы справочника: присутствует элемент А и элемент В. </li>
     * <li>Карточка элемента B, присутствуют название, описание, код, период и исклчение</li>
     * </ol>
     */
    @Test
    public void testCopyServiceTimeCatalogItem()
    {
        //Подготовка
        CatalogItem itemA = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(itemA);
        CatalogItem itemB = DAOCatalogItem.createServiceTime();
        GUILogon.asSuper();
        GUIServiceTime.addSTPeriod(itemA, "friday", "11", "15", "18", "00");
        String exclutionDate = DateTimeUtils.getRandomDateTimeddMMyyyy();
        GUIServiceTime.addExclusionWithPeriod(itemA, exclutionDate, "6", "45", "21", "30");
        //Выполнение действия
        GUICatalogItem.goToCatalog(itemA);
        GUICatalog.clickPictogram(itemA, "copy");
        GUIForm.assertDialogAppear("Форма добавления элемента справочника \"Классы обслуживания\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, itemB.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, itemB.getCode());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.DESCR_VALUE, itemB.getDescription());
        GUIForm.applyModalForm();
        //Проверки
        GUICatalogItem.assertPresent(itemB);
        GUIServiceTime.assertTSPeriod(itemB, "friday", "11:15-18:00");
        GUIServiceTime.assertExclusion(itemB, exclutionDate, "6:45-21:30");
    }

    /**
     * Тестирование ситуации, когда класс обслуживания- значение по умолчанию, и создается новая версия
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * http://sd-jira.naumen.ru/browse/NSDPRD-2131
     * <br>
     * <b>Подготовка.</b>
     * <li>Добавляем элемент serviceTimeItem в справочник "Классы обслуживания"</li>
     * <li>Делаем serviceTimeItem значением по умолчанию атрибутов supportHours и serviceHours у соглашения</li>
     * <li>Создаем запрос sc</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Перейти в карточку serviceTimeItem</li>
     * <li>Нажать Добавить период</li>
     * <li>Создать на базе данного класса черновик нового класса обслуживания</li>
     * <li>Заходим в карточку  справочника Классы обслуживания.</li>
     * <li>В блоке Элементы справочника напротив элемента  нажимаем пиктограмму редактировать</li>
     * <li>На форме редактирования элемента: меняем название</li>
     * <li>Нажать Сохранить</li>
     * <li>Нажимаем Утвердить</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>В атрибутах  supportHours и serviceHours у соглашения значение по умолчанию-измененное название класса обслуживания </li>
     * </ol>
     */
    @Test
    public void testDefaultNewVersionServiceTimeCatalogItem()
    {
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass scCase = DAOScCase.create();
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(scCase, emplCase);

        CatalogItem timeZoneItem = SharedFixture.timeZone();
        CatalogItem priorityItem = SharedFixture.priority();
        CatalogItem serviceTimeItem = DAOCatalogItem.createServiceTime();
        CatalogItem serviceTimeItemNew = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(serviceTimeItem);

        String targetAttr1 = "priority";
        String targetAttr2 = "resolutionTime";
        String sourceAttr = "metaClass";
        CatalogItem ruleItem1 = DAOCatalogItem.createRulesSettings(scCase, targetAttr1, sourceAttr);
        CatalogItem ruleItem2 = DAOCatalogItem.createRulesSettings(scCase, targetAttr2, sourceAttr);
        DSLCatalogItem.add(ruleItem1, ruleItem2);

        RsRow row1 = DAORsRow.create(ruleItem1, targetAttr1, priorityItem.getUuid(), sourceAttr, scCase.getFqn());
        RsRow row2 = DAORsRow.create(ruleItem2, targetAttr2, "10 HOUR", sourceAttr, scCase.getFqn());
        DSLRsRows.addRowToRSItem(row1, row2);

        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(agreementCase);
        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, ruleItem2,
                ruleItem1);
        Attribute supportHours = SysAttribute.supportHours(agreementCase);
        supportHours.setDefaultValue(serviceTimeItem.getUuid());
        Attribute serviceHours = SysAttribute.serviceHours(agreementCase);
        serviceHours.setDefaultValue(serviceTimeItem.getUuid());
        DSLAttribute.edit(supportHours, serviceHours);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(agreement, ou);

        DSLAgreement.addToRecipients(agreement, ou);

        Bo employee = DAOEmployee.create(emplCase, ou, true);
        DSLBo.add(employee);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);

        //Выполнение действий
        GUILogon.asSuper();
        GUICatalogItem.goToCard(serviceTimeItem);

        //Редактируем период
        GUIServiceTime.clickPictogramST("friday", "add");
        GUIForm.assertQuestion(ErrorMessages.ERR_ST_ITEM, serviceTimeItem.getTitle(), sc.getTitle());
        GUIForm.confirmByYes();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("0", "00", "18", "00");
        GUIForm.applyForm();

        GUICatalogItem.resolveSTItem();

        //Находимся на карточке нового класса обслуживания, записываем их в модель
        GUICatalogItem.setUuidByUrl(serviceTimeItemNew);

        GUIMetaClass.goToCard(agreementCase);

        // Сейчас в serviceTimeItem хранится старая версия класса осблуживания. В значении по умолчанию хранится uuid старой версии, а в интерфейсе отображается название новой версии.
        String message = "Ожидаемое значение по умолчанию атрибута типа 'Элемент справочника'  не соответствует полученному";
        GUITester.assertTextContainsWithMsg(GUIAttribute.ATTRIBUTE_TABLE_ELEMENT, serviceTimeItem.getTitle(), message,
                serviceHours.getCode(), "defaultValue");
        GUITester.assertTextContainsWithMsg(GUIAttribute.ATTRIBUTE_TABLE_ELEMENT, serviceTimeItem.getTitle(), message,
                supportHours.getCode(), "defaultValue");
    }

    /**
     * Тестирование удаления элемента справочника класса обслуживания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника Классы обслуживания. А</li>
     * <li>В элементе А добавить период и исключение.</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника Классы обслуживания</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму Удалить</li>
     * <li>Подтверждаем удаление</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника классы обслуживания, блок Элементы справочника: элемент А отсутствует</li>
     * </ol>
     */
    @Test
    public void testDeleteServiceTimeCatalogItem()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(item);
        //Выполнение действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(item);
        GUICatalog.clickPictogramRemoveAndConfirm(item);

        //Проверки
        GUICatalogItem.assertAbsence(item);
    }

    /**
     * NSDPRD-447  Не редактируются черновики и утвержденные классы обслуживания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * <br>
     * <b>Подготовка.</b>
     * <li>Добавляем элемент serviceTimeItem в справочник "Классы обслуживания"</li>
     * <li>Создаем запрос sc</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Перейти в карточку serviceTimeItem</li>
     * <li>Нажать Добавить период</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появилось сообщение:
     * Невозможно изменить класс обслуживания 'serviceTimeItem'
     * С классом обслуживания связаны запросы: 'sc'
     * Создать на базе данного класса черновик нового класса обслуживания?</li>
     * <li>Подтвердить</li>
     * </ol>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника Классы обслуживания.</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму редактировать</li>
     * <li>На форме редактирования элемента: меняем название</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Классы обслуживания: название и код</li>
     *
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника Классы обслуживания.</li>
     * <li>Нажимаем Утвердить</li>
     * <li>Нажать Сохранить</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму редактировать</li>
     * <li>На форме редактирования элемента: меняем название</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Классы обслуживания: название и код</li>
     * </ol>
     */
    @Test
    public void testEditArciveServiceTimeCatalogItem()
    {
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass scCase = DAOScCase.create();
        MetaClass emplCase = DAOEmployeeCase.create();
        MetaClass agreementCase = SharedFixture.agreementCase();
        DSLMetaClass.add(scCase, emplCase);

        CatalogItem timeZoneItem = DAOCatalogItem.createTimeZone("Antarctica/DumontDUrville");
        CatalogItem serviceTimeItem = DAOCatalogItem.createServiceTime();
        CatalogItem priorityItem = DAOCatalogItem.createPriority(1);

        MetaClass scClass = DAOScCase.createClass();
        String targetAttr1 = "priority";
        String targetAttr2 = "resolutionTime";
        String sourceAttr = "metaClass";
        CatalogItem ruleItem1 = DAOCatalogItem.createRulesSettings(scClass, targetAttr1, sourceAttr);
        CatalogItem ruleItem2 = DAOCatalogItem.createRulesSettings(scClass, targetAttr2, sourceAttr);

        DSLCatalogItem.add(timeZoneItem, serviceTimeItem, priorityItem, ruleItem1, ruleItem2);
        RsRow row = DAORsRow.create(ruleItem1, targetAttr1, priorityItem.getUuid(), sourceAttr, scCase.getFqn());
        DSLRsRows.addRowToRSItem(row);
        RsRow row2 = DAORsRow.create(ruleItem2, targetAttr2, "10 HOUR", sourceAttr, scCase.getFqn());
        DSLRsRows.addRowToRSItem(row2);

        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, ruleItem2,
                ruleItem1);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(agreement, ou);

        DSLAgreement.addToRecipients(agreement, ou);

        Bo employee = DAOEmployee.create(emplCase, ou, true);
        DSLBo.add(employee);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);

        GUILogon.asSuper();
        GUICatalogItem.goToCard(serviceTimeItem);
        GUIServiceTime.clickPictogramST("friday", "add");
        GUIForm.assertQuestion(ErrorMessages.ERR_ST_ITEM, serviceTimeItem.getTitle(), sc.getTitle());
        GUIForm.clickYes();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("11", "15", "18", "00");
        GUIForm.applyForm();

        //Тестовые действия
        serviceTimeItem.setTitle(ModelUtils.createTitle());
        GUICatalogItem.openEditForm();
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, serviceTimeItem.getTitle());
        GUIForm.applyModalForm();
        Assert.assertEquals("Название не отредактировано", serviceTimeItem.getTitle(),
                tester.getText(GUICatalog.X_TEXT_FIELD_INFO_TITLE_VALUE));

        GUICatalogItem.resolveSTItem();
        serviceTimeItem.setTitle(ModelUtils.createTitle());
        GUICatalogItem.openEditForm();
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, serviceTimeItem.getTitle());
        GUIForm.applyModalForm();
        Assert.assertEquals("Название не отредактировано", serviceTimeItem.getTitle(),
                tester.getText(GUICatalog.X_TEXT_FIELD_INFO_TITLE_VALUE));
    }

    /**
     * Тестирование редактирования элемента справочника класса обслуживания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника Классы обслуживания. А</li>
     * <li>В элементе А добавить период и исключение.</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника Классы обслуживания.</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму редактировать</li>
     * <li>На форме редактирования элемента проверяем, что код не редактируется.</li>
     * <li>На форме редактирования элемента: меняем название и описание</li>
     * <li>Нажать Сохранить</li>
     * <li>Заходим в карточку элемента А.</li>
     * <li>Меняем период обслуживания.</li>
     * <li>Добавляем период для другого дня недели</li>
     * <li>Меняем период исключения</li>
     * <li>Добавляем период исключения для другого дня</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Классы обслуживания: название и код</li>
     * <li>Карточка элемента: название, описание и код. Изменены старые периоды, добавились новые</li>
     * </ol>
     */
    @Test
    public void testEditServiceTimeCatalogItem()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(item);
        GUILogon.asSuper();
        GUIServiceTime.addSTPeriod(item, "friday", "11", "15", "18", "00");
        String exclutionDate = DateTimeUtils.getRandomDateTimeddMMyyyy();
        GUIServiceTime.addExclusionWithPeriod(item, exclutionDate, "6", "45", "21", "30");
        //Выполнение действия
        GUICatalogItem.goToCatalog(item);
        item.setTitle(ModelUtils.createTitle());
        GUICatalog.clickPictogram(item, "edit");
        GUIForm.assertDialogAppear("Форма редактирования элемента справочника \"Классы обслуживания\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        String actual = tester.getText(GUIXpath.PropertyDialogBoxContent.PROPERTY_DIALOG_BOX_CODE_CAPTION + "/span").trim();
        String message = String.format(
                "Код элемента справочника \"Классы обслуживания\" на форме редактирования не совпал с ожидаемым. Ожидаемый код:%s; полученный код:%s",
                item.getCode(), actual);
        Assert.assertEquals(message, item.getCode(), actual);
        GUIForm.applyModalForm();
        GUICatalogItem.goToCard(item);
        //Редактируем старый период
        GUIServiceTime.clickPictogramST("friday", "edit");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("5", "00", "22", "45");
        GUIForm.applyForm();
        //Редактируем староеи сключение
        GUIServiceTime.clickPictogramExclusion(exclutionDate, "edit");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("1", "15", "11", "30");
        GUIForm.applyForm();
        //Проверки
        GUICatalogItem.assertPresent(item);
        GUIServiceTime.assertTSPeriod(item, "friday", "5:00-22:45");
        GUIServiceTime.assertExclusion(item, exclutionDate, "1:15-11:30");
    }

    /**
     * Тестирование заполнения элемента справочника Классы обслуживани (заполняются график обслуживания и график исключений) графика обслуживания и графика исключений
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент класса обслуживания A.</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в карточку элемента.</li>
     * <li>Нажать на пиктограмму добавления периода обслуживания</li>
     * <li>На форме добавления выбрать период (дата начала больше даты конца)</li>
     * <li>Нажать Сохранить</li>
     * <li>Нажать на пиктограмму добавления исключения</li>
     * <li>Ввести произвольную даты</li>
     * <li>Нажать Сохранить</li>
     * <li>Нажать на пиктограмму добавления периода исключения</li>
     * <li>На форме добавления выбрать период (дата начала больше даты конца)</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка элемента: Проверить период обслуживания и период исключения. </li>
     * </ol>
     */
    @Test
    public void testFillSimpleServiceTimeCatalogItem()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(item);
        //Выполнение действия
        //Периоды зашиты
        GUILogon.asSuper();
        GUIServiceTime.addSTPeriod(item, "wednesday", "11", "15", "18", "00");
        String exclutionDate = DateTimeUtils.getRandomDateTimeddMMyyyy();
        GUIServiceTime.addExclusionWithPeriod(item, exclutionDate, "6", "45", "21", "30");
        //Проверки
        GUIServiceTime.assertTSPeriod(item, "wednesday", "11:15-18:00");
        GUIServiceTime.assertExclusion(item, exclutionDate, "6:45-21:30");
    }

    /**
     * Тестирование появления сообщения "Если не определен ни один период обслуживания, класс обслуживания обрабатывается как 24x7"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавляем элемент item в справочник "Классы обслуживания"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку элемента  item</li>
     * <li>Проверяем наличие сообщения "Если не определен ни один период обслуживания, класс обслуживания обрабатывается как 24x7"</li>
     * <li>В блоке График обслуживания добавляем период обслуживания для Понедельника</li>
     * <li>Проверяем, что сообщение отсутствует</li>
     * <li>Возвращаемся к справочнику "Классы обслуживания"</li>
     * <li>Переходим в карточку элемента item</li>
     * <li>Проверяем, что сообщение отсутствует</li>
     * <li>В блоке График обслуживания удаляем период обслуживания</li>
     * <li>Проверяем наличие сообщения</li>
     * <li>Добавляем исключение в график обслуживания</li>
     * <li>Проверяем наличие сообщения</li>
     * </ol>
     */
    @Test
    public void testInfoMessageDefaultServiceTime()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(item);
        //Выполнение действия
        GUILogon.asSuper();
        GUICatalogItem.goToCard(item);
        String expectedMessage = "Если не определен ни один период обслуживания, класс обслуживания обрабатывается как 24x7";
        String actualMessage = tester.getText(GUIServiceTime.X_INFO_MESSAGE_DEFAULT_ST);
        String message = "Сообщение о классе обслуживания, если не определен ни один период обслуживания, не соответствует ожидаемому сообщению."
                + "Полученное сообщение: '%s'; Ожидаемое сообщение: '%s'";
        //Проверям наличие сообщения
        Assert.assertEquals(String.format(message, actualMessage, expectedMessage), expectedMessage, actualMessage);
        //Добавляем период обслуживания для Понедельника
        GUIServiceTime.clickPictogramST("monday", "add");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("10", "00", "19", "00");
        GUIForm.applyForm();
        //Проверям, что период 10.00-19.00 добавился
        GUIServiceTime.assertTSPeriod(item, "monday", "10:00-19:00");
        //Проверяем отсутствие сообщения
        Assert.assertTrue("На картчоке присутствует сообщение",
                tester.waitDisappear(GUIServiceTime.X_INFO_MESSAGE_DEFAULT_ST));
        //Переходим в справочник и обратно
        GUICatalog.clickBackToCatalog();
        GUICatalogItem.goToCard(item);
        Assert.assertTrue("На картчоке присутствует сообщение",
                tester.waitDisappear(GUIServiceTime.X_INFO_MESSAGE_DEFAULT_ST));
        //Удаляем период обслуживания
        GUIServiceTime.clickPictogramST("monday", "edit");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.click(GUIServiceTime.X_DELETE_PERIOD_PICTOGRAM_ST, "1");
        GUIForm.applyForm();
        //Проверям наличие сообщения
        expectedMessage = "Если не определен ни один период обслуживания, класс обслуживания обрабатывается как 24x7";
        actualMessage = tester.getText(GUIServiceTime.X_INFO_MESSAGE_DEFAULT_ST);
        Assert.assertEquals(String.format(message, actualMessage, expectedMessage), expectedMessage, actualMessage);
        //Добавляем исключение в график обслуживания
        GUIServiceTime.addExclusionWithPeriod(item, "08.03.2012", "10", "00", "19", "00");
        //Проверям наличие сообщения
        expectedMessage = "Если не определен ни один период обслуживания, класс обслуживания обрабатывается как 24x7";
        actualMessage = tester.getText(GUIServiceTime.X_INFO_MESSAGE_DEFAULT_ST);
        Assert.assertEquals(String.format(message, actualMessage, expectedMessage), expectedMessage, actualMessage);
    }

    /**
     * Тестирование правильности отображения класса обслуживания на карточке справочника после редактирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создаем новый класс обслуживания serviceTimeItem</li>
     * <li>Создаем соглашение agreement, связанное с классом обслуживания serviceTimeItem</li>
     * <li>Создаем запрос sc по соглашению agreement</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим на карточку класса обслуживания serviceTimeItem </li>
     * <li>Редактируем serviceTimeItem</li>
     * <li>Утверждаем получившийся черновик как используемую версию</li>
     * <br>
     * <b>Проверки</b>
     * <li>Используемая версия serviceTimeItem отображается в списке используемых версий</li>
     * <li>Uuid отображаемой используемой версии действительно является uuid'ом используемой версии </li>
     * <li>Используемая версия serviceTimeItem не отображается в списке старых версий</li>
     * <li>Старая версия serviceTimeItem отображается в списке старых версий</li>
     * <li>Старая версия serviceTimeItem не отображается в списке используемых версий</li>
     * </ol>
     */
    @Test
    public void testOldAndActualServiceTimeClassPresentation()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        CatalogItem timeZoneItem = SharedFixture.timeZone();
        CatalogItem priorityItem = SharedFixture.priority();
        CatalogItem serviceTimeItem = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(serviceTimeItem);

        CatalogItem newServiceTimeItem = DAOCatalogItem.copy(serviceTimeItem);

        MetaClass scClass = DAOScCase.createClass();
        String targetAttr1 = "priority";
        String targetAttr2 = "resolutionTime";
        String sourceAttr = "metaClass";
        CatalogItem priorityRule = DAOCatalogItem.createRulesSettings(scClass, targetAttr1, sourceAttr);
        CatalogItem resolutionTimeRule = DAOCatalogItem.createRulesSettings(scClass, targetAttr2, sourceAttr);
        DSLCatalogItem.add(priorityRule, resolutionTimeRule);

        RsRow row1 = DAORsRow.create(priorityRule, targetAttr1, priorityItem.getUuid(), sourceAttr, scCase.getFqn());
        RsRow row2 = DAORsRow.create(resolutionTimeRule, targetAttr2, "10 HOUR", sourceAttr, scCase.getFqn());
        DSLRsRows.addRowToRSItem(row1, row2);

        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(agreementCase);
        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, resolutionTimeRule,
                priorityRule);

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(agreement, ou);

        DSLAgreement.addToRecipients(agreement, ou);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);

        //Выполнение действия
        GUILogon.asSuper();
        GUICatalogItem.goToCard(serviceTimeItem);

        //Добавляем период 3.30-5.15
        GUIServiceTime.clickPictogramST("tuesday", "add");
        GUIForm.assertQuestion(ErrorMessages.ERR_ST_ITEM, serviceTimeItem.getTitle(), sc.getTitle());
        GUIForm.confirmByYes();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("3", "30", "5", "15");
        GUIForm.applyForm();

        GUICatalogItem.resolveSTItem();

        newServiceTimeItem.setExists(true);
        newServiceTimeItem.setUuid(
                StringUtils.substringAfter(GUITester.getCurrentUrl(), GUINavigational.URL_POSTFIX_ADMIN + "#ci:"));

        serviceTimeItem.setCode(SdDataUtils
                .getCatalogItemByUUIDAndChangedAttribute(serviceTimeItem, CatalogItem.CODE, serviceTimeItem.getCode())
                .get(CatalogItem.CODE));

        //Проверки
        GUICatalog.clickBackToCatalog();

        GUITester.assertTextPresentWithMsg(GUICatalogItem.X_TITLE_IN_ITEM_LIST, newServiceTimeItem.getTitle(),
                "Название элемента справочника, на карточке справочника, не совпал c ожидаемым",
                newServiceTimeItem.getCode(), newServiceTimeItem.getParentCode());

        GUITester.assertTextPresentWithMsg(GUICatalogItem.X_CODE_IN_ITEM_LIST, newServiceTimeItem.getCode(),
                "Код элемента справочника, на карточке справочника, не совпал c ожидаемым",
                newServiceTimeItem.getCode());

        tester.click(String.format("//a[@id='%s.%s.color']", newServiceTimeItem.getCode(),
                newServiceTimeItem.getParentCode()));
        tester.refresh();

        String uuid = StringUtils.substringAfter(GUITester.getCurrentUrl(), GUINavigational.URL_POSTFIX_ADMIN + "#ci:");
        Assert.assertEquals(String.format(
                "Uuid элемента справочника на карточке не совпал с ожидаемым."
                        + " Полученный uuid элемента справочника %s, ожидаемый uuid %s.",
                uuid, newServiceTimeItem.getUuid()), uuid, newServiceTimeItem.getUuid());

        GUICatalog.clickBackToCatalog();

        Assert.assertTrue("Старая версия справочника отображается в списке старых версий.",
                tester.waitDisappear("//span[@id='%s.code']", serviceTimeItem.getCode()));

        GUICatalog.clickSwitchVersion();

        GUITester.assertTextContainsWithMsg(GUICatalogItem.X_TITLE_IN_ITEM_LIST, serviceTimeItem.getTitle(),
                "Название элемента справочника, на карточке справочника, не совпало c ожидаемым.",
                serviceTimeItem.getCode(), serviceTimeItem.getParentCode());

        GUITester.assertTextPresentWithMsg(GUICatalogItem.X_CODE_IN_ITEM_LIST, serviceTimeItem.getCode(),
                "Код элемента справочника, на карточке справочника, не совпал c ожидаемым", serviceTimeItem.getCode());
    }

    /**
     * В пользовательском атрибуте элемент справочника Классы обслуживания присутствую старые версии NSDPRD-797
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>В справочнике классы обслуживания создать класс обслуживания servicetime</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Создать запрос sc типа scCase</li>
     * <li>Создать атрибут catalogItem у ouCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку servicetime </li>
     * <li>Добавляем новый период для вторника</li>
     * <li>Подтверждаем создание черновика</li>
     * <li>Проверяем, что у catalogItem в значении по умолчанию отсутствуют черновики</li>
     *
     * <li>Утверждаем новый servicetime</li>
     * </ol>
     */
    @Test
    public void testOldServiceTime()
    {
        MetaClass ouCase = DAOOuCase.create();
        MetaClass scCase = DAOScCase.create();
        MetaClass emplCase = DAOEmployeeCase.create();
        MetaClass agreementCase = SharedFixture.agreementCase();
        DSLMetaClass.add(ouCase, scCase, emplCase);

        Catalog serviceTimeCatalog = DAOCatalog.createSystem(SystemCatalog.SERVICETIME);

        CatalogItem timeZoneItem = SharedFixture.timeZone();
        CatalogItem servicetimeItem = DAOCatalogItem.createServiceTime();
        CatalogItem priorityItem = SharedFixture.priority();

        MetaClass scClass = DAOScCase.createClass();
        String targetAttr1 = "priority";
        String targetAttr2 = "resolutionTime";
        String sourceAttr = "metaClass";
        CatalogItem ruleItem1 = DAOCatalogItem.createRulesSettings(scClass, targetAttr1, sourceAttr);
        CatalogItem ruleItem2 = DAOCatalogItem.createRulesSettings(scClass, targetAttr2, sourceAttr);

        DSLCatalogItem.add(servicetimeItem, ruleItem1, ruleItem2);
        RsRow row = DAORsRow.create(ruleItem1, targetAttr1, priorityItem.getUuid(), sourceAttr, scCase.getFqn());
        DSLRsRows.addRowToRSItem(row);
        RsRow row2 = DAORsRow.create(ruleItem2, targetAttr2, "10 HOUR", sourceAttr, scCase.getFqn());
        DSLRsRows.addRowToRSItem(row2);

        Bo agreement = DAOAgreement.createWithRules(agreementCase, servicetimeItem, servicetimeItem, ruleItem2,
                ruleItem1);

        Bo ou = SharedFixture.ou();
        DSLBo.add(agreement);
        Bo employee = DAOEmployee.create(emplCase, ou, true);
        DSLBo.add(employee);

        DSLAgreement.addToRecipients(agreement, employee);

        Bo sc = DAOSc.create(scCase, employee, agreement, timeZoneItem);
        DSLBo.add(sc);

        Attribute attr = DAOAttribute.createCatalogItem(ouCase.getFqn(), serviceTimeCatalog, servicetimeItem);
        DSLAttribute.add(attr);
        //Выполнение действия и проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCard(servicetimeItem);

        GUIServiceTime.clickPictogramST("tuesday", "add");
        GUIForm.assertQuestionAppear("Диалог создания новой версии не появился");
        GUIForm.confirmByYes();

        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("3", "30", "4", "30");
        GUIForm.applyForm();

        //Проверяем дефект
        //Тестовые действия
        GUIAttribute.goToAttribute(attr);
        GUIAttribute.clickEdit(attr);
        GUIForm.assertDialogAppear("Форма редактирования атрибута типа \"Элемент справочника\" не появилась.");
        tester.click(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.InputComplex.DEFAULT_VALUE_VALUE_DIV);
        Assert.assertTrue(
                "NSDPRD-797 в пользовательском атрибуте элемент справочника Классы обслуживания присутствую старые версии",
                tester.waitDisappear(
                        GUIXpath.Div.POPUP_LIST_SELECT + "//div[@id]//span[contains(text(),'Черновик')]"));
        //Утверждаем черновик
        GUICatalogItem.goToCatalog(servicetimeItem);
        GUIForm.cancelForm();
        tester.click("//a[text()='" + servicetimeItem.getTitle() + " (Черновик)']");
        tester.click(GUIXpath.Div.RESOLVE);
        GUIForm.assertQuestionAppear("Диалог утверждения не появился");
        GUIForm.confirmByYes();
        String uuid = StringUtils.substringAfter(GUITester.getCurrentUrl(), GUINavigational.URL_POSTFIX_ADMIN + "#ci:");
        servicetimeItem.setUuid(uuid);
    }

    /**
     * Тестирование сохранения значения по умолчанию у атрибутов типа элемент справочника (Класс обслуживания, Приоритет)
     * после внесения исключения в класс обслуживания и утверждения черновика, если код класса обслуживания и другого
     * элемента справочника совпадают.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * http://sd-jira.naumen.ru/browse/NSDPRD-5941
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase, тип отдела ouCase, тип соглашения agreementCase</li>
     * <li>Создать элементы справочника с одинаковым кодом: serviceTimeItem (Класс обслуживания), priorityItem (Приоритет)</li>
     * <li>Установить значение по умолчанию priorityItem атрибуту priority в типе scCase</li>
     * <li>Создать соглашение agreement типа agreementCase (класс обслуживания serviceTimeItem)</li>
     * <li>Создать отдел ou типа ouCase и сделать его получателем соглашения agreement</li>
     * <li>Создать запрос sc типа scCase (указать контрагентом ou)</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на страницу метакласса scCase</li>
     * <li>Проверить, что значением по умолчанию у атрибута priority является priorityItem</li>
     * <li>Перейти на карточку класса обслуживания serviceTimeItem</li>
     * <li>Добавить исключение в класс обслуживания serviceTimeItem и утвердить черновик</li>
     * <li>Перейти на страницу метакласса scCase</li>
     * <li>Проверить, что значением по умолчанию у атрибута priority является priorityItem</li>
     * </ol>
     */
    @Test
    public void testOverwriteDefaultValueWithSameCode()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass agreementCase = SharedFixture.agreementCase();
        MetaClass ouCase = SharedFixture.ouCase();
        DSLMetaClass.add(scCase);

        String itemCode = ModelUtils.createCode();

        CatalogItem timeZoneItem = SharedFixture.timeZone();
        CatalogItem serviceTimeItem = DAOCatalogItem.createServiceTime();
        serviceTimeItem.setCode(itemCode);
        CatalogItem priorityItem = DAOCatalogItem.createPriority(22);
        priorityItem.setCode(itemCode);
        DSLCatalogItem.add(priorityItem, serviceTimeItem);

        Attribute attrPriority = SysAttribute.priority(scCase);
        attrPriority.setDefaultValue(priorityItem.getUuid());
        Attribute resolutionTimeAttr = SysAttribute.resolutionTime(scCase);
        resolutionTimeAttr.setDefaultValue("2 HOUR");
        DSLAttribute.edit(attrPriority, resolutionTimeAttr);

        Bo agreement = DAOAgreement.create(agreementCase, serviceTimeItem, serviceTimeItem);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(agreement, ou);
        DSLAgreement.addToRecipients(agreement, ou);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToCard(scCase);
        GUIAttribute.assertCatalogItemDefaultValue(attrPriority, priorityItem);
        GUICatalogItem.goToCard(serviceTimeItem);
        tester.click(GUIXpath.Other.TOOL_BAR + GUIXpath.Div.ADD);
        GUIForm.confirmByYes();
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.DEBUG_VALUE + "//input",
                DateTimeUtils.getRandomDateTimeddMMyyyy());
        GUIForm.applyForm();
        GUICatalogItem.resolveSTItem();
        GUIMetaClass.goToCard(scCase);
        GUIAttribute.assertCatalogItemDefaultValue(attrPriority, priorityItem);
    }

    /**
     * Тестирование попытки копирования элемента справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника A.</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника </li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму копировать</li>
     * <li>На форме копирования элемента: вводим новое название, код соотвтествующие элементу справочника А</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появилось сообщение о невозможности копирования. Форма не закрылась</li>
     * </ol>
     */
    @Test
    public void testTryCopyCatalogItem()
    {
        //Подготовка
        CatalogItem itemA = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(itemA);
        CatalogItem itemB = DAOCatalogItem.createServiceTime();
        itemB.setCode(itemA.getCode());
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(itemA);
        GUICatalog.clickPictogram(itemA, "copy");
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, itemB.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, itemB.getCode());
        String message = "Значение атрибута 'Код элемента справочника': Элемент справочника '" + "Классы обслуживания"
                + "' с кодом '" + itemB.getCode() + "' уже существует!";
        GUIForm.applyFormAssertError(message);
        GUIForm.cancelForm();
    }

    /**
     * Тестирование обновления новой версии класса обслуживания в таблице соответствий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00317
     * http://sd-jira.naumen.ru/browse/NSDPRD-2367
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создаем класс обслуживания serviceTime</li>
     * <li>Создаем таблицу соответствий ServiceTable для класса Запрос: определяемые - приоритет и нормативное время, определяющий - класс обслуживания.</li>
     * <li>Добавляем строку в эту таблицу</li>
     * <li>Создаём соглашение agreement и устнавливаем для него правила вычисления приоритета и правила вычисления нормативного времени - serviceTimeTable,
     * время предоставления услуги и время поддержки - serviceTime</li>
     * <li>Создаём отдел ou и связываем его с соглашением agreement</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Захлодим под тестером</li>
     * <li>переходим на карточку отдела ou</li>
     * <li>Добавляем запрос. Приоритет и нормативное время определяются по созданной таблице соответствий</li>
     * <li>Проверяем что запрос зарегестировался</li>
     * <li>Заходим в класс обслуживания, редактируем: добавляем период обслуживания для вторника с 3-30 до 5-15 </li>
     * <li>Утверждаем изменения</li>
     * <li>регистрируем запрос повторно с теми же параметрами.</li>
     * <li>Проверяем что запрос зарегестировался</li>
     * </ol>
     */
    @Test
    public void testUpdateValueMapCatalogItem()
    {
        MetaClass ouCase = DAOOuCase.create();
        MetaClass scCase = DAOScCase.create();
        MetaClass scClass = DAOScCase.createClass();
        MetaClass agreementCase = SharedFixture.agreementCase();
        DSLMetaClass.add(ouCase, scCase);

        CatalogItem serviceTime = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(serviceTime);

        List<MetaClass> metaclasses = Lists.newArrayList(DAOScCase.createClass());

        List<String> targetAttrs = Lists.newArrayList("priority", "resolutionTime");
        List<String> sourceAttrs = Lists.newArrayList("serviceTime");

        CatalogItem serviceTable = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);
        DSLCatalogItem.add(serviceTable);

        RsRow row = DAORsRow.create(serviceTable,
                Lists.newArrayList(SysAttribute.priority(scClass).getCode(), SharedFixture.priority().getUuid(),
                        SysAttribute.resolutionTime(scClass).getCode(), "1 SECOND"),
                Lists.newArrayList("serviceTime", serviceTime.getUuid()));

        DSLRsRows.addRowToRSItem(row);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        Bo agreement = DAOAgreement.create(agreementCase);
        agreement.setAgrPriorityRuleUuid(serviceTable.getUuid());
        agreement.setAgrPriorityRuleUuid(serviceTable.getUuid());
        agreement.setAgrResolutionTimeRuleUuid(serviceTable.getUuid());
        agreement.setAgrSupportHoursUuid(serviceTime.getUuid());
        agreement.setAgrSupportHoursTitle(serviceTime.getTitle());
        agreement.setAgrServiceHoursUuid(serviceTime.getUuid());
        agreement.setAgrServiceHoursTitle(serviceTime.getTitle());
        DSLBo.add(agreement);

        DSLAgreement.addToRecipients(agreement, ou);

        Bo sc = DAOSc.create(scCase, ou, agreement, SharedFixture.timeZone());

        //действия и проверки
        GUILogon.asTester();

        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();

        GUIBo.fillScMainFields(sc);
        GUISelect.selectById(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);

        GUILogon.logout();
        GUILogon.asSuper();

        GUICatalogItem.goToCard(serviceTime);
        GUIServiceTime.clickPictogramST("tuesday", "add");
        GUIForm.confirmByYes();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm("3", "30", "5", "15");
        GUIForm.applyForm();
        GUICatalogItem.resolveSTItem();

        GUILogon.logout();
        GUILogon.asTester();

        Bo sc2 = DAOSc.create(scCase, ou, agreement, SharedFixture.timeZone());
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();

        GUIBo.fillScMainFields(sc2);
        GUISelect.selectById(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc2);
    }
}
