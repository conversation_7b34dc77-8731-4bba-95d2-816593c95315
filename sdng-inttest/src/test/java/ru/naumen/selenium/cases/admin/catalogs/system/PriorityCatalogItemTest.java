package ru.naumen.selenium.cases.admin.catalogs.system;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.file.GUIFileAdmin;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.util.StringUtils;

/**
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00321
 * Тестирование элементов системного справочника Приоритеты
 * <AUTHOR>
 * @since 12.01.2012
 *
 */

public class PriorityCatalogItemTest extends AbstractTestCase
{
    /**
     * Тестирование возможности перейти на карточку элемента справочника с карточки справочника при условия что для 
     * элемента справочника установлено изображение
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00145
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника приоритеты item</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника приоритеты</li>
     * <li>В блоке Элементы справочника напротив элемента item нажимаем пиктограмму редактировать</li>
     * <li>На форме редактирования элемента загружаем для него изображение из файла</li>
     * <li>Нажимаем Сохранить</li>
     * <li>Кликаем по ссылке с названием элемента справочнкиа item</li>
     * <br>
     * <b>Проверка</b>
     * <li>Совершился переход на карточку элемента справочника item</li>
     * </ol>
     */
    @Test
    public void testAbilityOfPassingToCatalogItemWithImage()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createPriority(1);
        DSLCatalogItem.add(item);

        //выполнение действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(item);
        GUICatalog.clickPictogram(item, "edit");
        GUIForm.assertDialogAppear("Форма добавления элемента справочника \"Приоритеты\" не появилась.");
        GUIFileAdmin.uploadFile(GUIXpath.PropertyDialogBoxContent.ICON_VALUE, DSLFile.IMG_FOR_UPLOAD);
        GUIForm.applyModalForm();

        tester.click(String.format(GUICatalogItem.X_TITLE_IN_ITEM_LIST_WITH_ICO, item.getCode(), item.getParentCode()));

        //Проверка
        Assert.assertEquals("Не удалость корректно перейти по ссылке.",
                StringUtils.substringAfter(GUITester.getCurrentUrl(), GUINavigational.URL_POSTFIX_ADMIN + "#ci:"),
                item.getUuid());
    }

    /**
     * Тестирование добавления элемента справочника Приоритеты
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00321
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника Приоритеты</li>
     * <li>В блоке Элементы справочника нажимаем кнопку Добавить</li>
     * <li>На форме добавления атрибута заполнить поля: название, код, уровень</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Приоритеты, блок Элементы справочника: название(ссылка на карточку элемента), уровень и код элемента</li>
     * <li>Карточка элемента: название, уровень и код.  </li>
     * </ol>
     */
    @Test
    public void testAddSimplePriorityCatalogItem()
    {
        //Выполнение действия
        CatalogItem item = DAOCatalogItem.createPriority(1);
        GUILogon.asSuper();
        GUICatalogItem.callAddForm(item);
        GUIForm.assertDialogAppear("Форма добавления элемента справочника \"Приоритеты\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, item.getCode());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.LEVEL_VALUE, item.getLevel());
        GUIForm.applyModalForm();
        //Проверки
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование копирования элемента справочника Приоритеты
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00321
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент Приоритеты A.</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника Приоритеты</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму копировать</li>
     * <li>На форме копирования элемента: вводим новое название, код, уровень соотвтествующие элементу справочника B.</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма копирования закрылась</li>
     * <li>Карточка справочника Приоритеты, блок Элементы справочника: присутствует элемент А и элемент В. </li>
     * <li>Карточка элемента B, присутствуют название,  код, уровень</li>
     * </ol>
     */
    @Test
    public void testCopyPriorityCatalogItem()
    {
        //Подготовка
        CatalogItem itemA = DAOCatalogItem.createPriority(2);
        DSLCatalogItem.add(itemA);
        CatalogItem itemB = DAOCatalogItem.createPriority(3);
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(itemA);
        GUICatalog.clickPictogram(itemA, "copy");
        GUIForm.assertDialogAppear("Форма копирования элемента справочника \"Приоритеты\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, itemB.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, itemB.getCode());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.LEVEL_VALUE, itemB.getLevel());
        GUIForm.applyModalForm();
        //Проверки
        GUICatalogItem.assertPresent(itemB);
    }

    /**
     * Тестирование удаления элемента справочника Приоритеты
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00321
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника Приоритеты. А</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника Приоритеты</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму Удалить</li>
     * <li>Подтверждаем удаление</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Приоритеты, блок Элементы справочника: элемент А отсутствует</li>
     * </ol>
     */
    @Test
    public void testDeletePriorityCatalogItem()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createPriority(3);
        DSLCatalogItem.add(item);
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(item);
        GUICatalog.clickPictogramRemoveAndConfirm(item);

        //Проверки
        GUICatalogItem.assertAbsence(item);
    }

    /**
     * Тестирование двойного редактирования элемента справочника Приоритеты
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00321
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника Приоритеты А</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника Приоритеты</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму редактировать</li>
     * <li>На форме редактирования элемента проверяем, что код не редактируется.</li>
     * <li>На форме редактирования элемента: меняем название, уровень</li>
     * <li>Нажать Сохранить</li>
     * <li>Дождавшись закрытия формы редактирования и не производя переходов с текущей страницы, повторно нажать пиктограмму редактировать.</li>
     * <li>Нажать Сохранить</li>
     * <li>Заходим в карточку элемента А.</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Приоритеты: название, уровень и код</li>
     * <li>Карточка элемента: название, уровень и код.</li>
     * </ol>
     */
    @Test
    public void testDoubleEditPriorityCatalogItem()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createPriority(2);
        DSLCatalogItem.add(item);
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(item);
        item.setTitle(ModelUtils.createTitle());
        item.setLevel(String.valueOf(0));
        GUICatalog.clickPictogram(item, "edit");
        GUIForm.assertDialogAppear("Форма редактирования элемента справочника \"Приоритеты\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.LEVEL_VALUE, item.getLevel());
        GUIForm.applyModalForm();
        GUICatalog.clickPictogram(item, "edit");
        GUIForm.assertDialogAppear("Форма редактирования элемента справочника \"Приоритеты\" не появилась.");
        GUIForm.applyModalForm();
        //Проверки
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование редактирования элемента справочника Приоритеты
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00321
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника Приоритеты А</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника Приоритеты</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму редактировать</li>
     * <li>На форме редактирования элемента проверяем, что код не редактируется.</li>
     * <li>На форме редактирования элемента: меняем название, уровень</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Приоритеты: название, уровень и код</li>
     * <li>Карточка элемента: название, уровень и код.</li>
     * </ol>
     */
    @Test
    public void testEditPriorityCatalogItem()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createPriority(1);
        DSLCatalogItem.add(item);
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(item);
        item.setTitle(ModelUtils.createTitle());
        item.setLevel(String.valueOf(3));
        GUICatalog.clickPictogram(item, "edit");
        GUIForm.assertDialogAppear("Форма редактирования элемента справочника \"Приоритеты\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.LEVEL_VALUE, item.getLevel());
        String actual = tester.getText(GUIXpath.PropertyDialogBoxContent.PROPERTY_DIALOG_BOX_CODE_CAPTION + "/span").trim();
        String message = String
                .format("Код элемента справочника \"Приоритеты\" на форме редактирования не совпал с ожидаемым. Ожидаемый код:%s; полученный код:%s",
                        item.getCode(), actual);
        Assert.assertEquals(message, item.getCode(), actual);
        GUIForm.applyModalForm();
        //Проверки
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование попытки копирования элемента справочника 
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00077
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника A.</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника </li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму копировать</li>
     * <li>На форме копирования элемента: вводим новое название, код соотвтествующие элементу справочника А</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появилось сообщение о невозможности копирования. Форма не закрылась</li>
     * </ol>
     */
    @Test
    public void testTryCopyCatalogItem()
    {
        //Подготовка
        CatalogItem itemA = DAOCatalogItem.createPriority(1);
        DSLCatalogItem.add(itemA);
        CatalogItem itemB = DAOCatalogItem.createPriority(1);
        itemB.setCode(itemA.getCode());
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(itemA);
        GUICatalog.clickPictogram(itemA, "copy");
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, itemB.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, itemB.getCode());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.LEVEL_VALUE, itemB.getLevel());
        String message = "Значение атрибута 'Код элемента справочника': Элемент справочника 'Приоритеты' с кодом '"
                + itemB.getCode() + "' уже существует!";
        GUIForm.applyFormAssertError(message);

        GUIForm.cancelForm();
    }
}
