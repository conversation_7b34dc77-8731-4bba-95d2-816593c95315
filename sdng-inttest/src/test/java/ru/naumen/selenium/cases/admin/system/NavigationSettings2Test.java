package ru.naumen.selenium.cases.admin.system;

import static ru.naumen.selenium.casesutil.metaclass.GUIEventAction.ACTION_VALUE;

import java.io.File;
import java.nio.file.Paths;
import java.util.List;
import java.util.Set;

import org.junit.Assert;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIPush;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLBreadCrumb;
import ru.naumen.selenium.casesutil.admin.DSLLeftMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.admin.GUIMenuItem;
import ru.naumen.selenium.casesutil.admin.GUINavSettings;
import ru.naumen.selenium.casesutil.admin.interfaze.GUIInterface;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.interfaceelement.MetaTree;
import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.Crumb;
import ru.naumen.selenium.casesutil.model.admin.DAOBreadCrumb;
import ru.naumen.selenium.casesutil.model.admin.DAOLeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem.LinkToContentLinkObjectTypes;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem.MenuItemType;
import ru.naumen.selenium.casesutil.model.admin.MenuItem.MenuItemTypeOfCard;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentType;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.DAOStructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsView;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.structuredobjectsview.DSLStructuredObjectsView;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.StringUtils;

/**
 * Тестирование настроек навигации в интерфейсе технолога
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
 * Тестирование настройки возможности создания объектов через общую кнопку добавить
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00513
 * <AUTHOR>
 * @since 16.04.2013
 */
public class NavigationSettings2Test extends AbstractTestCase
{
    /**
     * Тестирование редактирования элемента меню из его карточки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент меню chapter типа Раздел</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку chapter</li>
     * <li>Нажать кнопку Редактировать, изменить название и нажать кнопку Сохранить</li>
     * <br>
     * <b>Проверка</b>
     * <li>Название элемента меню изменилось</li>
     * </ol>
     */
    @Test
    public void testEditMenuItemFromCard()
    {
        //Подготовка
        MenuItem chapter = DAOMenuItem.createChapter(false);
        DSLMenuItem.add(chapter);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMenuItem.goToTopMenuItemCard(chapter);

        //TODO если во время заполнения поля Название убрать фокус, то элемент создается без названия

        tester.click(GUIXpath.Div.EDIT);
        chapter.setTitle(ModelUtils.createTitle());
        GUIMenuItem.fillElementTitleField(chapter.getTitle());
        GUIForm.applyForm();

        //Проверки
        String msg = "Название карточки не совпало с ожидаемым";
        String actualCardTitle = tester.getText(Div.HEADER_TITLE);
        Assert.assertTrue(msg, actualCardTitle.contains(chapter.getTitle()));

        String actualBlockTitle = tester.getText(GUIMenuItem.MENU_ITEM_CARD_ROWS_VALUE_PATTERN, "title");
        Assert.assertEquals(msg, chapter.getTitle(), actualBlockTitle);
    }

    /**
     * Тестирование включения/выключения чек-боксов в блоке "Видимость"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Действие:</b>
     * <li>в А. перейти в раздел "Интерфейс и навигация", на вкладку "Навигация"</li>
     * <br>
     * <b>Проверка:</b>
     * <li>В блоке "Видимость" свойство "Показывать верхнее меню" выключено</li>
     * <li>Свойство "Показывать левое меню" включено</li>
     * <br>
     * <b>Действие:</b>
     * <li>Редактируем блок:<ul>
     * <li>Включаем свойство "Показывать верхнее меню"</li>
     * <li>Выключаем свойство "Показывать левое меню"</li>
     * </ul></li>
     * <br>
     * <b>Проверка:</b>
     * <li>Свойство "Показывать верхнее меню" включено</li>
     * <li>Свойство "Показывать левое меню" выключено</li>
     * <br>
     * <b>Действие:</b>
     * <li>Редактируем блок:<ul>
     * <li>Выключаем свойство "Показывать верхнее меню"</li>
     * <li>Включаем свойство "Показывать левое меню"</li>
     * </ul></li>
     * <br>
     * <b>Проверка:</b>
     * <li>Свойство "Показывать верхнее меню" выключено</li>
     * <li>Свойство "Показывать левое меню" включено</li>
     * </ol>
     */
    @Test
    public void testEditVisibilityNavSettings()
    {
        // Настройка
        DSLNavSettings.editVisibilitySettings(false, true);
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUINavSettings.assertNavSettingsInfoBlock(false, true);

        //Тестовые действия и проверки
        GUINavSettings.clickOpenEditNavSettings();
        GUINavSettings.setFormNavSettingsValues(true, false);
        GUIForm.applyModalForm();
        GUINavSettings.assertNavSettingsInfoBlock(true, false);

        GUINavSettings.clickOpenEditNavSettings();
        GUINavSettings.setFormNavSettingsValues(false, true);
        GUIForm.applyModalForm();
        GUINavSettings.assertNavSettingsInfoBlock(false, true);
    }

    /**
     * Проверка сброса значения в поле "Вкладка контента" при смене Вкладки карточки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>На карточку сотрудника типа emplCase1 добавляем контент "панель вкладок" "1" и размещаем на первой вкладке  контент комментарии</li>
     * <li> На карточку сотрудника типа emplCase2 добавляем контент "панель вкладок" "2" и размещаем на первой вкладке  контент комментарии</li>
     * <li>Создаем и включаем элемент element типа "Ссылка на карточку".Вкладка карточки : emplCase1 / 1.Вкладка контента : 1/1</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>В поле Вкладка карточки меняем на Вкладка карточки : emplCase2 / 2</li>
     * <br>
     * <b>Проверки</b>
     * <li>В поле Вкладка контента значение сбросилось на [не указано]</li>
     * </ol>
     */
    @Test
    public void testEmptyTabPanelAfterChangeMetaclassTab()
    {
        // Подготовка
        DSLNavSettings.editVisibilitySettings(true, true);

        MetaClass emplCase1 = DAOEmployeeCase.create();
        MetaClass emplCase2 = DAOEmployeeCase.create();
        DSLMetaClass.add(emplCase1, emplCase2);

        //Вкладка метакласса
        ContentTab metaClassTab1 = DAOContentTab.createTab(emplCase1.getFqn());
        ContentTab metaClassTab2 = DAOContentTab.createTab(emplCase2.getFqn());
        DSLContent.addTab(metaClassTab1, metaClassTab2);

        //Контенты
        ContentForm tabPanel1 = DAOContentCard.createTabBar(emplCase1.getFqn());
        ContentForm tabPanel2 = DAOContentCard.createTabBar(emplCase2.getFqn());
        DSLContent.add(metaClassTab1, tabPanel1);
        DSLContent.add(metaClassTab2, tabPanel2);

        //Вкладки контентов
        ContentTab firstTabPanel1 = DSLContent.getFirstTab(tabPanel1);
        ContentForm comment = DAOContentCard.createCommentList(emplCase1.getFqn());
        DSLContent.add(firstTabPanel1, comment);

        ContentTab firstTabPanel2 = DSLContent.getFirstTab(tabPanel2);
        comment = DAOContentCard.createCommentList(emplCase2.getFqn());
        DSLContent.add(firstTabPanel2, comment);

        MenuItem reference1 = DAOMenuItem.createReference(emplCase1, true, metaClassTab1, tabPanel1, firstTabPanel1);
        DSLMenuItem.add(reference1);
        //Выполнение действий
        GUILogon.asSuper();
        // Действие
        GUINavSettings.goToCard();
        GUIMenuItem.clickEditTopMenuItem(reference1);
        GUIMenuItem.selectCardTab(metaClassTab2);

        // Проверка
        GUITester.assertValue(GUIMenuItem.MENU_ITEM_CONTENT_TAB_INPUT, GUISelect.EMPTY_VALUE);
    }

    /**
     * Тестирование включения и выключения элемента меню из его карточки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент меню chapter типа Раздел</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку chapter</li>
     * <li>Нажать кнопку Выключить</li>
     * <br>
     * <b>Проверка</b>
     * <li>В строке Включено блока Атрибуты элемента появилась галочка</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Нажать кнопку Выключить</li>
     * <br>
     * <b>Проверка</b>
     * <li>В строке Включено блока Атрибуты элемента исчезла галочка</li>
     * </ol>
     */
    @Test
    public void testEnableDisableMenuItemFromCard()
    {
        //Подготовка
        MenuItem chapter = DAOMenuItem.createChapter(false);
        DSLMenuItem.add(chapter);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMenuItem.goToTopMenuItemCard(chapter);
        tester.click(GUIXpath.Div.SWITCH);

        //Проверка
        GUIMenuItem.assertMenuItemEnabled(chapter.setEnabled("true"), true);

        //Выполнение действия
        tester.click(GUIXpath.Div.SWITCH);

        //Проверка
        GUIMenuItem.assertMenuItemEnabled(chapter.setEnabled("false"), true);
    }

    /**
     * Тестирование кнопок управления элементом: кнопка "Включить/Выключить".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать (в этом же порядке, все элементы выключены):<ul>
     * <li>Раздел меню "charter1"<ul>
     * <li>История "history"</li>
     * <li>Ссылка на карточку "reference1"</li>
     * <li>Ссылка на карточку "reference2"</li>
     * </ul></li>
     * <li>Раздел меню "charter2"<ul>
     * <li>Избранное "favorites"</li>
     * </ul></li>
     * </ul></li>
     * <li>Включить элементы charter1, reference1, history</li>
     * <br>
     * <b>Действие:</b>
     * <li>Выключить родительский элемент Раздел меню "charter1"</li>
     * <br>
     * <b>Проверка:</b>
     * <li>Выключился родительский элемент Раздел меню "charter1" и его потомки История "history",
     * Ссылка на карточку "reference1"</li>
     * <li>Элемент "reference2" остался выключенным</li>
     * <br>
     * <b>Действие:</b>
     * <li>Включить дочерний элемент Избранное "favorites"</li>
     * <br>
     * <b>Проверка:</b>
     * <li>Включился дочерний элемент Избранное "favorites" и его родитель Раздел меню "charter2"</li>
     * </ol>
     */
    @Test
    public void testEnableDisableMenuItemFromNavigationCard()
    {
        //Подготовка
        MenuItem chapter1 = DAOMenuItem.createChapter(true);
        MenuItem history = DAOMenuItem.createHistory(chapter1, true);
        MenuItem reference1 = DAOMenuItem.createReference(chapter1, DAORootClass.create(), true);
        MenuItem reference2 = DAOMenuItem.createReference(chapter1, DAOOuCase.createClass(), false);
        MenuItem chapter2 = DAOMenuItem.createChapter(false);
        MenuItem favorites = DAOMenuItem.createFavorites(chapter2, false);
        DSLMenuItem.add(chapter1, history, reference1, reference2, chapter2, favorites);

        //Действие
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUINavSettings.clickOffMenuItem(chapter1);

        //Проверка
        GUINavSettings.assertMenuItemEnabled(chapter1.setEnabled("false"));
        GUINavSettings.assertMenuItemEnabled(history.setEnabled("false"));
        GUINavSettings.assertMenuItemEnabled(reference1.setEnabled("false"));
        GUINavSettings.assertMenuItemEnabled(reference2.setEnabled("false"));

        //Действие
        GUINavSettings.clickOnMenuItem(favorites);

        //Проверка
        GUINavSettings.assertMenuItemEnabled(favorites.setEnabled("true"));
        GUINavSettings.assertMenuItemEnabled(chapter2.setEnabled("true"));
    }

    /**
     * Тестирование иконки сворачивания и разворачивания блоке "Атрибуты элемента" карточки элемента меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент меню chapter типа Раздел</li>
     * <br>
     * <b>Выполнение действия и проверка</b>
     * <li>Перейти на карточку chapter</li>
     * <li>В блоке "Атрибуты элемента" видна строка с названием элемента</li>
     * <br>
     * <b>Выполнение действия и проверка</b>
     * <li>Нажать на иконку сворачивания блока "Атрибуты элемента"</li>
     * <li>В блоке "Атрибуты элемента" не видна строка с названием элемента</li>
     * <br>
     * <b>Выполнение действия и проверка</b>
     * <li>Нажать на иконку сворачивания блока "Атрибуты элемента"</li>
     * <li>В блоке "Атрибуты элемента" видна строка с названием элемента</li>
     * </ol>
     */
    @Test
    public void testHideAttributesSection()
    {
        //Подготовка
        MenuItem chapter = DAOMenuItem.createChapter(false);
        DSLMenuItem.add(chapter);

        //Выполнение действия и проверка
        GUILogon.asSuper();
        GUIMenuItem.goToTopMenuItemCard(chapter);
        GUITester.assertPresent(String.format(GUIMenuItem.MENU_ITEM_CARD_ROWS_VALUE_PATTERN, "title"), "Не "
                + "отображается строка с названием элемента");

        //Выполнение действия и проверка
        tester.click(GUIXpath.Div.ANY_FIRST_BLOCK_TITLE);
        GUITester.assertAbsent(String.format(GUIMenuItem.MENU_ITEM_CARD_ROWS_VALUE_PATTERN, "title"), "Отображается"
                + " строка с названием элемента");

        //Выполнение действия и проверка
        tester.click(GUIXpath.Div.ANY_FIRST_BLOCK_TITLE);
        GUITester.assertPresent(String.format(GUIMenuItem.MENU_ITEM_CARD_ROWS_VALUE_PATTERN, "title"), "Не "
                + "отображается строка с названием элемента");
    }

    /**
     * Тестирование настроек навигации при частичной загрузке метаинформации, если они отсутствовали в загружаемом файле
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * http://sd-jira.naumen.ru/browse/NSDPRD-3603
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>Создать тип ouCase класса Отдел</li>
     * <li>В настройке "Интерфейс и навигация", на вкладке Нафигация установить настройки видимости
     * Показывать верхнее меню = true,
     * Показывать левое меню = false,
     * Показывать "хлебные крошки" = true </li>
     * <li>В настройке "Интерфейс и навигация", на вкладке Навигация добавить элемент Верхнего меню menuItem = кнопка добавления объектов типа ouCase</li>
     * <li>В настройке "Интерфейс и навигация", на вкладке Навигация добавить элемент Хлебных крошек crumbItem = метакласс ouCase, атрибут Автор</li>
     * <li>Загрузить файл метаинформации forNavigationTest.xml</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Входим в систему под администратором</li>
     * <li>Переходим в настройку интерфейса и навигации</li>
     * <li>Переходим на вкладку "Навигация"</li>
     * <li>Проверяем, что в блоке "Видимость"
     * Показывать верхнее меню = true,
     * Показывать левое меню = false,
     * Показывать "хлебные крошки" = true</li>
     * <li>Проверяем, что в блоке "Верхнее меню" присутствует элемент menuItem</li>
     * <li>Проверяем, что в блоке "Хлебные крошки" присутствует элемент crumbItem</li>
     * </ol>
     */
    @Test
    public void testNavigationSettingWhenMetainfoUpload()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();

        DSLNavSettings.editVisibilitySettings(true, false, true);

        MenuItem menuItem = DAOMenuItem.createAddButton(true, ouCase);
        DSLMenuItem.add(menuItem);

        Crumb crumbItem = DAOBreadCrumb.create(ouCase, SysAttribute.author(ouCase));
        crumbItem.setTitle("Отдел: " + ouCase.getTitle());
        DSLBreadCrumb.add(crumbItem);
        File file = Paths.get(DSLAdmin.FOLDER_METAINFO_PATH, DSLAdmin.METAINFO_FOR_NAVIGATION_TEST).toFile();

        DSLMetainfoTransfer.importMetainfo(file);

        //Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToInterfaceSettings();
        tester.click(GUIInterface.X_NAVIGATION);
        GUINavSettings.assertNavSettingsInfoBlock(true, false);
        GUINavSettings.assertBreadCrumbsCheck(true);
        GUINavSettings.assertMenuItemPresent(menuItem);
        GUINavSettings.assertCrumbTitleInList(crumbItem);
    }

    /**
     * Проверка содержимого вкладки "Навигация"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Выполнение действия</b>
     * <li>Заходим в интерфейс технолога, переходим на страницу "Интерфейс и навигация",
     * в ней на вкладку "Навигация"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем наличие блоков: "Панели навигации" (с кнопкой "Редактировать")
     * и "Верхнее навигационное меню" (с кнопкой "Добавить элемент").</li>
     * </ol>
     */
    @Test
    public void testNavigationTabHasContent()
    {
        GUILogon.asSuper();
        GUINavSettings.goToCard();

        GUINavSettings.assertSettingsInfoBlockPresent("Видимость");
        GUINavSettings.assertTopMenuBlockPresent("Верхнее меню");
        GUINavSettings.assertBreadCrumbsBlockPresent("Навигационная цепочка (\"Хлебные крошки\")");
    }

    /**
     * Проверка отсутствия возможности сделать вложение в себя
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать элемент меню chapter типа Раздел</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на страницу настроек навигации</li>
     * <li>Нажать на кнопку Редактировать элемент у chapter</li>
     * <br>
     * <b>Проверка </b>
     * <li>В списке Вложен в раздел отсутствует chapter</li>
     * </ol>
     */
    @Test
    public void testNoRecursiveEmbedding()
    {
        //Подготовка
        MenuItem chapter = DAOMenuItem.createChapter(true);
        DSLMenuItem.add(chapter);

        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUINavSettings.clickEditMenuItem(chapter);

        //Проверка
        GUISelect.assertNotDisplayed(GUIMenuItem.MENU_ITEM_PARENT_INPUT, chapter.getCode(), chapter.getTitle());
    }

    /**
     * Проверка обязательности полей при заполнении формы добавления элемента верхнего меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Выполнение действия</b>
     * <li>Заходим в интерфейс технолога, переходим на страницу "Интерфейс и навигация",
     * в ней на вкладку "Навигация"</li>
     * <li>Нажимаем кнопку Добавить элемент</li>
     * <br>
     * <b>Выполнение действий и проверок 1</b>
     * <li>Выбираем вид элемента - Ссылка на карточку и нажимаем кнопку Сохранить</li>
     * <li>Проявилось предупреждение: Поле должно быть заполнено.</li>
     * <li>Форма не закрылась</li>
     * <br>
     * <b>Выполнение действий и проверок 2</b>
     * <li>Выбираем вид элемента - Раздел меню и нажимаем кнопку Сохранить</li>
     * <li>Проявилось предупреждение: Поле должно быть заполнено.</li>
     * <li>Форма не закрылась</li>
     * <br>
     * <b>Выполнение действий и проверок 3</b>
     * <li>Выбираем вид элемента - История и нажимаем кнопку Сохранить</li>
     * <li>Проявилось предупреждение: Поле должно быть заполнено.</li>
     * <li>Форма не закрылась</li>
     * <br>
     * <b>Выполнение действий и проверок 4</b>
     * <li>Выбираем вид элемента - Избранное и нажимаем кнопку Сохранить</li>
     * <li>Проявилось предупреждение: Поле должно быть заполнено.</li>
     * <li>Форма не закрылась</li>
     * <br>
     * <b>Выполнение действий и проверок 5</b>
     * <li>Выбираем вид элемента - Кнопка добавления объектов, содержимое - Запрос и нажимаем кнопку Сохранить</li>
     * <li>Появилось предупреждение: Поле должно быть заполнено.</li>
     * <li>Форма не закрылась</li>
     * <li>Заполняем поле Название и Снимаем галочку с Запрос,  и нажимаем кнопку Сохранить</li>
     * <li>Появилось предупреждение: Должно быть выбрано хотя бы одно значение.</li>
     * <li>Форма не закрылась</li>
     * </ol>
     */
    @Test
    public void testNotSetReqElementField()
    {
        //Тестовые действия
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUINavSettings.clickAddTopMenuElement();

        //Выполнение действий и проверок 1
        GUIMenuItem.selectMenuItemType(MenuItemType.reference);
        GUIForm.applyFormAssertValidation(GUIMenuItem.MENU_ITEM_TITLE_INPUT);

        //Выполнение действий и проверок 2
        GUIMenuItem.selectMenuItemType(MenuItemType.chapter);
        GUIForm.applyFormAssertValidation(GUIMenuItem.MENU_ITEM_TITLE_INPUT);

        //Выполнение действий и проверок 3
        GUIMenuItem.selectMenuItemType(MenuItemType.history);
        GUIForm.applyFormAssertValidation(GUIMenuItem.MENU_ITEM_TITLE_INPUT);

        //Выполнение действий и проверок 4
        GUIMenuItem.selectMenuItemType(MenuItemType.favorites);
        GUIForm.applyFormAssertValidation(GUIMenuItem.MENU_ITEM_TITLE_INPUT);

        //Выполнение действий и проверок 5
        GUIMenuItem.selectMenuItemType(MenuItemType.addButton);
        MetaTree mcTree = new MetaTree(GUIMenuItem.MENU_ITEM_VALUE);
        mcTree.setElementInSelectTree(DAOScCase.createClass().getCode());
        GUIForm.applyFormAssertValidation(GUIMenuItem.MENU_ITEM_TITLE_INPUT);
        GUIMenuItem.fillElementTitleField(ModelUtils.createTitle());
        mcTree.unsetElementInMultiSelectTree(DAOScCase.createClass().getCode());
        GUIForm.applyFormAssertValidation(GUIMenuItem.MENU_ITEM_VALUE_INPUT,
                ConfirmMessages.VALIDATION_REQUIRED_MULTISELECT_FIELD);
    }

    /**
     * Тестирование невозможности удалить тип сотрудника, если он используется в параметрах элемента меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00237
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$121826583
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать структуру structure</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на страницу настроек навигации</li>
     * <li>Создать элемент левого меню linkToContent (Вид элемента — Ссылка на контент,
     * Тип контента — Иерархическое дерево, Структура — structure, Шаблон — [добавить новый],
     * Объект связи — объект, связанный с текущим пользователем, Атрибут, ссылающийся на объект — Отдел,
     * Тип Сотрудника — employeeCase)</li>
     * <li>Перейти на карточку типа employeeCase</li>
     * <li>Нажать на кнопку «Удалить» и подтвердить удаление</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появилось сообщение об ошибке с текстом:<br>
     * Тип employeeCase не может быть удален.<br>
     * Класс/тип employeeCase используется в настройках левого меню: linkToContent</li>
     * <li>Тип employeeCase не удален</li>
     * </ol>
     */
    @Test
    public void testPreventDeleteMetaClassUsedAsEmployeeCase()
    {
        // Подготовка
        StructuredObjectsView structure = DAOStructuredObjectsView.create();
        DSLStructuredObjectsView.add(structure);
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetainfo.add(employeeCase);
        // Выполнение действий
        LeftMenuItem linkToContent = DAOLeftMenuItem.createLinkToHierarchyGrid(null,true, null);
        linkToContent.setTitle(ModelUtils.createCode());
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUINavSettings.clickAddLeftMenuElement();

        GUIMenuItem.fillElementTitleField(linkToContent.getTitle());
        GUIMenuItem.selectMenuItemType(linkToContent.getType());
        GUIMenuItem.selectMenuItemListContentType(ContentType.HIERARCHY_GRID.getType());
        GUIMenuItem.selectHierarchyStructure(structure.getCode());
        GUIMenuItem.selectContentTemplate("![newTemplate]");
        GUIMenuItem.selectMenuItemListContentLinkObject(LinkToContentLinkObjectTypes.objLinkedToCurrentUser.getCode());
        GUIMenuItem.selectMenuItemListContentLinkObjectCase(employeeCase.getFqn());
        GUIMenuItem.selectMenuItemListContentLinkObjectAttr(SysAttribute.parent(employeeCase).getFqn());
        GUIMenuItem.fillElementListPageTitleField(ModelUtils.createTitle());
        GUIForm.applyForm();
        linkToContent.setCode(GUINavSettings.getLeftMenuId(linkToContent));
        linkToContent.setExists(true);

        GUIMetaClass.goToTab(employeeCase, MetaclassCardTab.ATTRIBUTES);
        // Проверки
        GUIMetaClass.tryDelete("Тип '" + employeeCase.getTitle() + "' не может быть удален.\n"
                + "Класс/тип '" + employeeCase.getTitle() + "' используется в настройках левого меню: "
                + linkToContent.getTitle());
        DSLMetaClass.assertPresent(employeeCase);
    }

    /**
     * Проверка ограничений на отображаемые вкладки "панель вкладок" в параметре "Вкладка карточки" и отображение полного пути до вкладки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>На карточке компании на вкладку метакласса "1" добавляем контент "Панель вкладок2" , вкладку "2",
     * добавляем контент "Панель вкладок3" , вкладку "3", добавляем контент "Панель вкладок4" , вкладку "4"</li>
     * <li>В блоке "Интерфейс и навигация" и создаем элемент element типа "ссылка на карточку"</li>
     * <br>
     * <b>Выполнение действия и проверка - 1 </b>
     * <li>Вкладка карточки: Компания / 1</li>
     * <li>В выпадающем списке параметра "Вкладка карточки" видим дерево</li>
     * <li>2 (контент)
     * 2 / 2
     * 3 (контент)
     * 3 / 3</li>
     * <li>Выбираем 3 / 3</li>
     * <br>
     * <b>Проверки</b>
     * <li>Содержимое element выглядит : Компания / 1 / 2 (контент) / 2 / 3 (контент) / 3</li>
     * </ol>
     */
    @Test
    public void testRefTabShowAllPath()
    {
        DSLNavSettings.editVisibilitySettings(true, true);

        MetaClass root = DAORootClass.create();

        //Вкладка метакласса
        ContentTab metaClassTab = DAOContentTab.createTab(root.getFqn());
        DSLContent.addTab(metaClassTab);

        ContentForm tabPanel1 = DAOContentCard.createTabBar(root.getFqn());
        DSLContent.add(metaClassTab, tabPanel1);
        ContentTab firstTabPanel1 = DSLContent.getFirstTab(tabPanel1);

        ContentForm tabPanel2 = DAOContentCard.createTabBar(root.getFqn());
        DSLContent.add(firstTabPanel1, tabPanel2);
        ContentTab firstTabPanel2 = DSLContent.getFirstTab(tabPanel2);
        MenuItem reference1 = DAOMenuItem.createReference(DAORootClass.create(), true, metaClassTab, tabPanel1,
                firstTabPanel1, tabPanel2, firstTabPanel2);
        DSLMenuItem.add(reference1);

        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUIMenuItem.clickEditTopMenuItem(reference1);
        GUIMenuItem.selectMenuItemTypeOfCard(MenuItemTypeOfCard.ROOT.getCode());
        GUIMenuItem.selectCardTab(metaClassTab);
        //@formatter:off
        List<String> expected = Lists.newArrayList(tabPanel1.getTitle()+" (контент)",
                tabPanel1.getTitle()+" / "+tabPanel1.getTitle(),
                tabPanel2.getTitle()+" (контент)",
                tabPanel2.getTitle()+" / "+tabPanel2.getTitle());
      //@formatter:on
        GUISelect.assertSelect(GUIMenuItem.MENU_ITEM_CONTENT_TAB_INPUT, expected, false, false, true);
        String id = String.format("%s-[%s, %s, %s, %s]", metaClassTab.getParentFqn(), tabPanel1.getCode(),
                firstTabPanel1.getCode(), tabPanel2.getCode(), firstTabPanel2.getCode());
        GUISelect.select(GUIMenuItem.MENU_ITEM_CONTENT_TAB_INPUT, id);
        GUIForm.applyForm();
        GUIMenuItem.goToTopMenuItemCard(reference1);

        //@formatter:off
        GUIMenuItem.assertContentOnItemCard(StringUtils.join(Lists.newArrayList(root.getTitle(), metaClassTab.getTitle(),
                tabPanel1.getTitle()+" (контент)", tabPanel1.getTitle(), tabPanel2.getTitle()+" (контент)", tabPanel2.getTitle()), " / "));
        //@formatter:on
    }

    /**
     * Проверка запрета на сброс настроек карточки типа, если на вкладку карточки этого типа ведет ссылка из элемента верхнего меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип Отдела ouCase</li>
     * <li>Добавить вкладку tab на карточку типа ouCase</b>
     * <li>Создать элемент верхнего меню Ссылка на карточку ouCase на вкладку tab</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку типа ouCase</li>
     * <li>Нажать кнопку Сбросить настройки</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверить наличие сообщения об ошибке</li>
     * </ol>
     */
    @Test
    public void testResetCardWithReference()
    {
        //Настройка
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        DSLMetaClass.add(ouCase);

        ContentTab tab = DAOContentTab.createTab(ouCase.getFqn());
        DSLContent.addTab(tab);

        MenuItem reference = DAOMenuItem.createReference(ouCase, true, tab);
        DSLMenuItem.add(reference);
        //Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToTab(ouCase, MetaclassCardTab.OBJECTCARD);
        tester.click(Div.MAIN_CONTENT + GUIContent.X_BTN_RESET_PROP);
        GUIForm.assertQuestionAppear("Отсутствует форма подтверждения сброса настроек.");
        GUIForm.clickYes();
        //Проверка
        GUIError.assertDialogError(String.format(
                "Настройки карточки не могут быть сброшены по причинам:\nВкладка %s/%s не может быть удалена. Она "
                        + "используется в настройках верхнего меню в элементах: '%s'",
                ouCase.getTitle(), tab.getTitle(), reference.getTitle()));
    }

    /**
     * Проверка отображения [не указано] в поле "Вложен в раздел" при добавлении элемента типа Избранное
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Выполнение действий</b>
     * <li>Добавить элемент меню chapter типа Раздел</li>
     * <li>Нажать кнопку Добавить элемент</li>
     * <li>Выбирать вид элемента - Избранное</li>
     * <br>
     * <b>Проверка</b>
     * <li>В поле "Вложен в раздел" по умолчанию установлено значение [не указано]</li>
     * </ol>
     */
    @Test
    public void testSelectParentElementOnAddForm()
    {
        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();

        List<String> oldIds = GUINavSettings.getElementsId();
        GUINavSettings.clickAddTopMenuElement();

        MenuItem chapter = DAOMenuItem.createChapter(false);
        GUIMenuItem.fillElementTitleField(chapter.getTitle());
        GUIMenuItem.selectMenuItemType(chapter.getType());
        GUIForm.applyForm();
        List<String> newIds = GUINavSettings.getNewElementsId(oldIds);
        chapter.setCode(newIds.get(0));
        chapter.setExists(true);

        GUINavSettings.clickAddTopMenuElement();
        GUIMenuItem.selectMenuItemType(MenuItemType.favorites);

        //Проверка
        GUITester.assertValue(GUIMenuItem.MENU_ITEM_PARENT_INPUT, GUISelect.EMPTY_VALUE);
    }

    /**
     * Проверка невозможности удаления контента "панель вкладок" и его вкладки, которые используются в элементе меню Ссылка на карточку
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>На карточке Компании добавить вкладку tab1</li>
     * <li>На карточке Компании на вкладке tab1 добавить контент tabPanel "Панель вкладок" и вкладку tab2</li>
     * <li>Добавить элемент меню reference типа Ссылка на карточку, ссылающуюся на tab2</li>
     * <br>
     * <b>Выполнение действия 1</b>
     * <li>Удалить вкладку tab2</li>
     * <br>
     * <b>Проверка 1</b>
     * <li>Было получено сообщение "Вкладка %Название удаляемой вкладки% не может быть удалена. Она используется в настройках верхнего меню: %Название элемента%"</li>
     * <br>
     * <b>Выполнение действия 2</b>
     * <li>Удалить панель вкладок tabPanel</li>
     * <br>
     * <b>Проверка 2</b>
     * <li>Было получено сообщение "Контент %Название контента% не может быть удален. Он используется в настройках верхнего меню: %Название элемента%"</li>
     * </ol>
     */
    @Test
    public void testTryDelContentUsedInReferenceMenuItem()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();

        ContentTab tab1 = DAOContentTab.createTab(rootClass.getFqn());
        DSLContent.addTab(tab1);

        ContentForm tabPanel = DAOContentCard.createTabBar(rootClass.getFqn());
        DSLContent.add(tab1, tabPanel);

        ContentTab tab2 = DAOContentTab.createTab(rootClass.getFqn());
        DSLContent.addTab(tabPanel, tab2);
        MenuItem reference = DAOMenuItem.createReference(rootClass, true, tab1, tabPanel, tab2);
        DSLMenuItem.add(reference);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToTab(rootClass, MetaclassCardTab.OBJECTCARD);
        GUITab.clickOnTab(tab1);

        //Проверка 1
        GUITab.deleteTabAssertError(tabPanel,tab2,
                "Вкладка Компания/%s не может быть удалена. Она используется в настройках верхнего меню в элементах: "
                        + "'%s'",
                tab2.getTitle(), reference.getTitle());
        GUITab.closeEditTabsForm();
        //Проверка 2
        GUIContent.deleteAssertError(tabPanel,
                String.format(
                        "Контент Компания/%s не может быть удален. Он используется в настройках верхнего меню в "
                                + "элементах: '%s'",
                        tabPanel.getTitle(), reference.getTitle()));
    }

    /**
     * Проверка корректности обработки невозможности удаления вкладки карточки метакласса, на которой находится контент, используемый в элементе меню Ссылка на карточку
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить тип сотрудника employeeCase</li>
     * <li>На карточке employeeCase добавить вкладку tab1</li>
     * <li>На вкладке tab1 добавить контент content "Панель вкладок" и вкладку tab2</li>
     * <li>Добавить элемент меню reference типа Ссылка на карточку, ссылающуюся на tab2</li>
     * <br>
     * <b>Выполнение действия 1</b>
     * <li>Удалить вкладку tab1</li>
     * <br>
     * <b>Проверка 1</b>
     * <li>Появилась ошибка</li>
     * <br>
     * <b>Выполнение действия 2</b>
     * <li>Нажать кнопку Добавить контент</li>
     * <br>
     * <b>Проверка 2</b>
     * <li>Ошибки не появилось</li>
     * </ol>
     */
    @Test
    public void testTryDelTabWithContentUsedInReferenceMenuItem()
    {
        //Подготовка
        MetaClass employeeCase = DAOEmployeeCase.create(DAOEmployeeCase.createClass());
        DSLMetaClass.add(employeeCase);

        ContentTab tab1 = DAOContentTab.createTab(employeeCase.getFqn());
        DSLContent.addTab(tab1);

        ContentForm tabPanel = DAOContentCard.createTabBar(employeeCase.getFqn());
        DSLContent.add(tab1, tabPanel);

        ContentTab tab2 = DAOContentTab.createTab(employeeCase.getFqn());
        DSLContent.addTab(tabPanel, tab2);

        MenuItem reference = DAOMenuItem.createReference(employeeCase, true, tab1, tabPanel, tab2);
        DSLMenuItem.add(reference);

        //Выполнение действий 1
        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeCase, MetaclassCardTab.OBJECTCARD);
        GUITab.clickOnTab(tab1);

        //Проверка 1
        GUITab.deleteCardTabAssertError(tab1,
                "Вкладка %s/%s не может быть удалена. Она используется в настройках верхнего меню в элементах: '%s'",
                employeeCase.getTitle(), tab1.getTitle(), reference.getTitle());

        //Выполнение действий 2
        GUITab.closeEditTabsForm();
        GUIContent.clickAdd();

        //Проверка 2
        GUIError.assertErrorAbsence();
    }

    /**
     * Проверка невозможности удаления пользовательского типа используемого в элементе меню Кнопка добавления объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и в нем пользовательский тип userCase</li>
     * <li>Добавить элемент меню button типа Кнопка добавления объектов ссылающуюся на userCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку userCase и нажать кнопку удаления метакласса</li>
     * <br>
     * <b>Проверка</b>
     * <li>Была получено сообщение о невозможности удаления userCase</li>
     * <li>Пользовательский тип userCase остался в системе</li>
     * </ol>
     */
    @Test
    public void testTryDelUserCaseUsedInAddButtonMenuItem()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        MenuItem button = DAOMenuItem.createAddButton(true, userCase);
        DSLMenuItem.add(button);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToCard(userCase);

        //Проверка
        String message = String.format(ErrorMessages.CASE_NOT_DELETE + "%n" + ErrorMessages.TRY_DEL_CASE_MI_REL,
                userCase.getTitle(), userCase.getTitle(), button.getTitle());
        GUIMetaClass.tryDelete(message);
        GUIForm.assertFormDisappear(GUIXpath.Any.QUESTION_DIALOG);
        DSLMetaClass.assertPresent(userCase);
    }

    /**
     * Проверка невозможности удаления пользовательского класса используемого в элементе меню Кнопка добавления объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Добавить элемент меню типа Раздел и в него элемент типа Ссылка на карточку ссылающийся на userClass</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку userClass и нажать кнопку удаления метакласса</li>
     * <br>
     * <b>Проверка</b>
     * <li>Была получено сообщение о невозможности удаления userClass</li>
     * <li>Пользовательский класс userClass остался в системе</li>
     * </ol>
     */
    @Test
    public void testTryDelUserClassUsedInReferenceMenuItem()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);

        MenuItem chapter = DAOMenuItem.createChapter(false);
        MenuItem reference = DAOMenuItem.createReference(chapter, userClass, false);
        DSLMenuItem.add(chapter, reference);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToCard(userClass);

        //Проверка
        String message = String.format(ErrorMessages.CLASS_NOT_DELETE + "%n" + ErrorMessages.TRY_DEL_CASE_MI_REL,
                userClass.getTitle(), userClass.getTitle(), reference.getTitle());
        GUIMetaClass.tryDelete(message);

        GUIForm.assertFormDisappear(GUIXpath.Any.QUESTION_DIALOG);
        DSLMetaClass.assertPresent(userClass);
    }

    /**
     * Тестирование элемента меню "Произвольная ссылка", начинающегося с url системы
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00237
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$136469190
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Включить видимость верхнего и левого меню</li>
     * <li>Создать класс userClass и его тип userCase с объектами userBo1 и userBo2 по этому типу</li>
     * <li>Добавить элемент верхнего меню menuItem "Произвольная ссылка": Адрес ссылки - карточка объекта userBo2,
     * Начинается с url системы - true, Включить - true</li>
     * <li>Добавить элемент левого меню leftMenuItem "Произвольная ссылка": Адрес ссылки - карточка объекта userBo1,
     * Начинается с url системы - true, Включить - true</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником/li>
     * <li>Кликнуть по элементу левого меню leftMenuItem</li>
     * <li>Проверить, что открылась карточка объекта userBo1</li>
     * <li>Кликнуть по элементу верхнего меню menuItem</li>
     * <li>Проверить, что открылась карточка объекта userBo2</li>
     * </ol>
     */
    @Test
    public void testCustomLinkStartingWithSystemUrl()
    {
        //Подготовка
        DSLNavSettings.editVisibilitySettings(true, true, false);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);
        Bo userBo1 = DAOUserBo.create(userCase);
        Bo userBo2 = DAOUserBo.create(userCase);
        DSLBo.add(userBo1, userBo2);
        String customLinkValue1 = "/sd/operator/#uuid:" + userBo1.getUuid();
        String customLinkValue2 = "/sd/operator/#uuid:" + userBo2.getUuid();
        LeftMenuItem leftMenuItem = DAOLeftMenuItem.createCustomLink(true, null);
        MenuItem menuItem = DAOMenuItem.createCustomLink(true, null);
        DSLMenuItem.add(menuItem);

        GUILogon.asSuper();
        GUINavSettings.goToCard();

        GUINavSettings.clickAddLeftMenuElement();
        GUIMenuItem.fillElementTitleField(leftMenuItem.getTitle());
        GUIMenuItem.selectMenuItemType(leftMenuItem.getType());
        GUIMenuItem.setStartsWithSystemUrl(true);
        GUIMenuItem.fillCustomLinkField(customLinkValue1);
        GUIForm.applyForm();
        String code = GUINavSettings.getLeftMenuIdByTitle(leftMenuItem.getTitle());
        leftMenuItem.setCode(code);
        leftMenuItem.setExists(true);

        GUINavSettings.clickEditMenuItem(menuItem);
        GUIMenuItem.fillElementTitleField(menuItem.getTitle());
        GUIMenuItem.setStartsWithSystemUrl(true);
        GUIMenuItem.fillCustomLinkField(customLinkValue2);
        GUIForm.applyForm();

        GUIMenuItem.goToLeftMenuItemCard(leftMenuItem);
        GUIMenuItem.clickSwitchButton();

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUINavigational.showNavPanelOperator();

        GUIButtonBar.clickButtonByText(leftMenuItem.getTitle());
        GUIBo.assertThatBoCard(userBo1);

        GUINavSettingsOperator.clickMenuItem(menuItem);
        GUIBo.assertThatBoCard(userBo2);
    }

    /**
     * Тестирование добавления элемента меню "Пользовательская кнопка" в верхнее и левое меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00410
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$137259891
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Включить видимость верхнего и левого меню</li>
     * <li>Создать класс userClass и его тип userCase</li>
     * <li>Создать пользовательское ДПС event на userClass со скриптом: "utils.edit(subject, ['title':'%s'])"</li>
     * <li>Создать форму быстрого добавления quickActionForm по системной группе атрибутов по типу userCase</li>
     * <li>Добавить элемент верхнего меню menuItem "Пользовательская кнопка" настроенный на быструю форму quickActionForm</li>
     * <li>Добавить элемент левого меню leftMenuItem "Пользовательская кнопка" настроенный на ДПС - event</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником/li>
     * <li>Открыть левое меню, проверить, что кнопка leftMenuItem отсутствует</li>
     * <li>Кликнуть по элементу верхнего меню menuItem</li>
     * <li>Заполнить поля на появившейся форме добавления объекта userBo и сохранить его</li>
     * <li>Проверить, что появилось уведомление о добавлении объекта userBo</li>
     * <li>Перейти на карточку объекта userBo и открыть левое меню</li>
     * <li>Кликнуть по элементу левого меню leftMenuItem</li>
     * <li>Проверить, что ДПС event сработало</li>
     * <li>Перезагрузить страницу и проверить, что название объекта userBo изменилось</li>
     * </ol>
     */
    @Test
    public void testAddingCustomButtonsToTopAndLeftMenu()
    {
        //Подготовка
        DSLNavSettings.editVisibilitySettings(true, true, false);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        String title = ModelUtils.createTitle();
        String scriptInfo = "utils.edit(subject, ['title':'%s'])";

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(String.format(scriptInfo, title));
        DSLScriptInfo.addScript(script);
        EventAction event = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true, userClass);
        DSLEventAction.add(event);

        CustomForm quickActionForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase);
        DSLCustomForm.add(quickActionForm);

        MenuItem menuItem = DAOMenuItem.createCustomButton(true, null);
        DSLMenuItem.add(menuItem);
        LeftMenuItem leftMenuItem = DAOLeftMenuItem.createCustomButton(true, null);

        GUILogon.asSuper();
        GUINavSettings.goToCard();

        GUINavSettings.clickAddLeftMenuElement();
        GUIMenuItem.fillElementTitleField(leftMenuItem.getTitle());
        GUIMenuItem.selectMenuItemType(leftMenuItem.getType());
        GUISelect.selectByTitle(ACTION_VALUE, event.getTitle());
        GUIForm.applyForm();
        leftMenuItem.setCode(GUINavSettings.getLeftMenuId(leftMenuItem));
        leftMenuItem.setExists(true);
        Cleaner.afterTest(() -> {
            DSLLeftMenuItem.delete(leftMenuItem);
        });

        GUINavSettings.clickEditMenuItem(menuItem);
        GUIMenuItem.fillElementTitleField(menuItem.getTitle());
        tester.setCheckbox(GUIXpath.Input.USE_QUICK_ADD_FORM, true);
        GUISelect.selectByTitle(Div.QUICK_ADD_FORM_VALUE, quickActionForm.getTitle());
        GUIForm.applyForm();
        GUIMenuItem.goToTopMenuItemCard(menuItem);
        GUIMenuItem.clickSwitchButton();

        GUINavSettings.goToCard();
        GUIMenuItem.goToLeftMenuItemCard(leftMenuItem);
        GUIMenuItem.clickSwitchButton();

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.assertMenuItemAbsent(leftMenuItem);

        GUINavSettingsOperator.clickMenuItem(menuItem);

        Set<String> old = DSLBo.getUuidsByFqn(userCase.getFqn());
        String titleBo = ModelUtils.createTitle();
        GUIForm.fillTitleOnFastAddEditForm(titleBo);
        GUIForm.applyForm();

        Bo userBo = DSLBo.getNewBoModel(old, userCase);

        GUIPush.assertPush("Создан объект " + titleBo + ".");
        GUIBo.goToCard(userBo);

        GUINavigational.hideNavPanel();
        GUINavigational.showNavPanelOperator();
        GUIButtonBar.clickButtonByText(leftMenuItem.getTitle());
        GUIForm.assertInfoDialog(String.format("Действие \"%s\" запущено", event.getTitle()));
        tester.refresh();
        DSLBo.assertTitle(userBo, title);
    }
}