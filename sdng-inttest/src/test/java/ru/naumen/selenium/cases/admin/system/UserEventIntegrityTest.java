package ru.naumen.selenium.cases.admin.system;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel;
import ru.naumen.selenium.casesutil.customforms.DSLFormParameter;
import ru.naumen.selenium.casesutil.interfaceelement.MetaTree;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIEventAction;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIAccessMatrix;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIMarkerForm;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScript;
import ru.naumen.selenium.casesutil.script.GUIScriptCatalogList;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.security.SecurityMarkerUserEvent;

/**
 * Тестирование сохранения ссылочной целостности связей с действиями по пользовательским событиям.
 * <AUTHOR>
 * @since Mar 10, 2016
 */
public class UserEventIntegrityTest extends AbstractTestCase
{
    private static MetaClass userClass;

    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        DSLMetainfo.add(userClass);
    }

    /**
     * Тестирование удаления пользовательского события из маркера прав после смены типа события
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * http://sd-jira.naumen.ru/browse/NSDPRD-5343
     * http://sd-jira.naumen.ru/browse/NSDPRD-6091
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass и наследуемый от него тип userCase</li>
     * <li>Создать действие по событию eventAction (Объекты - userClass, Событие - [Пользовательское событие],
     * Действие - Скрипт, Скрипт - <code>return</code>)</li>
     * <li>В матрице прав типа userCase создать маркер marker в группе "Пользовательские события" и добавить
     * в него eventAction</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку действия по событиям eventAction</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>Изменить тип события на "Изменение объекта" и нажать на кнопку "Сохранить"</li>
     * <li>Перейти в матрицу прав типа userCase</li>
     * <li>Нажать на иконку редактирования маркера marker</li>
     * <li>Проверить, что пользовательское событие eventAction отсутствует в левом и правом списках</li>
     * <li>Нажать на кнопку "Отмена"</li>
     * <li>Перейти на карточку действия по событиям eventAction</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>Изменить тип события на "[Пользовательское событие]" и нажать на кнопку "Сохранить"</li>
     * <li>Перейти в матрицу прав типа userCase</li>
     * <li>Нажать на иконку редактирования маркера marker</li>
     * <li>Проверить, что пользовательское событие eventAction находится в списке слева (доступно, но не выбрано)</li>
     * </ol>
     */
    @Test
    public void testClearUserEventMarkersOnChangeEventType()
    {
        // Подготовка
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userCase);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return");
        DSLScriptInfo.addScript(script);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true,
                userClass);
        DSLEventAction.add(eventAction);

        SecurityMarkerUserEvent marker = new SecurityMarkerUserEvent(userCase);
        marker.addEventActions(eventAction);
        marker.apply();
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userCase, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker.getRightCode());
        GUIMarkerForm.assertContainsEvent(true, true, eventAction);
        GUIMarkerForm.assertContainsEvent(false, false, eventAction);
        GUIForm.cancelDialog();

        GUIEventAction.goToCard(eventAction);
        GUIEventAction.editFromCard();
        GUIEventAction.setEventType(EventType.edit.getCode());
        GUIForm.applyModalForm();

        GUIMetaClass.goToTab(userCase, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker.getRightCode());
        GUIMarkerForm.assertContainsEvent(false, true, eventAction);
        GUIMarkerForm.assertContainsEvent(false, false, eventAction);
        GUIForm.cancelDialog();

        GUIEventAction.goToCard(eventAction);
        GUIEventAction.editFromCard();
        GUIEventAction.setEventType(EventType.userEvent.getCode());
        GUIForm.applyModalForm();

        GUIMetaClass.goToTab(userCase, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker.getRightCode());
        GUIMarkerForm.assertContainsEvent(false, true, eventAction);
        GUIMarkerForm.assertContainsEvent(true, false, eventAction);
        GUIForm.cancelDialog();
    }

    /**
     * Тестирование удаления пользовательского события из маркера прав после изменения набора
     * метаклассов в настройках действия по событию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * http://sd-jira.naumen.ru/browse/NSDPRD-5343
     * http://sd-jira.naumen.ru/browse/NSDPRD-6091
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass и наследуемые от него типы userCase1, userCase2</li>
     * <li>Создать действие по событию eventAction (Объекты - userClass, Событие - [Пользовательское событие],
     * Действие - Скрипт, Скрипт - <code>return</code>)</li>
     * <li>В матрице прав типа userCase1 создать маркер marker1 в группе "Пользовательские события" и добавить
     * в него eventAction</li>
     * <li>В матрице прав типа userCase2 создать маркер marker2 в группе "Пользовательские события" и добавить
     * в него eventAction</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку действия по событиям eventAction</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>В поле "Объекты" снять выбор с типа userCase2 и нажать на кнопку "Сохранить"</li>
     * <li>Перейти в матрицу прав типа userCase1</li>
     * <li>Нажать на иконку редактирования маркера marker1</li>
     * <li>Проверить, что пользовательское событие eventAction присутствует в списке справа (выбрано)</li>
     * <li>Нажать на кнопку "Отмена"</li>
     * <li>Перейти в матрицу прав типа userCase2</li>
     * <li>Нажать на иконку редактирования маркера marker2</li>
     * <li>Проверить, что пользовательское событие eventAction отсутствует в списках слева и справа</li>
     * <li>Нажать на кнопку "Отмена"</li>
     * <li>Перейти на карточку действия по событиям eventAction</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>В поле "Объекты" вернуть выбор типа userCase2 и нажать на кнопку "Сохранить"</li>
     * <li>Перейти в матрицу прав типа userCase1</li>
     * <li>Нажать на иконку редактирования маркера marker1</li>
     * <li>Проверить, что пользовательское событие eventAction присутствует в списке справа (выбрано)</li>
     * <li>Нажать на кнопку "Отмена"</li>
     * <li>Перейти в матрицу прав типа userCase2</li>
     * <li>Нажать на иконку редактирования маркера marker2</li>
     * <li>Проверить, что пользовательское событие eventAction находится в списке слева (доступно, но не выбрано)</li>
     * </ol>
     */
    @Test
    public void testClearUserEventMarkersOnChangeObjectCases()
    {
        // Подготовка
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        DSLMetainfo.add(userCase1, userCase2);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return");
        DSLScriptInfo.addScript(script);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true,
                userClass);
        DSLEventAction.add(eventAction);

        SecurityMarkerUserEvent marker1 = new SecurityMarkerUserEvent(userCase1);
        marker1.addEventActions(eventAction);
        marker1.apply();
        SecurityMarkerUserEvent marker2 = new SecurityMarkerUserEvent(userCase2);
        marker2.addEventActions(eventAction);
        marker2.apply();
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userCase1, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker1.getRightCode());
        GUIMarkerForm.assertContainsEvent(true, true, eventAction);
        GUIMarkerForm.assertContainsEvent(false, false, eventAction);
        GUIForm.cancelDialog();

        GUIMetaClass.goToTab(userCase2, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker2.getRightCode());
        GUIMarkerForm.assertContainsEvent(true, true, eventAction);
        GUIMarkerForm.assertContainsEvent(false, false, eventAction);
        GUIForm.cancelDialog();

        GUIEventAction.goToCard(eventAction);
        GUIEventAction.editFromCard();
        MetaTree fqnTree = new MetaTree(GUIXpath.Id.FQN_VALUE);
        fqnTree.unsetElementInMultiSelectTree(userClass, userCase2);
        GUIForm.applyModalForm();

        GUIMetaClass.goToTab(userCase1, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker1.getRightCode());
        GUIMarkerForm.assertContainsEvent(true, true, eventAction);
        GUIMarkerForm.assertContainsEvent(false, false, eventAction);
        GUIForm.cancelDialog();

        GUIMetaClass.goToTab(userCase2, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker2.getRightCode());
        GUIMarkerForm.assertContainsEvent(false, true, eventAction);
        GUIMarkerForm.assertContainsEvent(false, false, eventAction);
        GUIForm.cancelDialog();

        GUIEventAction.goToCard(eventAction);
        GUIEventAction.editFromCard();
        fqnTree.setElementInMultiSelectTree(userClass, userCase2);
        GUIForm.applyModalForm();

        GUIMetaClass.goToTab(userCase1, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker1.getRightCode());
        GUIMarkerForm.assertContainsEvent(true, true, eventAction);
        GUIMarkerForm.assertContainsEvent(false, false, eventAction);
        GUIForm.cancelDialog();

        GUIMetaClass.goToTab(userCase2, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker2.getRightCode());
        GUIMarkerForm.assertContainsEvent(false, true, eventAction);
        GUIMarkerForm.assertContainsEvent(true, false, eventAction);
        GUIForm.cancelDialog();
    }

    /**
     * Тестирование удаления пользовательского события из маркера прав после удаления действия по событию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * http://sd-jira.naumen.ru/browse/NSDPRD-5343
     * http://sd-jira.naumen.ru/browse/NSDPRD-6091
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass и наследуемый от него тип userCase</li>
     * <li>Создать действие по событию eventAction (Объекты - userClass, Событие - [Пользовательское событие],
     * Действие - Скрипт, Скрипт - <code>return</code>)</li>
     * <li>В матрице прав типа userCase создать маркер marker в группе "Пользовательские события" и добавить
     * в него eventAction</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку действия по событиям eventAction</li>
     * <li>Нажать на кнопку "Удалить" и подтвердить удаление</li>
     * <li>Перейти в матрицу прав типа userCase</li>
     * <li>Нажать на иконку редактирования маркера marker</li>
     * <li>Проверить, что пользовательское событие eventAction отсутствует в левом и правом списках</li>
     * </ol>
     */
    @Test
    public void testClearUserEventMarkersOnDelete()
    {
        // Подготовка
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userCase);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return");
        DSLScriptInfo.addScript(script);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true,
                userClass);
        DSLEventAction.add(eventAction);

        SecurityMarkerUserEvent marker = new SecurityMarkerUserEvent(userCase);
        marker.addEventActions(eventAction);
        marker.apply();
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userCase, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker.getRightCode());
        GUIMarkerForm.assertContainsEvent(true, true, eventAction);
        GUIMarkerForm.assertContainsEvent(false, false, eventAction);
        GUIForm.cancelDialog();

        GUIEventAction.goToCard(eventAction);
        GUIForm.openDeleteForm();
        GUIForm.confirmDelete();

        GUIMetaClass.goToTab(userCase, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker.getRightCode());
        GUIMarkerForm.assertContainsEvent(false, true, eventAction);
        GUIMarkerForm.assertContainsEvent(false, false, eventAction);
        GUIForm.cancelDialog();
    }

    /**
     * Тестирование удаления параметров пользовательского события после смены типа события
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * http://sd-jira.naumen.ru/browse/NSDPRD-5343
     * http://sd-jira.naumen.ru/browse/NSDPRD-6091
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass</li>
     * <li>Создать действие по событию eventAction (Объекты - userClass, Событие - [Пользовательское событие],
     * Действие - Скрипт, Скрипт - <code>return</code>)</li>
     * <li>Добавить вычислимый строковый параметр param действия по событию eventAction,
     * скрипт вычисления значения (computableScript) - <code>return '42'</code></li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку действия по событиям eventAction</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>Изменить тип события на "Изменение объекта" и нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что список параметров действия по событию отсуствует</li>
     * <li>Перейти в каталог скриптов и проверить, что ошибок нет</li>
     * <li>Перейти на карточку скрипта computableScript</li>
     * <li>Проверить, категория скрипта - [Без категории]</li>
     * <li>Перейти на карточку действия по событиям eventAction</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>Изменить тип события на "[Пользовательское событие]" и нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что список параметров действия по событию присутствует, но не содержит ни одного элемента</li>
     * </ol>
     */
    @Test
    public void testClearUserEventParametersOnChangeEventType()
    {
        // Подготовка
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return");
        DSLScriptInfo.addScript(script);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true,
                userClass);
        DSLEventAction.add(eventAction);

        ScriptInfo computableScript = DAOScriptInfo.createNewScriptInfo("return '42'");
        DSLScriptInfo.addScript(computableScript);
        FormParameter param = DAOFormParameter.createString();
        DAOAttribute.changeToComputable(param, computableScript);
        param.setEventAction(eventAction.getUuid());
        DSLFormParameter.save(param);
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEventAction.goToCard(eventAction);
        GUIEventAction.assertParamOrder(param.getCode());
        GUIEventAction.editFromCard();
        GUIEventAction.setEventType(EventType.edit.getCode());
        GUIForm.applyModalForm();
        GUIEventAction.assertParameterListPresent(false);
        param.setExists(false);

        GUIScriptCatalogList.goToAllScripts();
        GUIError.assertErrorAbsence();
        GUIScript.goToCard(computableScript);
        GUIScript.assertCategoryName("[Без категории]");

        GUIEventAction.goToCard(eventAction);
        GUIEventAction.editFromCard();
        GUIEventAction.setEventType(EventType.userEvent.getCode());
        GUIForm.applyModalForm();
        GUIEventAction.assertParameterListPresent(true);
        GUIEventAction.assertParamOrder();
    }

    /**
     * Тестирование удаления параметров пользовательского события после удаления действия по событию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * http://sd-jira.naumen.ru/browse/NSDPRD-5343
     * http://sd-jira.naumen.ru/browse/NSDPRD-6091
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass</li>
     * <li>Создать действие по событию eventAction (Объекты - userClass, Событие - [Пользовательское событие],
     * Действие - Скрипт, Скрипт - <code>return</code>)</li>
     * <li>Добавить вычислимый строковый параметр param действия по событию eventAction,
     * скрипт вычисления значения (computableScript) - <code>return '42'</code></li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку действия по событиям eventAction</li>
     * <li>Нажать на кнопку "Удалить" и подтвердить удаление</li>
     * <li>Перейти в каталог скриптов и проверить, что ошибок нет</li>
     * <li>Перейти на карточку скрипта computableScript</li>
     * <li>Проверить, категория скрипта - [Без категории]</li>
     * </ol>
     */
    @Test
    public void testClearUserEventParametersOnDelete()
    {
        // Подготовка
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return");
        DSLScriptInfo.addScript(script);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true,
                userClass);
        DSLEventAction.add(eventAction);

        ScriptInfo computableScript = DAOScriptInfo.createNewScriptInfo("return '42'");
        DSLScriptInfo.addScript(computableScript);
        FormParameter param = DAOFormParameter.createString();
        DAOAttribute.changeToComputable(param, computableScript);
        param.setEventAction(eventAction.getUuid());
        DSLFormParameter.save(param);
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEventAction.goToCard(eventAction);
        GUIEventAction.assertParamOrder(param.getCode());
        GUIForm.openDeleteForm();
        GUIForm.confirmDelete();
        param.setExists(false);

        GUIScriptCatalogList.goToAllScripts();
        GUIError.assertErrorAbsence();
        GUIScript.goToCard(computableScript);
        GUIScript.assertCategoryName("[Без категории]");
    }

    /**
     * Тестирование удаления пользовательского события после смены типа события
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * http://sd-jira.naumen.ru/browse/NSDPRD-5343
     * http://sd-jira.naumen.ru/browse/NSDPRD-6091
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass</li>
     * <li>Создать действие по событию eventAction (Объекты - userClass, Событие - [Пользовательское событие],
     * Действие - Скрипт, Скрипт - <code>return</code>)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку действия по событиям eventAction</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>Изменить тип события на "Изменение объекта" и нажать на кнопку "Сохранить"</li>
     * <li>Перейти на страницу настройки карточки объекта класса userClass</li>
     * <li>Перейти в режим редактирования панели инструментов</li>
     * <li>Перенести шаблон "Новая кнопка" на панель действий</li>
     * <li>Проверить, что в списке доступных действий отсутствует eventAction</li>
     * <li>Нажать на кнопку "Отмена" и закрыть оба диалоговых окна</li>
     * <li>Перейти на карточку действия по событиям eventAction</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>Изменить тип события на "[Пользовательское событие]" и нажать на кнопку "Сохранить"</li>
     * <li>Перейти на страницу настройки карточки объекта класса userClass</li>
     * <li>Перейти в режим редактирования панели инструментов</li>
     * <li>Перенести шаблон "Новая кнопка" на панель действий</li>
     * <li>Проверить, что в списке доступных действий присутствует eventAction</li>
     * </ol>
     */
    @Test
    public void testUserEventUnavailableAfterChangeEventType()
    {
        // Подготовка
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return");
        DSLScriptInfo.addScript(script);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true,
                userClass);
        DSLEventAction.add(eventAction);
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEventAction.goToCard(eventAction);
        GUIEventAction.editFromCard();
        GUIEventAction.setEventType(EventType.edit.getCode());
        GUIForm.applyModalForm();

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        ContentForm window = DSLContent.getWindowContent(userClass);
        GUIAdvListEditableToolPanel editableToolPanel = window.advlist().editableToolPanel();
        editableToolPanel.clickEditToolPanel();
        editableToolPanel.moveTool(GUIButtonBar.BTN_NEW_TEMPLATE, GUIButtonBar.BTN_EDIT, false);
        editableToolPanel.assertsEditMode().availableActionsOnForm(false, eventAction);
        editableToolPanel.clickCancelOnDialogByTitle("Добавление элемента");
        GUIForm.cancelDialog();

        GUIEventAction.goToCard(eventAction);
        GUIEventAction.editFromCard();
        GUIEventAction.setEventType(EventType.userEvent.getCode());
        GUIForm.applyModalForm();

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        editableToolPanel = window.advlist().editableToolPanel();
        editableToolPanel.clickEditToolPanel();
        editableToolPanel.moveTool(GUIButtonBar.BTN_NEW_TEMPLATE, GUIButtonBar.BTN_EDIT, false);
        editableToolPanel.assertsEditMode().availableActionsOnForm(true, eventAction);
        editableToolPanel.clickCancelOnDialogByTitle("Добавление элемента");
        GUIForm.cancelDialog();
    }

    /**
     * Тестирование удаления пользовательского события после удаления действия по событию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * http://sd-jira.naumen.ru/browse/NSDPRD-5343
     * http://sd-jira.naumen.ru/browse/NSDPRD-6091
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass</li>
     * <li>Создать действие по событию eventAction (Объекты - userClass, Событие - [Пользовательское событие],
     * Действие - Скрипт, Скрипт - <code>return</code>)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку действия по событиям eventAction</li>
     * <li>Нажать на кнопку "Удалить" и подтвердить удаление</li>
     * <li>Перейти на страницу настройки карточки объекта класса userClass</li>
     * <li>Перейти в режим редактирования панели инструментов</li>
     * <li>Перенести шаблон "Новая кнопка" на панель действий</li>
     * <li>Проверить, что в списке доступных действий отсутствует eventAction</li>
     * </ol>
     */
    @Test
    public void testUserEventUnavailableAfterDelete()
    {
        // Подготовка
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return");
        DSLScriptInfo.addScript(script);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true,
                userClass);
        DSLEventAction.add(eventAction);
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEventAction.goToCard(eventAction);
        GUIForm.openDeleteForm();
        GUIForm.confirmDelete();

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        ContentForm window = DSLContent.getWindowContent(userClass);
        GUIAdvListEditableToolPanel editableToolPanel = window.advlist().editableToolPanel();
        editableToolPanel.clickEditToolPanel();
        editableToolPanel.moveTool(GUIButtonBar.BTN_NEW_TEMPLATE, GUIButtonBar.BTN_EDIT, false);
        editableToolPanel.assertsEditMode().availableActionsOnForm(false, eventAction);
        editableToolPanel.clickCancelOnDialogByTitle("Добавление элемента");
        GUIForm.cancelDialog();
    }
}