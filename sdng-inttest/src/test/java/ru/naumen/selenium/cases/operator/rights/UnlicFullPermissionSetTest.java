package ru.naumen.selenium.cases.operator.rights;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;
import java.util.stream.Stream.Builder;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.TimerType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование работы с лицензией с полным ("full") набором прав доступа
 * для нелицензированных пользователей<br>
 *
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00387
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
 * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$73937422
 *
 * <AUTHOR>
 * @since 06.06.19
 */
public class UnlicFullPermissionSetTest extends AbstractTestCase
{
    private static Bo employee;
    private static SecurityProfile secProfile;

    /**
     * <b>Общая подготовка</b>
     * <ol>
     * <li>Создаем сотрудника employee со значением атрибута "Лицензия" = "Нелицензированный пользователь"</li>
     * <li>Создаём профиль прав доступа secProfile с параметрами:<br>
     *      "Для нелицензированных пользователей" = "да"<br>
     *      "Роль" = "Сотрудник"</li>
     * <li>Добавить соглашение сотруднику</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(),
                false, false);
        DSLBo.add(employee);

        secProfile = DAOSecurityProfile.create(
                false, null, SysRole.employee());
        DSLSecurityProfile.add(secProfile);

        DSLAgreement.addToRecipients(SharedFixture.agreement(), employee);
    }

    /**
     * Проверка редактируемости атрибутов<br>
     * Проверяет видимость атрибутов, открывает форму редактирования и проверяет что
     * на форме редактирования присутствуют все указанные атрибуты
     */
    private static void assertEditableAttributes(ContentForm editablePropertyList,
            Attribute[] editableAttributes)
    {
        assertViewableAttributes(editablePropertyList, editableAttributes, false);
        GUIContent.assertLinkPresent(editablePropertyList, GUIContent.LINK_EDIT);
        GUIContent.clickEdit(editablePropertyList);
        GUIForm.assertFormAppear(GUIXpath.Div.CONTAINS_FORM);
        GUIForm.assertAttrPresent(editableAttributes);
        GUIForm.cancelForm();
    }

    /**
     * Проверка видимости атрибутов<br>
     * Проверяет что блок параметров объекта присутствует и содержит все указанные атрибуты
     * @param propertyList контент типа "Параметры объекта"
     * @param attributes проверяемые атрибуты
     * @param checkNotEditable проверить что атрибуты нельзя редактировать
     */
    private static void assertViewableAttributes(
            ContentForm propertyList, Attribute[] attributes, boolean checkNotEditable)
    {
        GUIContent.assertPresent(propertyList);
        GUIContent.assertContentAttributeCaption(
                propertyList, attributes);
        if (checkNotEditable)
        {
            GUIContent.assertLinkAbsense(propertyList, GUIContent.LINK_EDIT);
        }
    }

    /**
     * Проверка видимости атрибутов<br>
     * Проверяет что блок параметров объекта присутствует и содержит все указанные атрибуты
     * и что атрибуты нельзя редактировать
     * @param propertyList контент типа "Параметры объекта"
     * @param attributes проверяемые атрибуты
     */
    private static void assertViewableAttributes(
            ContentForm propertyList, Attribute[] attributes)
    {
        assertViewableAttributes(propertyList, attributes, true);
    }

    private static ContentForm createPropertyListWithAttrs(
            MetaClass metaClass, Attribute[] attributes, String groupTitle)
    {
        GroupAttr groupAttr = DAOGroupAttr.create(metaClass, groupTitle);
        DSLGroupAttr.add(groupAttr, attributes);

        ContentForm propertyList = DAOContentCard.createPropertyList(metaClass, groupAttr);
        DSLContent.add(propertyList);
        return propertyList;
    }

    /**
     * Создать модели (сохранённые) пользовательских атрибутов редактируемые для всех
     * <ul>
     *   <li>Временной интервал</li>
     *   <li>Дата</li>
     *   <li>Дата/Время</li>
     *   <li>Логический</li>
     *   <li>Строка</li>
     *   <li>Текст</li>
     *   <li>Целое число</li>
     *   <li>Элемент справочника</li>
     *   <li>Набор элементов справочника</li>
     * </ul>
     */
    private static Attribute[] createUserAttrsEditableForAll(MetaClass metaClass)
    {
        Attribute[] attributes = {
                DAOAttribute.createTimeInterval(metaClass.getFqn()),
                DAOAttribute.createDate(metaClass.getFqn()),
                DAOAttribute.createDateTime(metaClass.getFqn()),
                DAOAttribute.createBool(metaClass),
                DAOAttribute.createString(metaClass),
                DAOAttribute.createText(metaClass.getFqn()),
                DAOAttribute.createInteger(metaClass.getFqn()),
                DAOAttribute.createCatalogItem(metaClass.getFqn(),
                        DAOCatalog.createSystem(SystemCatalog.TIMEZONE), null),
                DAOAttribute.createCatalogItemSet(metaClass.getFqn(),
                        DAOCatalog.createSystem(SystemCatalog.TIMEZONE))
        };
        DSLAttribute.add(attributes);
        return attributes;
    }

    /**
     * Возвращает модели (сохранённые) пользовательских атрибутов редактируемые только для
     * набора прав FULL, и видимые для всех
     * <ul>
     *   <li>Вещественное число</li>
     *   <li>Гиперссылка</li>
     *   <li>Набор типов класса</li>
     *   <li>Текст в формате RTF</li>
     *   <li>Текст с подсветкой синтаксиса</li>
     *   <li>Файл</li>
     * </ul>
     */
    private static Attribute[] createUserAttrsEditableForFull(MetaClass metaClass)
    {
        Attribute[] attributes = {
                DAOAttribute.createDouble(metaClass),
                DAOAttribute.createHyperlink(metaClass.getFqn()),
                DAOAttribute.createCaseList(metaClass.getFqn(),
                        SharedFixture.employeeCase()),
                DAOAttribute.createTextRTF(metaClass.getFqn()),
                DAOAttribute.createSourceCode(metaClass.getFqn()),
                DAOAttribute.createFile(metaClass.getFqn())
        };
        DSLAttribute.add(attributes);
        return attributes;
    }

    /**
     * Возвращает модели (сохранённые) пользовательских атрибутов, нередактируемые,
     * но видимые для всех
     * <ul>
     *   <li>Агрегирующий</li>
     *   <li>Ссылка на бизнес-объект</li>
     *   <li>Набор ссылок на БО</li>
     *   <li>Счётчик времени</li>
     *   <li>Счётчик времени (обратный)</li>
     * </ul>
     * @param excludeTimers исключить атрибуты "Счетчик времени" и "Счетчик времени (обратный)"
     *     (эти атрибуты в принципе нередактируемые)
     */
    private static Attribute[] createUserAttrsViewableForAll(
            MetaClass metaClass, boolean excludeTimers)
    {
        Builder<Attribute> builder = Stream.<Attribute> builder()
                .add(DAOAttribute.createAggregate(metaClass,
                        AggregatedClasses.OU_AND_TEAM, null, null))
                .add(DAOAttribute.createObjectLink(metaClass, SharedFixture.ouCase(), null))
                .add(DAOAttribute.createBoLinks(metaClass, SharedFixture.teamCase()));
        if (!excludeTimers)
        {
            BoStatus registered = DAOBoStatus.createRegistered(metaClass);
            Attribute timeZoneAttr = DAOAttribute.createCatalogItem(metaClass.getFqn(),
                    DAOCatalog.createSystem(SystemCatalog.TIMEZONE), null);
            DSLAttribute.add(timeZoneAttr);
            TimerDefinition timer = DAOTimerDefinition.createAstroTimerByStatus(
                    metaClass.getFqn(), timeZoneAttr.getCode(), registered);
            TimerDefinition backTimer = DAOTimerDefinition.createAstroTimerByStatus(
                    metaClass.getFqn(), timeZoneAttr.getCode(), registered);
            DSLTimerDefinition.add(timer, backTimer);

            builder.add(DAOAttribute.createTimer(metaClass.getFqn(),
                    timer, TimerType.TIMER_STATUS_VIEW))
                    .add(DAOAttribute.createBackTimer(metaClass.getFqn(),
                            backTimer, TimerType.TIMER_STATUS_VIEW));
        }

        Attribute[] attributes = builder.build().toArray(Attribute[]::new);
        DSLAttribute.add(attributes);
        return attributes;
    }

    /**
     * Тестирование прав доступа нелицензированным пользователеям для класса "Анкета"<br>
     * Проверка производится для наборов прав "system" и "full"<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00387
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс {@code userClass} с кодом 'questionary'</li>
     * <li>Создать тип {@code userCase} для класса {@code userClass}</li>
     * <li>Создаём группу атрибутов {@code systemEditableAttrs} с названием "Редактируемые системные":
     *     <ul>
     *       <li>Иконка (system_icon)</li>
     *       <li>Номер (number)</li>
     *     </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу атрибутов {@code systemViewableAttrs} с названием "Системные видимые всем":
     *   <ul>
     *     <li>Название (title)</li>
     *     <li>Признак архивирования (removed)</li>
     *     <li>Тип объекта (metaClass)</li>
     *     <li>Уникальный идентификатор (UUID)</li>
     *     <li>[ЖЦ] Статус (state)</li>
     *   </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу атрибутов {@code systemViewableAttrsFULL} с названием
     * "Системные видимые только для набора прав FULL":
     * <ul>
     *   <li>Автор (author)</li>
     *   <li>Дата архивирования (removalDate)</li>
     *   <li>Дата изменения (lastModifiedDate)</li>
     *   <li>Дата создания (creationDate)</li>
     *   <li>Папки (folders)</li>
     *   <li>[ЖЦ] Время входа в статус (stateStartTime)</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу пользовательских атрибутов {@code editableUserAttributes} с названием
     * "Редактируемые пользовательские (все)":
     * <ul>
     *   <li>Временной интервал</li>
     *   <li>Дата</li>
     *   <li>Дата/Время</li>
     *   <li>Логический</li>
     *   <li>Строка</li>
     *   <li>Текст</li>
     *   <li>Целое число</li>
     *   <li>Элемент справочника</li>
     *   <li>Набор элементов справочника</li>
     *   <li>Вещественное число</li>
     *   <li>Гиперссылка</li>
     *   <li>Набор типов класса</li>
     *   <li>Текст в формате RTF</li>
     *   <li>Текст с подсветкой синтаксиса</li>
     *   <li>Файл</li>
     *   <li>Агрегирующий</li>
     *   <li>Ссылка на бизнес-объект</li>
     *   <li>Набор ссылок на БО</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Для профиля secProfile выставляем права:
     *     просмотр карточки объекта,
     *     просмотр атрибутов: Остальные атрибуты,
     *     редактирование атрибутов: Остальные атрибуты</li>
     * <li>Создаем объект {@code bo} типа {@code userCase}</li>
     * <br>
     * <b>Действия и проверки 1. Для набора прав SYSTEM</b>
     * <li>Зайти под сотрудником {@code employee}</li>
     * <li>Перейти на карточку объекта {@code bo}</li>
     * <li>Проверить, что атрибуты {@code systemViewableAttrs}
     *     видимые и нередактируемые</li>
     * <li>Проверить что атрибуты {@code systemViewableAttrsFULL} отсутствуют на карточке</li>
     * <li>Проверить, что атрибуты {@code systemEditableAttrs} редактируемые</li>
     * <li>Проверить, что атрибуты {@code editableUserAttributes} редактируемые</li>
     * <br>
     * <b>Действия и проверки 2.  Для набора прав FULL</b>
     * <li>Загрузить лицензию с полным набором прав доступа для нелиц. пользователей</li>
     * <li>Обновить страницу и перейти на карточку {@code bo}</li>
     * <li>Проверить, что атрибуты {@code systemViewableAttrs} видимые и нередактируемые</li>
     * <li>Проверить что атрибуты {@code systemViewableAttrsFULL} видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code systemEditableAttrs} редактируемые</li>
     * <li>Проверить, что атрибуты {@code editableUserAttributes} редактируемые</li>
     * </ol>
     */
    @Test
    public void testQuestionaryPermissionsUnlicUser()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        userClass.setTitle("Анкета");
        userClass.setCode("questionary");
        userClass.setFqn("questionary");
        assertSpecialSystemAndEditableAllUserAttributes(userClass);
    }

    /**
     * Тестирование прав доступа нелицензированным пользователеям для класса "Запрос"<br>
     * Проверка производится для наборов прав "system, "full" и возможностей из
     * набора прав "extendedWorkflow" (перевода запроса из любого состояния в состояние
     * "возобновлен" или "закрыт") при загруженной лицензии с набором прав доступа "full"
     * и настроенным жизненным циклом<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00387
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}<li>
     * <li>Создать тип scCase для класса "Запрос"</li>
     * <li>Создать пользовательский статус userStatus</li>
     * <li>В типе scCase разрешить переход из статуса "Зарегистрирован" (registered) ->
     *     (userStatus) -> "Закрыт" (closed) -> "Возобновлен" (resumed) в матрице переходов</li>
     * <li>В типе scCase разрешить переход из статуса (userStatus) в "Возобновлен" (resumed) в матрице переходов</li>
     * <li>Сделать атрибуты "Кем закрыт" и "Код закрытия" необязательным на входе в статус "Закрыт"</li>
     * <li>Создаём группу атрибутов {@code systemEditableAttrs} с названием "Редактируемые системные":
     *     <ul>
     *       <li>Иконка (system_icon)</li>
     *     </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу атрибутов {@code systemAttrsViewableForAll} с названием
     * "Системные видимые всем":
     *   <ul>
     *     <li>Название (title)</li>
     *     <li>Номер (number)</li>
     *     <li>Признак архивирования (removed)</li>
     *     <li>Тип объекта (metaClass)</li>
     *     <li>Уникальный идентификатор (UUID)</li>
     *     <li>[ЖЦ] Статус (state)</li>
     *   </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу атрибутов {@code systemScAttrsViewableForAll} с названием
     * "Системные класса 'Запрос' видимые всем":
     *   <ul>
     *     <li>Дата регистрации (registrationDate)</li>
     *     <li>Запас нормативного времени обслуживания (timeAllowanceTimer)</li>
     *     <li>Соглашение (agreement)</li>
     *     <li>Услуга (service)</li>
     *     <li>Статус (state)</li>
     *     <li>Код закрытия (codeOfClosing)</li>
     *   </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу атрибутов {@code systemAttrsViewableForFull} с названием
     * "Системные видимые только для FULL":
     * <ul>
     *   <li>Автор (author)</li>
     *   <li>Дата архивирования (removalDate)</li>
     *   <li>Дата изменения (lastModifiedDate)</li>
     *   <li>Дата создания (creationDate)</li>
     *   <li>Папки (folders)</li>
     *   <li>[ЖЦ] Время входа в статус (stateStartTime)</li>
     *   <li>[Ответственный] Время последнего изменения ответственных (responsibleStartTime)</li>
     *   <li>[Ответственный] Ответственный (responsible)</li>
     *   <li>[Ответственный] Ответственный (команда) (responsibleTeam)</li>
     *   <li>[Ответственный] Ответственный (сотрудник) (responsibleEmployee)</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу атрибутов {@code systemScAttrsViewableForFull} с названием
     * "Системные класса 'Запрос' видимые только для FULL":
     * <ul>
     *   <li>Влияние (impact)</li>
     *   <li>Массовый (massProblem)</li>
     *   <li>Массовый запрос (masterMassProblem)</li>
     *   <li>Время обработки запроса (processingTimeTimer)</li>
     *   <li>Время входа в статус (stateStartTime)</li>
     *   <li>Дата обращения (requestDate)</li>
     *   <li>Информация о клиенте для отображения в интерфейсе (clientLinkName)</li>
     *   <li>Категории запроса (categories)</li>
     *   <li>Кем закрыт (closedBy)</li>
     *   <li>Кем закрыт (сотрудник) (closedByEmployee)</li>
     *   <li>Кем закрыт (команда) (closedByTeam)</li>
     *   <li>Кем решен (solvedBy)</li>
     *   <li>Кем решен (сотрудник) (solvedByTeam)</li>
     *   <li>Кем решен (команда) (solvedByEmployee)</li>
     *   <li>Класс обслуживания (serviceTime)</li>
     *   <li>Код профиля связанных жизненных циклов (wfProfile)</li>
     *   <li>Контрагент (client)</li>
     *   <li>Контрагент (сотрудник) (clientEmployee)</li>
     *   <li>Контрагент (команда) (clientTeam)</li>
     *   <li>Контрагент (отдел) (clientOU)</li>
     *   <li>Нормативное время обработки (resolutionTime)</li>
     *   <li>Подчинённые запросы (massProblemSlaves)</li>
     *   <li>Приоритет (priority)</li>
     *   <li>Регламентное закрытие заявки (deadLineTime)</li>
     *   <li>Регламентное время начала работ (startTime)</li>
     *   <li>Счётчик суммарного времени обработки заявок (totalTimeTimer)</li>
     *   <li>Часовой пояс (timeZone)</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу пользовательских атрибутов {@code userEditableAttrs} с названием
     * "Пользовательские редактируемые (все)":
     * <ul>
     *   <li>Временной интервал</li>
     *   <li>Дата</li>
     *   <li>Дата/Время</li>
     *   <li>Логический</li>
     *   <li>Строка</li>
     *   <li>Текст</li>
     *   <li>Целое число</li>
     *   <li>Элемент справочника</li>
     *   <li>Набор элементов справочника</li>
     *   <li>Вещественное число</li>
     *   <li>Гиперссылка</li>
     *   <li>Набор типов класса</li>
     *   <li>Текст в формате RTF</li>
     *   <li>Текст с подсветкой синтаксиса</li>
     *   <li>Файл</li>
     *   <li>Агрегирующий</li>
     *   <li>Ссылка на бизнес-объект</li>
     *   <li>Набор ссылок на БО</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Для профиля secProfile выставляем права:
     *     просмотр карточки объекта, смена статуса, просмотр атрибутов: Остальные атрибуты,
     *     редактирование атрибутов: Остальные атрибуты</li>
     * <li>Создаем запросы sc1 и sc2</li>
     * <li>Переводим статус запросов sc1 и sc2 на userStatus</li>
     * <br>
     * <b>Действия и проверки 0. Для типа sc1 и набора прав SYSTEM</b>
     * <li>Зайти под сотрудником employee</li>
     * <li>Перейти на карточку запроса sc1</li>
     * <li>Проверить, запрос в статусе userStatus</li>
     * <li>Проверить, что на карточке запроса отсутствует кнопка "Изменить статус"</li>
     * <li>Проверить, что атрибуты {@code systemAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code systemScAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code systemEditableAttrs} редактируемые</li>
     * <li>Проверить что атрибуты {@code systemAttrsViewableForFull} отсутствуют на карточке</li>
     * <li>Проверить что атрибуты {@code systemScAttrsViewableForFull} отсутствуют на карточке</li>
     * <li>Проверить, что атрибуты {@code userEditableAttrs} редактируемые</li>
     * <br>
     * <b>Действия и проверки 1. Для типа sc1 и набора прав FULL</b>
     * <li>Загрузить лицензию с полным набором прав доступа для нелиц. пользователей<li>
     * <li>Обновить страницу и перейти на карточку запроса sc1</li>
     * <li>Проверить, что на карточке запроса есть кнопка "Изменить статус"</li>
     * <li>Нажать на кнопку "Изменить статус"</li>
     * <li>Выбрать состояние "Закрыт" и нажать сохранить</li>
     * <li>Проверить, что запрос перешёл в состояние "Закрыт"</li>
     * <li>Проверить, что атрибуты {@code systemAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code systemScAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code systemEditableAttrs} редактируемые</li>
     * <li>Проверить что атрибуты {@code systemAttrsViewableForFull}
     *     видимые и нередактируемые</li>
     * <li>Проверить что атрибуты {@code systemScAttrsViewableForFull}
     *     видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code userEditableAttrs} редактируемые</li>
     * <li>Проверить, что на карточке запроса есть кнопка "Изменить статус"</li>
     * <li>Нажать на кнопку "Изменить статус"</li>
     * <li>Выбрать состояние "Возобновлён" и нажать сохранить</li>
     * <li>Проверить, что запрос перешёл в состояние "Возобновлён"</li>
     * <br>
     * <b>Действия и проверки 2. Для типа sc2 и набора прав FULL</b>
     * <li>Перейти на карточку запроса sc2</li>
     * <li>Проверить, что на карточке запроса есть кнопка "Изменить статус"</li>
     * <li>Нажать на кнопку "Изменить статус"</li>
     * <li>Выбрать состояние "Возобновлён" и нажать сохранить</li>
     * <li>Проверить, что запрос перешел в состояние "Возобновлён"</li>
     */
    @Test
    public void testScPermissionsUnlicUser()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus userStatus = DAOBoStatus.createUserStatus(scCase.getFqn());
        DSLBoStatus.add(userStatus);
        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());
        BoStatus resumed = DAOBoStatus.createResumed(scCase.getFqn());
        DSLBoStatus.setTransitions(registered, userStatus, closed, resumed);
        DSLBoStatus.setTransitions(userStatus, resumed);
        DSLBoStatus.setAttrInState(SysAttribute.codeOfClosing(scCase), closed,
                true, true, 0, 0);
        DSLBoStatus.setAttrInState(SysAttribute.closedBy(scCase), closed,
                true, true, 0, 0);

        /* Внимание! Для класса "Запрос" атрибут "Иконка" редактируем вопреки постановке!
        По сути это ошибка в первичной реализации прав доступа для нелиц. пользователя.
        Но так как это тянется аж с 2012 года, то это стало фичей и закрывать права
        мы не можем, т.к. эта особенность может использоваться заказчиком.
         */
        Attribute[] systemEditableAttrs = {
                SysAttribute.systemIcon(scCase)
        };
        ContentForm systemEditableAttrsPropList = createPropertyListWithAttrs(
                scCase, systemEditableAttrs, "Редактируемые системные");

        // Исключаем атрибут "Иконка", т.к. он редактируемый, его проверяем отдельно.
        Attribute[] systemAttrsViewableForAll = getSystemAttrsViewableForAll(
                scCase, true, SystemAttrEnum.SYSTEM_ICON.getCode());
        ContentForm systemAttrsViewableForAllPropList = createPropertyListWithAttrs(
                scCase, systemAttrsViewableForAll, "Системные видимые всем");

        Attribute[] systemScAttrsViewableForAll = {
                SysAttribute.registrationDate(scCase),
                SysAttribute.timeAllowanceTimer(scCase),
                SysAttribute.agreement(scCase),
                SysAttribute.service(scCase),
                SysAttribute.codeOfClosing(scCase)
        };
        ContentForm systemScAttrsViewableForAllPropList = createPropertyListWithAttrs(
                scCase, systemScAttrsViewableForAll,
                "Системные класса 'Запрос' видимые всем");

        Attribute[] systemAttrsViewableForFull = getSystemAttrsViewableForFull(
                scCase, true, true);
        ContentForm systemAttrsViewableForFullPropList = createPropertyListWithAttrs(
                scCase, systemAttrsViewableForFull,
                "Системные видимые только для FULL");

        Attribute[] systemScAttrsViewableForFull = {
                SysAttribute.impact(scCase),
                SysAttribute.massProblem(scCase),
                SysAttribute.masterMassProblem(scCase),
                SysAttribute.processingTimeTimer(scCase),
                SysAttribute.stateStartTime(scCase),
                SysAttribute.requestDate(scCase),
                SysAttribute.clientLinkName(scCase),
                SysAttribute.categories(scCase),
                SysAttribute.closedBy(scCase),
                SysAttribute.closedByEmployee(scCase),
                SysAttribute.closedByTeam(scCase),
                SysAttribute.solvedBy(scCase),
                SysAttribute.solvedByTeam(scCase),
                SysAttribute.solvedByEmployee(scCase),
                SysAttribute.serviceTime(scCase),
                SysAttribute.wfProfile(scCase),
                SysAttribute.client(scCase),
                SysAttribute.clientEmployee(scCase),
                SysAttribute.clientTeam(scCase),
                SysAttribute.clientOU(scCase),
                SysAttribute.resolutionTime(scCase),
                SysAttribute.massProblemSlaves(scCase),
                SysAttribute.priority(scCase),
                SysAttribute.deadLineTime(scCase),
                SysAttribute.startTime(scCase),
                SysAttribute.totalTimeTimer(scCase),
                SysAttribute.timeZone(scCase)
        };
        ContentForm systemScAttrsViewableForFullPropList = createPropertyListWithAttrs(
                scCase, systemScAttrsViewableForFull,
                "Системные класса 'Запрос' видимые только для FULL");

        Attribute[] userEditableAttrs = createAllUserAttributesExcludeUneditable(scCase);
        ContentForm userEditableAttrsPropList = createPropertyListWithAttrs(
                scCase, userEditableAttrs,
                "Пользовательские редактируемые (все)");

        DSLSecurityProfile.setRights(scCase, secProfile, AbstractBoRights.CHANGE_STATE,
                AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES,
                AbstractBoRights.EDIT_REST_ATTRIBUTES);

        Bo sc1 = DAOSc.create(scCase, employee, SharedFixture.agreement(),
                SharedFixture.timeZone());
        Bo sc2 = DAOSc.create(scCase, employee, SharedFixture.agreement(),
                SharedFixture.timeZone());
        DSLBo.add(sc1, sc2);
        DSLSc.changeState(sc1, userStatus);
        DSLSc.changeState(sc2, userStatus);

        // Действия и проверки 0. Для типа sc1 и набора прав SYSTEM
        GUILogon.login(employee);
        GUIBo.goToCard(sc1);

        DSLSc.assertState(sc1, userStatus);
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_CHANGE_STATE);

        assertViewableAttributes(systemAttrsViewableForAllPropList, systemAttrsViewableForAll);
        assertViewableAttributes(systemScAttrsViewableForAllPropList, systemScAttrsViewableForAll);
        assertEditableAttributes(systemEditableAttrsPropList, systemEditableAttrs);
        GUIContent.assertAbsence(systemAttrsViewableForFullPropList);
        GUIContent.assertAbsence(systemScAttrsViewableForFullPropList);
        assertEditableAttributes(userEditableAttrsPropList, userEditableAttrs);

        // Действия и проверки 1. Для типа sc1 и набора прав FULL
        DSLAdmin.installLicense(DSLAdmin.FULL_UNLIC_PERMISSION_SET_LICENSE);

        tester.refresh();
        GUIBo.goToCard(sc1);

        GUIButtonBar.assertPresent(GUIButtonBar.BTN_CHANGE_STATE);
        GUISc.changeState(closed);
        DSLSc.assertState(sc1, closed);

        assertViewableAttributes(systemAttrsViewableForAllPropList, systemAttrsViewableForAll);
        assertViewableAttributes(systemScAttrsViewableForAllPropList, systemScAttrsViewableForAll);
        assertEditableAttributes(systemEditableAttrsPropList, systemEditableAttrs);
        assertViewableAttributes(systemAttrsViewableForFullPropList, systemAttrsViewableForFull);
        assertViewableAttributes(systemScAttrsViewableForFullPropList, systemScAttrsViewableForFull);
        assertEditableAttributes(userEditableAttrsPropList, userEditableAttrs);

        GUIButtonBar.assertPresent(GUIButtonBar.BTN_CHANGE_STATE);
        GUISc.changeState(resumed);
        DSLSc.assertState(sc1, resumed);

        // Действия и проверки 2. Для типа sc2 и набора прав FULL
        GUIBo.goToCard(sc2);
        GUIButtonBar.assertPresent(GUIButtonBar.BTN_CHANGE_STATE);
        GUISc.changeState(resumed);
        DSLSc.assertState(sc2, resumed);
    }

    /**
     * Тестирование прав доступа нелицензированным пользователеям для пользовательского класса<br>
     * Проверка производится для наборов прав "system" и "full"<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00387
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}<li>
     * <li>Создать пользовательский класс userClass с жизненным циклом и
     *     возможностью назначения ответственного</li>
     * <li>Создать тип userCase для класса userClass</li>
     * <li>Создаём группу атрибутов {@code systemAttrsViewableForAll} с названием
     * "Системные видимые всем":
     *   <ul>
     *     <li>Иконка (system_icon)</li>
     *     <li>Название (title)</li>
     *     <li>Признак архивирования (removed)</li>
     *     <li>Тип объекта (metaClass)</li>
     *     <li>Уникальный идентификатор (UUID)</li>
     *     <li>[ЖЦ] Статус (state)</li>
     *   </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу атрибутов {@code systemAttrsViewableForFull} с названием
     * "Системные видимые только для FULL":
     * <ul>
     *   <li>Автор (author)</li>
     *   <li>Дата архивирования (removalDate)</li>
     *   <li>Дата изменения (lastModifiedDate)</li>
     *   <li>Дата создания (creationDate)</li>
     *   <li>Папки (folders)</li>
     *   <li>[ЖЦ] Время входа в статус (stateStartTime)</li>
     *   <li>[Ответственный] Время последнего изменения ответственных (responsibleStartTime)</li>
     *   <li>[Ответственный] Ответственный (responsible)</li>
     *   <li>[Ответственный] Ответственный (команда) (responsibleTeam)</li>
     *   <li>[Ответственный] Ответственный (сотрудник) (responsibleEmployee)</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу пользовательских атрибутов {@code userAttrsViewableForAll} с названием
     * "Пользовательские видимые (нередактируемые) для всех":
     * <ul>
     *   <li>Агрегирующий</li>
     *   <li>Ссылка на бизнес-объект</li>
     *   <li>Набор ссылок на БО</li>
     *   <li>Счётчик времени</li>
     *   <li>Счётчик времени (обратный)</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу пользовательских атрибутов {@code userAttrsEditableForAll} с названием
     * "Пользовательские редактируемые для всех":
     * <ul>
     *   <li>Временной интервал</li>
     *   <li>Дата</li>
     *   <li>Дата/Время</li>
     *   <li>Логический</li>
     *   <li>Строка</li>
     *   <li>Текст</li>
     *   <li>Целое число</li>
     *   <li>Элемент справочника</li>
     *   <li>Набор элементов справочника</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу пользовательских атрибутов {@code userAttrsEditableForFull} с названием
     * "Пользовательские редактируемые только для FULL":
     * <ul>
     *   <li>Вещественное число</li>
     *   <li>Гиперссылка</li>
     *   <li>Набор типов класса</li>
     *   <li>Текст в формате RTF</li>
     *   <li>Текст с подсветкой синтаксиса</li>
     *   <li>Файл</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Для профиля secProfile выставляем права:
     *     просмотр карточки объекта, смена статуса,
     *     просмотр атрибутов: Остальные атрибуты,
     *     редактирование атрибутов: Остальные атрибуты</li>
     * <li>Создаем объект bo типа userCase</li>
     * <br>
     * <b>Действия и проверки 1. Для набора прав SYSTEM</b>
     * <li>Зайти под сотрудником employee</li>
     * <li>Перейти на карточку объекта bo</li>
     * <li>Проверить, что атрибуты {@code systemAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить что атрибуты {@code systemAttrsViewableForFull} отсутствуют на карточке</li>
     * <li>Проверить, что атрибуты {@code userAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code userAttrsEditableForAll} редактируемые</li>
     * <li>Проверить, что атрибуты {@code userAttrsEditableForFull}
     *     видимые и нередактируемые</li>
     * <br>
     * <b>Действия и проверки 2.  Для набора прав FULL</b>
     * <li>Загрузить лицензию с полным набором прав доступа для нелиц. пользователей<li>
     * <li>Обновить страницу и перейти на карточку bo</li>
     * <li>Проверить, что атрибуты {@code systemAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить что атрибуты {@code systemAttrsViewableForFull}
     *     видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code userAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code userAttrsEditableForAll} редактируемые</li>
     * <li>Проверить, что атрибуты {@code userAttrsEditableForFull} редактируемые</li>
     */
    @Test
    public void testUserClassPermissionsUnlicUser()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Attribute[] systemAttrsViewableForAll = getSystemAttrsViewableForAll(
                userClass, true);
        ContentForm systemAttrsViewableForAllPropList = createPropertyListWithAttrs(
                userClass, systemAttrsViewableForAll, "Системные видимые всем");

        Attribute[] systemAttrsViewableForFull = getSystemAttrsViewableForFull(
                userClass, true, true);
        ContentForm systemAttrsViewableForFullPropList = createPropertyListWithAttrs(
                userClass, systemAttrsViewableForFull,
                "Системные видимые только для FULL");

        Attribute[] userAttrsViewableForAll = createUserAttrsViewableForAll(userClass, false);
        ContentForm userAttrsViewableForAllPropList = createPropertyListWithAttrs(
                userClass, userAttrsViewableForAll,
                "Пользовательские видимые (нередактируемые) для всех");

        Attribute[] userAttrsEditableForAll = createUserAttrsEditableForAll(userClass);
        ContentForm userAttrsEditableForAllPropList = createPropertyListWithAttrs(
                userClass, userAttrsEditableForAll,
                "Пользовательские редактируемые для всех");

        Attribute[] userAttrsEditableForFull = createUserAttrsEditableForFull(userClass);
        ContentForm userAttrsEditableForFullPropList = createPropertyListWithAttrs(
                userClass, userAttrsEditableForFull,
                "Пользовательские редактируемые только для FULL");

        DSLSecurityProfile.setRights(userClass, secProfile,
                AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES,
                AbstractBoRights.EDIT_REST_ATTRIBUTES);

        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);

        // Действия и проверки 1. Для набора прав SYSTEM
        GUILogon.login(employee);
        GUIBo.goToCard(bo);

        assertViewableAttributes(systemAttrsViewableForAllPropList, systemAttrsViewableForAll);
        GUIContent.assertAbsence(systemAttrsViewableForFullPropList);
        assertViewableAttributes(userAttrsViewableForAllPropList, userAttrsViewableForAll);
        assertEditableAttributes(userAttrsEditableForAllPropList, userAttrsEditableForAll);
        assertViewableAttributes(userAttrsEditableForFullPropList, userAttrsEditableForFull);

        // Действия и проверки 2.  Для набора прав FULL
        DSLAdmin.installLicense(DSLAdmin.FULL_UNLIC_PERMISSION_SET_LICENSE);

        tester.refresh();
        GUIBo.goToCard(bo);

        assertViewableAttributes(systemAttrsViewableForAllPropList, systemAttrsViewableForAll);
        assertViewableAttributes(systemAttrsViewableForFullPropList, systemAttrsViewableForFull);
        assertViewableAttributes(userAttrsViewableForAllPropList, userAttrsViewableForAll);
        assertEditableAttributes(userAttrsEditableForAllPropList, userAttrsEditableForAll);
        assertEditableAttributes(userAttrsEditableForFullPropList, userAttrsEditableForFull);
    }

    /**
     * Тестирование прав доступа нелицензированным пользователеям для класса "Голосование"<br>
     * Проверка производится для наборов прав "system" и "full"<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00387
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}<li>
     * <li>Создать пользовательский класс {@code userClass} с кодом 'vote'</li>
     * <li>Создать тип {@code userCase} для класса {@code userClass}</li>
     * <li>Создаём группу атрибутов {@code systemEditableAttrs} с названием "Редактируемые системные":
     *     <ul>
     *       <li>Иконка (system_icon)</li>
     *       <li>Номер (number)</li>
     *     </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу атрибутов {@code systemViewableAttrs} с названием "Системные видимые всем":
     *   <ul>
     *     <li>Название (title)</li>
     *     <li>Признак архивирования (removed)</li>
     *     <li>Тип объекта (metaClass)</li>
     *     <li>Уникальный идентификатор (UUID)</li>
     *     <li>[ЖЦ] Статус (state)</li>
     *   </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу атрибутов {@code systemViewableAttrsFULL} с названием
     * "Системные видимые только для набора прав FULL":
     * <ul>
     *   <li>Автор (author)</li>
     *   <li>Дата архивирования (removalDate)</li>
     *   <li>Дата изменения (lastModifiedDate)</li>
     *   <li>Дата создания (creationDate)</li>
     *   <li>Папки (folders)</li>
     *   <li>[ЖЦ] Время входа в статус (stateStartTime)</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу пользовательских атрибутов {@code editableUserAttributes} с названием
     * "Редактируемые пользовательские (все)":
     * <ul>
     *   <li>Временной интервал</li>
     *   <li>Дата</li>
     *   <li>Дата/Время</li>
     *   <li>Логический</li>
     *   <li>Строка</li>
     *   <li>Текст</li>
     *   <li>Целое число</li>
     *   <li>Элемент справочника</li>
     *   <li>Набор элементов справочника</li>
     *   <li>Вещественное число</li>
     *   <li>Гиперссылка</li>
     *   <li>Набор типов класса</li>
     *   <li>Текст в формате RTF</li>
     *   <li>Текст с подсветкой синтаксиса</li>
     *   <li>Файл</li>
     *   <li>Агрегирующий</li>
     *   <li>Ссылка на бизнес-объект</li>
     *   <li>Набор ссылок на БО</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Для профиля secProfile выставляем права:
     *     просмотр карточки объекта,
     *     просмотр атрибутов: Остальные атрибуты,
     *     редактирование атрибутов: Остальные атрибуты</li>
     * <li>Создаем объект {@code bo} типа {@code userCase}</li>
     * <br>
     * <b>Действия и проверки 1. Для набора прав SYSTEM</b>
     * <li>Зайти под сотрудником {@code employee}</li>
     * <li>Перейти на карточку объекта {@code bo}</li>
     * <li>Проверить, что атрибуты {@code systemViewableAttrs}
     *     видимые и нередактируемые</li>
     * <li>Проверить что атрибуты {@code systemViewableAttrsFULL} отсутствуют на карточке</li>
     * <li>Проверить, что атрибуты {@code systemEditableAttrs} редактируемые</li>
     * <li>Проверить, что атрибуты {@code editableUserAttributes} редактируемые</li>
     * <br>
     * <b>Действия и проверки 2.  Для набора прав FULL</b>
     * <li>Загрузить лицензию с полным набором прав доступа для нелиц. пользователей</li>
     * <li>Обновить страницу и перейти на карточку {@code bo}</li>
     * <li>Проверить, что атрибуты {@code systemViewableAttrs} видимые и нередактируемые</li>
     * <li>Проверить что атрибуты {@code systemViewableAttrsFULL} видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code systemEditableAttrs} редактируемые</li>
     * <li>Проверить, что атрибуты {@code editableUserAttributes} редактируемые</li>
     * </ol>
     */
    @Test
    public void testVotePermissionsUnlicUser()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        userClass.setTitle("Голосование");
        userClass.setFqn("vote");
        userClass.setCode("vote");
        // Проверки
        assertSpecialSystemAndEditableAllUserAttributes(userClass);
    }

    /**
     * Тестирование прав доступа нелицензированным пользователеям класса
     * 'Подсказки по элементам интерфейса (туры)'<br>
     * Проверка производится для наборов прав "system" и "full"<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00387
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}<li>
     * <li>Создать пользовательский класс {@code interfaceTour}</li>
     * <li>Создать тип userCase для класса {@code interfaceTour}</li>
     * <li>Создаём группу атрибутов {@code systemAttrsViewableForAll} с названием
     * "Системные видимые всем":
     *   <ul>
     *     <li>Иконка (system_icon)</li>
     *     <li>Название (title)</li>
     *     <li>Признак архивирования (removed)</li>
     *     <li>Тип объекта (metaClass)</li>
     *     <li>Уникальный идентификатор (UUID)</li>
     *     <li>[ЖЦ] Статус (state)</li>
     *   </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу атрибутов {@code systemAttrsViewableForFull} с названием
     * "Системные видимые только для FULL":
     * <ul>
     *   <li>Автор (author)</li>
     *   <li>Дата архивирования (removalDate)</li>
     *   <li>Дата изменения (lastModifiedDate)</li>
     *   <li>Дата создания (creationDate)</li>
     *   <li>Папки (folders)</li>
     *   <li>[ЖЦ] Время входа в статус (stateStartTime)</li>
     *   <li>[Ответственный] Время последнего изменения ответственных (responsibleStartTime)</li>
     *   <li>[Ответственный] Ответственный (responsible)</li>
     *   <li>[Ответственный] Ответственный (команда) (responsibleTeam)</li>
     *   <li>[Ответственный] Ответственный (сотрудник) (responsibleEmployee)</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу пользовательских атрибутов {@code userAttrsViewableForAll} с названием
     * "Пользовательские видимые (нередактируемые) для всех":
     * <ul>
     *   <li>Агрегирующий</li>
     *   <li>Ссылка на бизнес-объект</li>
     *   <li>Набор ссылок на БО</li>
     *   <li>Счётчик времени</li>
     *   <li>Счётчик времени (обратный)</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу пользовательских атрибутов {@code userAttrsEditableForAll} с названием
     * "Пользовательские редактируемые для всех":
     * <ul>
     *   <li>Временной интервал</li>
     *   <li>Дата</li>
     *   <li>Дата/Время</li>
     *   <li>Логический</li>
     *   <li>Строка</li>
     *   <li>Текст</li>
     *   <li>Целое число</li>
     *   <li>Элемент справочника</li>
     *   <li>Набор элементов справочника</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём группу пользовательских атрибутов {@code userAttrsEditableForFull} с названием
     * "Пользовательские редактируемые только для FULL":
     * <ul>
     *   <li>Вещественное число</li>
     *   <li>Гиперссылка</li>
     *   <li>Набор типов класса</li>
     *   <li>Текст в формате RTF</li>
     *   <li>Текст с подсветкой синтаксиса</li>
     *   <li>Файл</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Создаём атрибут {@code directLink} типа "Ссылка на БО" в классе "Сотрудник"</li>
     * <li>Создаём атрибут {@code disabledUsers} типа "Набор ссылок на БО"
     *     в классе {@code interfaceTour}</li>
     * <li>Создаём атрибут {@code suspendedUsers} типа "Обратная ссылка"
     *     для ссылки {@code directLink} в классе {@code interfaceTour}</li>
     * <li>Создаём группу пользовательских атрибутов {@code userSpecialAttrsEditable} с названием
     * "Пользовательские специальные для класса interfaceTour":
     * <ul>
     *   <li>Отключен у пользователей (disabledUsers)</li>
     *   <li>Приостановлен у пользователей (suspendedUsers)</li>
     * </ul>
     * <li>Добавляем группу на карточку класса</li>
     * <li>Для профиля secProfile выставляем права:
     *     просмотр карточки объекта, смена статуса,
     *     просмотр атрибутов: Остальные атрибуты,
     *     редактирование атрибутов: Остальные атрибуты</li>
     * <li>Создаем объект bo типа userCase</li>
     * <br>
     * <b>Действия и проверки 1. Для набора прав SYSTEM</b>
     * <li>Зайти под сотрудником employee</li>
     * <li>Перейти на карточку объекта bo</li>
     * <li>Проверить, что атрибуты {@code systemAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить что атрибуты {@code systemAttrsViewableForFull} отсутствуют на карточке</li>
     * <li>Проверить, что атрибуты {@code userAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code userAttrsEditableForAll} редактируемые</li>
     * <li>Проверить, что атрибуты {@code userAttrsEditableForFull}
     *     видимые и нередактируемые</li>
     * <br>
     * <b>Действия и проверки 2.  Для набора прав FULL</b>
     * <li>Загрузить лицензию с полным набором прав доступа для нелиц. пользователей<li>
     * <li>Обновить страницу и перейти на карточку bo</li>
     * <li>Проверить, что атрибуты {@code systemAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить что атрибуты {@code systemAttrsViewableForFull}
     *     видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code userAttrsViewableForAll}
     *     видимые и нередактируемые</li>
     * <li>Проверить, что атрибуты {@code userAttrsEditableForAll} редактируемые</li>
     * <li>Проверить, что атрибуты {@code userAttrsEditableForFull} редактируемые</li>
     */
    @Test
    public void testInterfaceTourPermissionsUnlicUser()
    {
        // Подготовка
        MetaClass interfaceTour = DAOUserClass.create();
        interfaceTour.setTitle("Подсказки по элементам интерфейса (туры)");
        interfaceTour.setFqn("interfaceTour");
        interfaceTour.setCode("interfaceTour");
        MetaClass interfaceTourCase = DAOUserCase.create(interfaceTour, "serviceCall");
        DSLMetaClass.add(interfaceTour, interfaceTourCase);

        Attribute[] systemAttrsViewableForAll = getSystemAttrsViewableForAll(
                interfaceTour, false);
        ContentForm systemAttrsViewableForAllPropList = createPropertyListWithAttrs(
                interfaceTour, systemAttrsViewableForAll, "Системные видимые всем");

        Attribute[] systemAttrsViewableForFull = getSystemAttrsViewableForFull(
                interfaceTour, false, false);
        ContentForm systemAttrsViewableForFullPropList = createPropertyListWithAttrs(
                interfaceTour, systemAttrsViewableForFull,
                "Системные видимые только для FULL");

        Attribute[] userAttrsViewableForAll = createUserAttrsViewableForAll(
                interfaceTour, true);
        ContentForm userAttrsViewableForAllPropList = createPropertyListWithAttrs(
                interfaceTour, userAttrsViewableForAll,
                "Пользовательские видимые (нередактируемые) для всех");

        Attribute[] userAttrsEditableForAll = createUserAttrsEditableForAll(interfaceTour);
        ContentForm userAttrsEditableForAllPropList = createPropertyListWithAttrs(
                interfaceTour, userAttrsEditableForAll,
                "Пользовательские редактируемые для всех");

        Attribute[] userAttrsEditableForFull = createUserAttrsEditableForFull(interfaceTour);
        ContentForm userAttrsEditableForFullPropList = createPropertyListWithAttrs(
                interfaceTour, userAttrsEditableForFull,
                "Пользовательские редактируемые только для FULL");

        MetaClass employeeClass = DAOEmployeeCase.createClass();
        Attribute disabledUsers = DAOAttribute.createBoLinks(interfaceTour, employeeClass);
        disabledUsers.setCode("disabledUsers");
        disabledUsers.setTitle("Отключен у пользователей");

        Attribute directLink = DAOAttribute.createObjectLink(
                employeeClass, interfaceTour, null);
        Attribute suspendedUsers = DAOAttribute.createBackBOLinks(
                interfaceTour, directLink);
        suspendedUsers.setCode("suspendedUsers");
        suspendedUsers.setTitle("Приостановлен у пользователей");
        DSLAttribute.add(disabledUsers, directLink, suspendedUsers);
        Attribute[] userSpecialAttrsEditable = {
                disabledUsers, suspendedUsers
        };
        ContentForm userSpecialAttrsEditablePropList = createPropertyListWithAttrs(
                interfaceTour, userSpecialAttrsEditable,
                "Пользовательские специальные для класса interfaceTour");

        DSLSecurityProfile.setRights(interfaceTour, secProfile,
                AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES,
                AbstractBoRights.EDIT_REST_ATTRIBUTES);

        Bo bo = DAOUserBo.create(interfaceTourCase);
        DSLBo.add(bo);

        // Действия и проверки 1. Для набора прав SYSTEM
        GUILogon.login(UnlicFullPermissionSetTest.employee);
        GUIBo.goToCard(bo);

        assertViewableAttributes(systemAttrsViewableForAllPropList, systemAttrsViewableForAll);
        GUIContent.assertAbsence(systemAttrsViewableForFullPropList);
        assertViewableAttributes(userAttrsViewableForAllPropList, userAttrsViewableForAll);
        assertEditableAttributes(userAttrsEditableForAllPropList, userAttrsEditableForAll);
        assertViewableAttributes(userAttrsEditableForFullPropList, userAttrsEditableForFull);
        assertEditableAttributes(userSpecialAttrsEditablePropList, userSpecialAttrsEditable);

        // Действия и проверки 2.  Для набора прав FULL
        DSLAdmin.installLicense(DSLAdmin.FULL_UNLIC_PERMISSION_SET_LICENSE);

        tester.refresh();
        GUIBo.goToCard(bo);

        assertViewableAttributes(systemAttrsViewableForAllPropList, systemAttrsViewableForAll);
        assertViewableAttributes(systemAttrsViewableForFullPropList, systemAttrsViewableForFull);
        assertViewableAttributes(userAttrsViewableForAllPropList, userAttrsViewableForAll);
        assertEditableAttributes(userAttrsEditableForAllPropList, userAttrsEditableForAll);
        assertEditableAttributes(userAttrsEditableForFullPropList, userAttrsEditableForFull);
        assertEditableAttributes(userSpecialAttrsEditablePropList, userSpecialAttrsEditable);
    }

    /**
     * Проверить что системные атрибуты отвечают базовым настройкам видимости
     * и особым для редактирования (редактируемые атрибуты: "Иконка", "Номер"),
     * и что все пользовательские атрибуты доступны для редактирования нелиц. пользователю.
     * Создаёт тип для класса и тестируемых групп атрибутов в классе.
     * Применяется только для специфически настроенных классов типа Голосование и Анкета.
     * @param metaClass тестируемый класс
     */
    private void assertSpecialSystemAndEditableAllUserAttributes(MetaClass metaClass)
    {
        MetaClass userCase = DAOUserCase.create(metaClass);
        DSLMetaClass.add(metaClass, userCase);

        /* Внимание! Для классов "Анкета" и "Голосование"
           атрибуты "Иконка" и "Номер" редактируемы вопреки постановке!
        По сути это ошибка в первичной реализации прав доступа для нелиц. пользователя.
        Но так как это тянется аж с 2012 года, то это стало фичей и закрывать права
        мы не можем, т.к. эта особенность может использоваться заказчиком.
         */
        Attribute[] systemEditableAttrs = {
                SysAttribute.systemIcon(metaClass),
                SysAttribute.number(metaClass)
        };
        ContentForm systemEditableAttrsPropList = createPropertyListWithAttrs(
                metaClass, systemEditableAttrs, "Редактируемые системные");

        // Системные видимые всем
        // Исключаем атрибуты "Иконка" и "Номер", т.к. они редактируемые, их проверяем отдельно.
        Attribute[] systemViewableAttrs = getSystemAttrsViewableForAll(
                metaClass, true, SystemAttrEnum.SYSTEM_ICON.getCode(),
                SystemAttrEnum.NUMBER.getCode());
        ContentForm systemViewablePropertyList = createPropertyListWithAttrs(
                metaClass, systemViewableAttrs, "Системные видимые всем");

        // Системные видимые только для набора прав FULL
        Attribute[] systemViewableAttrsFULL = getSystemAttrsViewableForFull(
                metaClass, true, false);
        ContentForm systemViewableFULLPropertyList = createPropertyListWithAttrs(
                metaClass, systemViewableAttrsFULL,
                "Системные видимые только для набора прав FULL");

        // Редактируемые пользовательские (все)
        Attribute[] editableUserAttributes = createAllUserAttributesExcludeUneditable(metaClass);
        ContentForm editableUserPropertyList = createPropertyListWithAttrs(
                metaClass, editableUserAttributes,
                "Редактируемые пользовательские (все)");

        DSLSecurityProfile.setRights(metaClass, secProfile,
                AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES,
                AbstractBoRights.EDIT_REST_ATTRIBUTES);

        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);

        // Действия и проверки 1. Для набора прав SYSTEM
        GUILogon.login(employee);
        GUIBo.goToCard(bo);
        assertViewableAttributes(systemViewablePropertyList, systemViewableAttrs);
        GUIContent.assertAbsence(systemViewableFULLPropertyList);
        assertEditableAttributes(systemEditableAttrsPropList, systemEditableAttrs);
        assertEditableAttributes(editableUserPropertyList, editableUserAttributes);

        // Действия и проверки 2.  Для набора прав FULL
        DSLAdmin.installLicense(DSLAdmin.FULL_UNLIC_PERMISSION_SET_LICENSE);

        tester.refresh();
        GUIBo.goToCard(bo);
        // После установки расширенной лицензии должны отобразиться новые системные,
        // а пользовательские атрибуты должны быть по прежнему редактирумыми
        assertViewableAttributes(systemViewablePropertyList, systemViewableAttrs);
        assertViewableAttributes(systemViewableFULLPropertyList, systemViewableAttrsFULL);
        assertEditableAttributes(systemEditableAttrsPropList, systemEditableAttrs);
        assertEditableAttributes(editableUserPropertyList, editableUserAttributes);
    }

    /**
     * Создать модели пользовательских атрибутов всех типов, исключив те,
     * которые не могут быть редактируемыми
     */
    private Attribute[] createAllUserAttributesExcludeUneditable(MetaClass metaClass)
    {
        return Stream.of(createUserAttrsEditableForAll(metaClass),
                createUserAttrsEditableForFull(metaClass),
                // Исключаем из набора атрибутов нередактируемые атрибуты:
                // Счётчик времени, Счётчик времени (обратный)
                createUserAttrsViewableForAll(metaClass, true))
                .flatMap(Arrays::stream)
                .toArray(Attribute[]::new);
    }

    /**
     * Возвращает модели системных атрибутов, видимые для всех
     * <ul>
     *   <li>Иконка (system_icon)</li>
     *   <li>Название (title)</li>
     *   <li>Номер (number)</li>
     *   <li>Признак архивирования (removed)</li>
     *   <li>Тип объекта (metaClass)</li>
     *   <li>Уникальный идентификатор (UUID)</li>
     *   <li>[ЖЦ] Статус (state)</li>
     * </ul>
     * @param includeWorkFlow добавить атрибуты, которые принадлежат классу с жизненным циклом
     * @param exclude коды атрибутов, которые нужно исключить из результата
     */
    private Attribute[] getSystemAttrsViewableForAll(MetaClass metaClass,
            boolean includeWorkFlow, String... exclude)
    {
        Builder<Attribute> builder = Stream.<Attribute> builder()
                .add(SysAttribute.systemIcon(metaClass))
                .add(SysAttribute.title(metaClass))
                .add(SysAttribute.number(metaClass))
                .add(SysAttribute.removed(metaClass))
                .add(SysAttribute.metaClass(metaClass))
                .add(SysAttribute.uuid(metaClass));
        // Принадлежит ЖЦ
        if (includeWorkFlow)
        {
            builder.add(SysAttribute.state(metaClass));
        }
        Stream<Attribute> stream = builder.build();
        if (exclude.length > 0)
        {
            List<String> codesToExclude = Arrays.asList(exclude);
            stream = stream.filter(attribute ->
                    !codesToExclude.contains(attribute.getCode()));
        }
        return stream.toArray(Attribute[]::new);
    }

    /**
     * Возвращает модели системных атрибутов, видимые для набора прав FULL
     * <ul>
     *   <li>Автор (author)</li>
     *   <li>Дата архивирования (removalDate)</li>
     *   <li>Дата изменения (lastModifiedDate)</li>
     *   <li>Дата создания (creationDate)</li>
     *   <li>Папки (folders)</li>
     *   <li>[ЖЦ] Время входа в статус (stateStartTime)</li>
     *   <li>[Ответственный] Время последнего изменения ответственных (responsibleStartTime)</li>
     *   <li>[Ответственный] Ответственный (responsible)</li>
     *   <li>[Ответственный] Ответственный (команда) (responsibleTeam)</li>
     *   <li>[Ответственный] Ответственный (сотрудник) (responsibleEmployee)</li>
     * </ul>
     * @param includeWorkFlow добавить атрибуты, которые принадлежат классу с жизненным циклом
     * @param includeResponsible добавить атрибуты, которые принадлежат классу с назначением отвественного
     */
    private Attribute[] getSystemAttrsViewableForFull(MetaClass metaClass,
            boolean includeWorkFlow, boolean includeResponsible)
    {
        Builder<Attribute> builder = Stream.<Attribute> builder()
                .add(SysAttribute.author(metaClass))
                .add(SysAttribute.removalDate(metaClass))
                .add(SysAttribute.lastModifiedDate(metaClass))
                .add(SysAttribute.creationDate(metaClass))
                .add(SysAttribute.folders(metaClass));
        // Принадлежит ЖЦ
        if (includeWorkFlow)
        {
            builder.add(SysAttribute.stateStartTime(metaClass));
        }
        // Принадлежит классу с назначением отвественного
        if (includeResponsible)
        {
            boolean isServiceCall = metaClass.getParentFqn().equals(SystemClass.SERVICECALL.getCode());
            Attribute responsibleEmployee = SysAttribute.responsibleEmployee(metaClass);
            Attribute responsibleTeam = SysAttribute.responsibleTeam(metaClass);
            if (!isServiceCall)
            {
                /* По какому-то непонятному стечению обстоятельств у нас в системе используется
                   разные регистры первых букв у слов сотрудник и команда для класса "Запрос" и остальных*/
                // Меняем первую букву слова "сотрудник" на заглавную
                responsibleEmployee.setTitle("Ответственный (Сотрудник)");
                // Меняем первую букву слова "команда" на заглавную
                responsibleTeam.setTitle("Ответственный (Команда)");
            }
            builder.add(isServiceCall
                    ? SysAttribute.responsibleStartTimeSc(metaClass)
                    : SysAttribute.responsibleStartTimeUserClass(metaClass))
                    .add(SysAttribute.responsible(metaClass))
                    .add(responsibleTeam)
                    .add(responsibleEmployee);
        }
        return builder.build().toArray(Attribute[]::new);
    }
}