package ru.naumen.selenium.cases.admin.catalogs.system;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.DoubleType;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;

/**
 * Тестирование элементов системных справочников Типы значения динамического поля и "Представления динамических полей"
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00979
 *
 * <AUTHOR>
 * @since 11.10.2022
 */
public class DynamicFieldsCatalogItem1Test extends AbstractTestCase
{
    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Загрузить лицензию с поддержкой динамических полей</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.DYNAMIC_FIELD_LICENSE);
    }

    /**
     * Тестирование отображения папок для элемента справочника "Представления динамических полей"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00979
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$184682047
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти в справочник "Представления динамических полей"</li>
     * <li>Проверить, что существует папка типа атрибута "Вещественное число"</li>
     * <li>Проверить, что существует папка Отображение для типа атрибута "Вещественное число"</li>
     * <li>Проверить, что существует папка Редактирование для типа атрибута "Вещественное число"</li>
     * </ol>
     */
    @Test
    public void testAddFoldersForFieldPresentationCatalogItem()
    {
        //Выполнение действия и проверки
        CatalogItem doubleFolder = DAOCatalogItem.createSystemFolder("Вещественное число", "double",
                SystemCatalog.FIELD_PRESENTATION_TYPES.getCode());
        CatalogItem doubleShow = DAOCatalogItem.createSystemFolder("Отображение", "doubleShow",
                SystemCatalog.FIELD_PRESENTATION_TYPES.getCode());
        CatalogItem doubleEditing = DAOCatalogItem.createSystemFolder("Редактирование", "doubleEditing",
                SystemCatalog.FIELD_PRESENTATION_TYPES.getCode());

        GUILogon.asSuper();
        GUICatalog.goToCard(SystemCatalog.FIELD_PRESENTATION_TYPES.getCode());
        GUICatalogItem.assertFolderPresent(doubleFolder);
        GUICatalogItem.assertFolderPresent(doubleShow);
        GUICatalogItem.assertFolderPresent(doubleEditing);
    }

    /**
     * Тестирование редактирования элемента справочника "Представления динамических полей"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00979
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$194423665
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти в справочник "Представления динамических полей"</li>
     * <li>Открыть форму редактирования для элемента справочника "Вещественное число"</li>
     * <li>Сменить название и сохранить форму</li>
     * <li>Проверить, что элемент справочника остался вложен в папку</li>
     * </ol>
     */
    @Test
    public void testEditFieldPresentationCatalogItem()
    {
        //Выполнение действия и проверки
        CatalogItem doubleFolder = DAOCatalogItem.createSystemFolder("Вещественное число", "double",
                SystemCatalog.FIELD_PRESENTATION_TYPES.getCode());

        GUILogon.asSuper();
        GUICatalog.goToCard(SystemCatalog.FIELD_PRESENTATION_TYPES.getCode());
        GUICatalog.clickEdit(doubleFolder);
        String title = ModelUtils.createTitle();
        doubleFolder.setTitle(title);
        GUICatalogItem.setTitle(title);
        GUIForm.applyModalForm();

        Cleaner.afterTest(true, () ->
        {
            doubleFolder.setUuid(DSLCatalogItem.getUuid(DAOCatalog.createSystem(SystemCatalog.FIELD_PRESENTATION_TYPES),
                    doubleFolder.getCode()));
            doubleFolder.setTitle("Вещественное число");
            DSLCatalogItem.edit(doubleFolder);
        });

        Assert.assertTrue("Элемент справочника не вложен в папку",
                tester.isPresence(
                        String.format("//a[contains (@id,'%s.%s.color') and contains (@style,'margin-left:20px')]",
                DoubleType.VIEW, SystemCatalog.FIELD_PRESENTATION_TYPES.getCode())));
    }
}