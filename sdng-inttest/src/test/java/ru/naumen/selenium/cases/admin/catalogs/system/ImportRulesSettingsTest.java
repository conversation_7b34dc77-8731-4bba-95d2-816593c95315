package ru.naumen.selenium.cases.admin.catalogs.system;

import java.io.File;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.openqa.selenium.Keys;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.advimport.GUIAdvimportList;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUIRulesSettings;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.WaitTool;

/**
 * Тестирования работы импорта таблиц соответствий
 * <AUTHOR>
 * @since 04/07/2019
 */
public class ImportRulesSettingsTest extends AbstractTestCase
{
    CatalogItem userFolder, rulesSettings;
    Attribute intAttr, strAttr;
    MetaClass userClass, userCase;

    /**
     * Общий блок (действия, выполняемые перед каждым тестом)
     * <ol>
     * <li>Создать класс userClass, добавить тип userCase</li>
     * <li>В классе userClass добавить атрибуты numbAttr  - тип целое число; strAttr - тип строка</li>
     * <li>В разделе Системные справочники -> Таблицы соответствий добавить новую папку userFolder</li>
     * <li> Добавить новый элемент:
     * <pre>
     * Родитель - userFolder
     * Объекты - userClass
     * Определяемые атрибуты - strAttr
     * Определяющие атрибуты - numbAttr
     * </pre>
     * </li>
     * </ol>
     */
    @Before
    public void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        intAttr = DAOAttribute.createInteger(userClass.getFqn());
        strAttr = DAOAttribute.createString(userClass.getFqn());
        DSLAttribute.add(intAttr, strAttr);

        Catalog userCatalogType = DAOCatalog.createSystem(SystemCatalog.RULESSETTINGS);
        userFolder = DAOCatalogItem.createUserFolder(userCatalogType);
        DSLCatalogItem.add(userFolder);

        rulesSettings = DAOCatalogItem.createRulesSettings(userClass, strAttr, intAttr);
        rulesSettings.setParentUuid(userFolder.getUuid());
        DSLCatalogItem.add(rulesSettings);
    }

    /**
     *  Тестирование создания правила импорта при создании Таблицы Соответствий
     *  https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00844
     *  https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$72202726
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Перейти на карточку созданной Таблицы Соответствий. Проверить, что на карточке присутствует вкладка "Правило импорта"</li>
     * <li>Перейти в раздел Настройки системы -> Синхронизация.
     *  Проверить, что в списке присутствует конфигурация с кодом ранее созданной таблицы соответствий</li>
     * </ol>
     */
    @Test
    public void testCreateImportRuleWhenCreatingRulesSettings()
    {
        //Действия и проверки
        GUILogon.asSuper();

        GUICatalog.goToCatalogRulesSettings();
        GUICatalogItem.goToCard(rulesSettings);

        GUITab.assertTabPresent(GUITab.IMPORT_CONFIG_TAB);

        GUINavigational.goToAdvimport();
        GUIAdvimportList.advlistConfig().content().asserts().rowsPresence(rulesSettings);
    }

    /**
     *  Тестирование использования кнопок "Удалить" и "Удалить историю" в блоке "История синхронизаций"
     *  https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00844
     *  https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$72202726
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Перейти на карточку созданной Таблицы Соответствий testTable. Кликнуть на кнопку "Выгрузить"</li>
     * <li>Затем кликнуть на кнопку "Загрузить". Прикрепить выгруженный архив. Параметр Заменить настройки объекта = false. Повторить загрузку еще два раза</li>
     * <li>Перейти на вкладку "История". Проверить, что в блоке "История синхронизаций" присутствуют три записи</li>
     * <li>Кликнуть на кнопку "Удалить" в строке с любой записью. Проверить, что появилось сообщение "Вы действительно хотите удалить Файл 'ImportLog.txt'?" Нажать Да</li>
     * <li>Проверить, что запись удалилась из Истории синхронизаций</li>
     * <li>Кликнуть на кнопку "Удалить историю". Проверить, что появилось сообщение "Вы действительно хотите удалить всю историю импорта?" Нажать Да</li>
     * <li>Проверить, что в Истории синхронизаций не осталось записей</li>
     * </ol>
     */
    @Test
    public void testDeleteAndHistoryDeleteInSyncHistory()
    {
        //Действия и проверки
        GUILogon.asSuper();

        GUICatalog.goToCatalogRulesSettings();
        GUICatalogItem.goToCard(rulesSettings);
        File exportedRS = GUIRulesSettings.exportRS(rulesSettings);

        GUIRulesSettings.importRS(exportedRS, false);
        GUIRulesSettings.importRS(exportedRS, false);
        GUIRulesSettings.importRS(exportedRS, false);

        GUIRulesSettings.goToRulesSettingsCard(GUITab.IMPORT_HISTORY_TAB);
        GUIRulesSettings.assertHistoryListCount(3);

        GUIRulesSettings.clickSyncHistoryElem(0);
        GUIForm.assertQuestionAppear(String.format(ConfirmMessages.DELETE_FILE, "ImportLog.txt"));
        GUIForm.clickYes();

        GUIRulesSettings.assertHistoryListCount(2);

        tester.click(Div.ANY + GUIXpath.Any.BUTTON_TOOLBAR, GUIRulesSettings.SYNC_HISTORY);
        GUIForm.assertQuestionAppear(ConfirmMessages.DELETE_FULL_HISTORY);
        GUIForm.clickYes();

        Assert.assertTrue("После очистки истории синхронизаций не все элементы были удалены", tester.waitDisappear(
                WaitTool.WAIT_TIME_APPEAR, GUICatalogItem.HISTORY_TAB_ADVLIST_ELEMS, GUIRulesSettings.SYNC_HISTORY));
    }

    /**
     *  Тестирование присутствия проверки на совпадение родителя при импорте Таблицы Соответствий с параметром "Заменить настройки объекта" = false
     *  https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00844
     *  https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$72202726
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Перейти на карточку таблицы соответствий testTable, кликнуть на кнопку "Выгрузить". Сохранить выгруженный архив</li>
     * <li>Удалить созданную Таблицу Соответствий</li>
     * <li>Создать Таблицу Соответствий с таким же кодом и такими же параметрами, но не вкладывая в папку</li>
     * <li>Перейти на карточку таблицы соответствий testTable, кликнуть на кнопку "Загрузить". Прикрепить ранее сохраненный файл.
     *  "Заменить настройки объекта" = false. Кликнуть на кнопку "Сохранить"</li>
     * <li>Проверить, что на форме появилось сообщение "Изменение значения атрибута 'Ссылка на родительский элемент' запрещено!"</li>
     * </ol>
     */
    @Test
    public void testExistCheckParentCoincedenceUntilRulesSettingsImport()
    {
        //Действия и проверки
        GUILogon.asSuper();

        GUICatalog.goToCatalogRulesSettings();
        GUICatalogItem.goToCard(rulesSettings);
        File exportedRS = GUIRulesSettings.exportRS(rulesSettings);

        DSLCatalogItem.delete(rulesSettings);

        rulesSettings.setParentUuid(null);
        DSLCatalogItem.add(rulesSettings);

        GUICatalogItem.goToCard(rulesSettings);
        GUIRulesSettings.importRSExpectError(exportedRS, false, ConfirmMessages.CHANGE_ATTRIB_FORBIDDEN);
    }

    /**
     *  Тестирование импорта Таблицы Соответствий, которая используется в атрибуте как правило определения
     *  https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00844
     *  https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$72202726
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В созданном пользовательском классе открыть форму редактирования атрибута strAttr,
     *  установить параметр "Определяемый по Таблице соответствий" = true. В правиле определения выбрать ранее созданную таблицу соответствий.</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Перейти на карточку созданной Таблицы Соответствий. Кликнуть на кнопку "Выгрузить"</li>
     * <li>Кликнуть на кнопку "Загрузить". Прикрепить выгруженный архив. Параметр Заменить настройки объекта = false.
     *  Проверить, что таблица загрузилась, никаких ошибок не возникло</li>
     * <li>Еще раз кликнуть на кнопку "Загрузить". Прикрепить выгруженный архив. Параметр Заменить настройки объекта = true</li>
     * <li>Проверить, что появилось информационное сообщение о невозможности замещения</li>
     * </ol>
     */
    @Test
    public void testImportMathesTableWhichUsedAsRuleOfDefinision()
    {
        //удаляем после теста папку, в которую вложены таблицы соответствий
        Cleaner.afterTest(true, () ->
        {
            DSLAttribute.editModelDeterminable(strAttr, false, rulesSettings);
            DSLAttribute.edit(strAttr);
            DSLCatalogItem.delete(userFolder, rulesSettings);
        });

        //Действия и проверки
        DSLAttribute.editModelDeterminable(strAttr, true, rulesSettings);
        DSLAttribute.edit(strAttr);

        GUILogon.asSuper();

        GUICatalog.goToCatalogRulesSettings();
        GUICatalogItem.goToCard(rulesSettings);
        File exportedRS = GUIRulesSettings.exportRS(rulesSettings);

        GUIRulesSettings.importRS(exportedRS, false);

        GUIRulesSettings.importRSExpectError(exportedRS, true, String.format(ErrorMessages.CATALOG_ITEM_REPLACE,
                DAOCatalog.SystemCatalog.RULESSETTINGS.getTitle(), rulesSettings.getTitle()));
    }

    /**
     *  Тестирование осуществления навигации по браузерным кнопкам "Вперед-Назад" и по системной кнопке "Назад" со страницы конфигурации импорта
     *  https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00844
     *  https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$72202726
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Перейти на карточку созданной Таблицы Соответствий testTable. Кликнуть на кнопку "Выгрузить"</li>
     * <li>Перейти на вкладку "Правило импорта", отредактировать правило импорта (Суть изменений не важна. Например, можно в строке <config save-log="true" skip-workflow="false" threads-number="1">
     *  изменить threads-number="2". Сохранить изменения</li>
     * <li>Перейти на вкладку "История". В блоке "история изменения конфигурации" перейти в последнюю созданную версию</li>
     * <li>Кликнуть на системную кнопку "Назад". Проверить, что осуществился переход на вкладку "История" таблицы соответствий testTable</li>
     * </ol>
     */
    @Test
    public void testTabNavigationFromImportConfigPage()
    {
        String newConfigVer = "2";

        //Действия и проверки
        GUILogon.asSuper();

        GUICatalog.goToCatalogRulesSettings();
        GUICatalogItem.goToCard(rulesSettings);
        GUIRulesSettings.goToRulesSettingsCard(GUITab.IMPORT_CONFIG_TAB);
        GUIForm.openEditForm();
        tester.click(GUIXpath.SpecificComplex.IMPORT_EDIT_CONFIG);
        tester.actives().sendKeys(Keys.DELETE, newConfigVer);
        GUIForm.applyModalForm();

        GUIRulesSettings.goToRulesSettingsCard(GUITab.IMPORT_HISTORY_TAB);
        tester.clickTopLeftCorner(GUICatalogItem.ROW_BY_CODE, rulesSettings.getCode(), newConfigVer);

        String matchTblTitleMsg = String.format(
                "После нажатии кнопки 'Назад' в браузере не был осуществлен переход на карточку таблицы соответствий %s",
                rulesSettings.getTitle());

        GUINavigational.clickBackLink();
        GUITester.assertPresent(Div.HEADER_TITLE_TEXT, matchTblTitleMsg, rulesSettings
                .getTitle());
        GUITab.assertTabActive(GUITab.IMPORT_HISTORY_TAB);
    }
}
