package ru.naumen.selenium.cases.admin.system;

import static ru.naumen.selenium.casesutil.bo.GUIButtonBar.BTN_ADD_COMMENT_TITLE;
import static ru.naumen.selenium.casesutil.bo.GUIButtonBar.BTN_CHANGE_RESPONSIBLE_TITLE;
import static ru.naumen.selenium.casesutil.bo.GUIButtonBar.BTN_CHANGE_STATE_TITLE;
import static ru.naumen.selenium.casesutil.metaclass.GUIEventAction.ACTION_VALUE;

import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.Assert;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.admin.GUIMenuItem;
import ru.naumen.selenium.casesutil.admin.GUINavSettings;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem.MenuItemType;
import ru.naumen.selenium.casesutil.model.admin.MenuItem.MenuItemTypeOfCard;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.personalsettings.GUIPersonalSettings;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тестирование настроек навигации в интерфейсе технолога
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
 * Тестирование настройки возможности создания объектов через общую кнопку добавить
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00513
 * 
 * <AUTHOR>
 * @since 16.04.2013
 */
public class NavigationSettings1Test extends AbstractTestCase
{

    /**
     * Тестирование непоявления поля  "Вкладка контента" когда нет контента "Панель вкладок"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>На карточке компании добавляем вкладку1, на нее - любой контент (например, комментарии)</li>
     * <li>Открываем форму создания элемента меню</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Выбираем тип "Ссылка на карточку"</li>
     * <li>Проверяем, что в поле Вкладка карточки есть пункт [не указано], и он выбран</li>
     * <li>В параметре Вкладка карточки выбираем вкладку1</li>
     * <li>Проверяем, что появился попап о незаполненности поля</li>
     * <br>
     * <b>Проверки</b>
     * <li>На форме НЕ появился параметр "Вкладка контента"</li>
     * <li></li>
     * </ol>
    */
    @Test
    public void testAbsenceContentTab()
    {
        // Подготовка
        MetaClass root = DAORootClass.create();
        ContentTab metaClassTab = DAOContentTab.createTab(root.getFqn());
        DSLContent.addTab(metaClassTab);

        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUINavSettings.clickAddTopMenuElement();

        // Действие
        GUIMenuItem.selectMenuItemType(MenuItemType.reference);
        GUIMenuItem.selectMenuItemTypeOfCard(MenuItemTypeOfCard.ROOT.getCode());
        GUISelect.assertSelect(GUIMenuItem.MENU_ITEM_CARD_TAB_INPUT, Lists.newArrayList(GUISelect.EMPTY_VALUE),
                true, false, false);
        GUISelect.select(GUIMenuItem.MENU_ITEM_CARD_TAB_INPUT, GUISelect.EMPTY_SELECTION_ITEM);
        GUIForm.applyFormWithValidationErrors();
        Assert.assertTrue("Значение обязательного атрибута не определено! Отсутствует предупреждение.",
                tester.waitAppear(GUIXpath.Complex.VALIDATION_MESSAGE));

        GUIMenuItem.selectCardTab(metaClassTab);

        // Проверка
        tester.waitDisappear(GUIMenuItem.MENU_ITEM_CONTENT_TAB);
    }

    /**
     * Проверка наличия только что добавленного раздела меню в списке Вложен в раздел
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Выполнение действий</b>
     * <li>Заходим в интерфейс технолога, переходим на страницу "Интерфейс и навигация",
     * в ней на вкладку "Навигация"</li>
     * <li>Нажимаем кнопку Добавить элемент</li>
     * <li>Выбираем вид элемента - Раздел, вводим наименование</li>
     * <li>Нажимаем Сохранить</li>
     * <li>Нажимаем кнопку Добавить элемент</li>
     * <li>Выбираем вид элемента - Ссылка на карточку</li>
     * <br>
     * <b>Проверка</b>
     * <li>В списке Вложен в раздел присутствует раздел, созданный выше</li>
     * </ol>
     */
    @Test
    public void testAddElementWithAddedChapter()
    {
        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();

        List<String> oldIds = GUINavSettings.getElementsId();
        GUINavSettings.clickAddTopMenuElement();

        MenuItem chapter = DAOMenuItem.createChapter(false);
        GUIMenuItem.fillElementTitleField(chapter.getTitle());
        GUIMenuItem.selectMenuItemType(chapter.getType());

        GUIForm.applyForm();

        List<String> newIds = GUINavSettings.getNewElementsId(oldIds);
        chapter.setCode(newIds.get(0));
        chapter.setExists(true);

        GUINavSettings.clickAddTopMenuElement();
        GUIMenuItem.selectMenuItemType(MenuItemType.reference);

        //Проверка
        GUIMenuItem.assertParentSelected("[не указано]", chapter.getTitle());

        DSLMenuItem.delete(chapter);
    }

    /**
     * Проверка количества символов в поле "Название" на форме добавления элемента меню и после его добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Выполнение действий</b>
     * <li>Заходим в интерфейс технолога, переходим на страницу "Интерфейс и навигация",
     * в ней на вкладку "Навигация"</li>
     * <li>Нажимаем кнопку Добавить элемент</li>
     * <li>Вводим в поле Название текст длиной 70 символов</li>
     * <br>
     * <b>Проверка</b>
     * <li>В поле Название отображаются только 64 символа</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Выбираем вид элемента - Раздел меню и нажимаем кнопку Сохранить</li>
     * <br>
     * <b>Проверка</b>
     * <li>Был добавлен 1 элемент</li>
     * <li>Название элемента в таблице элементов верхнего меню содержит 64 символа</li>
     * </ol>
     */
    @Test
    public void testAddElementWithLargeTitle()
    {
        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();

        List<String> oldIds = GUINavSettings.getElementsId();
        GUINavSettings.clickAddTopMenuElement();

        MenuItem chapter = DAOMenuItem.createChapter(false);
        chapter.setTitle(ModelUtils.createTitle(70));

        GUIMenuItem.fillElementTitleField(chapter.getTitle());
        GUIMenuItem.selectMenuItemType(chapter.getType());

        //Проверка
        chapter.setTitle(chapter.getTitle().substring(0, 64));
        String msg = "Количество символов в названии элемента не совпало с ождаемым";
        GUITester.assertValue(GUIMenuItem.MENU_ITEM_TITLE_INPUT, chapter.getTitle());

        //TODO если во время заполнения поля Название убрать фокус, то элемент создается без названия

        //Выполнение действий
        GUIForm.applyForm();

        //Проверки
        List<String> newIds = GUINavSettings.getNewElementsId(oldIds);
        Assert.assertEquals("Полученное количесов элементов не совпало с ожидаемым.", 1, newIds.size());
        chapter.setCode(newIds.get(0));
        chapter.setExists(true);
        GUITester.assertTextPresentWithMsg(GUINavSettings.X_TOP_MENU_ITEM_TITLE, chapter.getTitle(), msg,
                newIds.get(0));

        DSLMenuItem.delete(chapter);
    }

    /**
     * Тестирование отсутствие отображения вложенности в себя у элемента типа Раздел меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент меню chapter типа Раздел</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Перейти на карточку chapter</li>
     * <br>
     * <b>Проверка</b>
     * <li>В блоке "Атрибуты элемента" поле "Вложен в раздел" не заполнено</li>
     * </ol>
     */
    @Test
    public void testBlankParentInRootParent()
    {
        //Подготовка
        MenuItem chapter = DAOMenuItem.createChapter(false);
        DSLMenuItem.add(chapter);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMenuItem.goToTopMenuItemCard(chapter);

        //Проверка
        GUITester.assertTextPresent(GUIMenuItem.MENU_ITEM_CARD_ROWS_VALUE_PATTERN + "/..", "", "parent");
    }

    /**
     * Тестирование кнопки "Отмена" при удалении элемента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать:<ul>
     *   <li>Раздел меню "charter"<ul>
     *     <li>История "history"</li>
     *   </ul></li>
     * </ul></li>
     * <br>
     * <b>Действие:</b>
     * <li>Нажать кнопку удалить у элемента История "history"</li>
     * <li>В появившемся модальном окне нажать "Отмена"</li>
     * <br>
     * <b>Проверка:</b>
     * <li>Элемент остался в списке</li>
     * </ol>
     */
    @Test
    public void testCancelDeleteMenuItem()
    {
        //Настройка
        MenuItem chapter = DAOMenuItem.createChapter(true);
        MenuItem history = DAOMenuItem.createHistory(chapter, true);
        DSLMenuItem.add(chapter, history);

        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUINavSettings.clickDeleteMenuItem(history);
        GUIForm.assertQuestionAppear("Форма подтверждения удаления элемента меню не появилась.");
        tester.click(GUIXpath.SpecificComplex.QUESTION_DIALOG_BTN_NO);
        GUIForm.assertQuestionDisappear("Форма подтверждения удаления элемента меню не исчезла.");

        // Проверка
        GUINavSettings.assertMenuItemPresent(history);
        tester.refresh();
        GUINavSettings.assertMenuItemPresent(history);
    }

    /**
     * Изменение порядка элементов в меню. Перемещение подчиненных элементов внутри родительского
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать (в этом же порядке):<ul>
     *   <li>Раздел меню "charter1"<ul>
     *     <li>История "history"</li>
     *     <li>Ссылка на карточку "reference1"</li>
     *     <li>Ссылка на карточку "reference2"</li>
     *   </ul></li>
     *   <li>Кнопка добавления элементов "button"</li>
     *   <li>Раздел меню "charter2"<ul>
     *     <li>Избранное "favorites"</li>
     *   </ul></li>
     * </ul></li>
     * <br>
     * <b>Проверка:</b>
     * <li>У элемента История "history" есть только кнопка "стрелка вниз"</li>
     * <li>У элемента Ссылка на карточку "reference1" есть две кнопки "стрелка вверх" и "стрелка вниз"</li>
     * <li>У элемента Ссылка на карточку "reference2" есть только кнопка "стрелка вверх"</li>
     * <li>У элемента Избранное "favorites" нет кнопок "стрелка вверх" и "стрелка вниз"
     *     (т.к это единственный элемент )</li>
     * <br>
     * <b>Действие:</b>
     * <li>Переместить элемент История "history" вниз на один шаг</li>
     * <br>
     * <b>Проверка:</b>
     * <li>У элемента История "history" появилась еще одна кнопка, их стало две: "стрелка вверх" и "стрелка вниз"</li>
     * <li>Элемент переместился:<ul>
     *   <li>Раздел меню "charter1"<ul>
     *     <li>Ссылка на карточку "reference1"</li>
     *     <li>История "history"</li>
     *     <li>Ссылка на карточку "reference2"</li>
     *   </ul></li>
     *   <li>Кнопка добавления элементов "button"</li>
     *   <li>Раздел меню "charter2"<ul>
     *     <li>Избранное "favorites"</li>
     *   </ul></li>
     * </ul></li>
     * </ol>
     */
    @Test
    public void testChangeOrderMoveChildMenuItem()
    {
        //Настройка
        MenuItem chapter1 = DAOMenuItem.createChapter(false);
        MenuItem history = DAOMenuItem.createHistory(chapter1, false);
        MenuItem reference1 = DAOMenuItem.createReference(chapter1, DAORootClass.create(), false);
        MenuItem reference2 = DAOMenuItem.createReference(chapter1, DAOOuCase.createClass(), false);
        MenuItem button = DAOMenuItem.createAddButton(false, DAOEmployeeCase.createClass());
        MenuItem chapter2 = DAOMenuItem.createChapter(false);
        MenuItem favorites = DAOMenuItem.createFavorites(chapter2, false);
        DSLMenuItem.add(chapter1, history, reference1, reference2, button, chapter2, favorites);

        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();

        //Проверки
        GUINavSettings.assertMenuItemMoveArrowPresence(history, false, true);
        GUINavSettings.assertMenuItemMoveArrowPresence(reference1, true, true);
        GUINavSettings.assertMenuItemMoveArrowPresence(reference2, true, false);
        GUINavSettings.assertMenuItemMoveArrowPresence(favorites, false, false);

        //Выполнение действий
        GUINavSettings.clickMoveMenuItem(history, false);
        GUINavSettings.assertMenuItemMoveArrowPresence(history, true, true);
        MenuItem[] menuItemList = { chapter1, reference1, history, reference2, button, chapter2, favorites };

        //Проверка
        GUINavSettings.assertMenuItemRow(menuItemList);
    }

    /**
     * Изменение порядка элементов в меню. Перемещение родительского элемента с подчиненными
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать (в этом же порядке):<ul>
     *   <li>Раздел меню "charter1"<ul>
     *     <li>История "history"</li>
     *     <li>Ссылка на карточку "reference1"</li>
     *     <li>Ссылка на карточку "reference2"</li>
     *   </ul></li>
     *   <li>Кнопка добавления элементов "button"</li>
     *   <li>Раздел меню "charter2"<ul>
     *     <li>Избранное "favorites"</li>
     *   </ul></li>
     * </ul></li>
     * <br>
     * <b>Проверка:</b>
     * <li>У элемента Раздел меню "charter2" есть только кнопка "стрелка вверх"</li>
     * <li>У элемента Раздел меню "charter1" есть только кнопка "стрелка вниз"</li>
     * <li>У элемента Кнопка добавления элементов "button" есть две кнопки "стрелка вверх" и "стрелка вниз"</li>
     * <br>
     * <b>Действие:</b>
     * <li>Переместить элемент Раздел меню "charter2" вверх на один шаг</li>
     * <br>
     * <b>Проверка:</b>
     * <li>У элемента Раздел меню "charter2" есть кнопки "стрелка вверх" и "стрелка вниз"</li>
     * <li>Элемент переместился вместе с вложенным элементом Избранное "favorites":<ul>
     *   <li>Раздел меню "charter1"<ul>
     *     <li>История "history"</li>
     *     <li>Ссылка на карточку "reference1"</li>
     *     <li>Ссылка на карточку "reference2"</li>
     *   </ul></li>
     *   <li>Раздел меню "charter2"<ul>
     *     <li>Избранное "favorites"</li>
     *   </ul></li>
     *   <li>Кнопка добавления элементов "button"</li>
     * </ul></li>
     * <br>
     * <b>Действие:</b>
     * <li>Переместить элемент Раздел меню "charter1" вниз на один шаг</li>
     * <br>
     * <b>Проверки:</b>
     * <li>У элемента Раздел меню "charter1" есть кнопки "стрелка вверх" и "стрелка вниз"</li>
     * <li>Элемент переместился вместе с вложенными элементами и оказался под вложением предыдущего элемента:<ul>
     *   <li>Раздел меню "charter2"<ul>
     *     <li>Избранное "favorites"</li>
     *   </ul></li>
     *   <li>Раздел меню "charter1"<ul>
     *     <li>История "history"</li>
     *     <li>Ссылка на карточку "reference1"</li>
     *     <li>Ссылка на карточку "reference2"</li>
     *   </ul></li>
     *   <li>Кнопка добавления элементов "button"</li>
     * </ul></li>
     * </ol>
     */
    @Test
    public void testChangeOrderMoveParentMenuItem()
    {
        //Проготовка
        MenuItem chapter1 = DAOMenuItem.createChapter(false);
        MenuItem history = DAOMenuItem.createHistory(chapter1, false);
        MenuItem reference1 = DAOMenuItem.createReference(chapter1, DAORootClass.create(), false);
        MenuItem reference2 = DAOMenuItem.createReference(chapter1, DAOOuCase.createClass(), false);
        MenuItem button = DAOMenuItem.createAddButton(false, DAOEmployeeCase.createClass());
        MenuItem chapter2 = DAOMenuItem.createChapter(false);
        MenuItem favorites = DAOMenuItem.createFavorites(chapter2, false);
        DSLMenuItem.add(chapter1, history, reference1, reference2, button, chapter2, favorites);

        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();

        //Проверки
        GUINavSettings.assertMenuItemMoveArrowPresence(chapter2, true, false);
        GUINavSettings.assertMenuItemMoveArrowPresence(chapter1, false, true);
        GUINavSettings.assertMenuItemMoveArrowPresence(button, true, true);

        //Выполнение действия
        GUINavSettings.clickMoveMenuItem(chapter2, true);

        //Проверки
        GUINavSettings.assertMenuItemMoveArrowPresence(chapter2, true, true);
        MenuItem[] menuItemList = { chapter1, history, reference1, reference2, chapter2, favorites, button };
        GUINavSettings.assertMenuItemRow(menuItemList);

        //Выполнение действия
        GUINavSettings.clickMoveMenuItem(chapter1, false);

        //Проверки
        GUINavSettings.assertMenuItemMoveArrowPresence(chapter1, true, true);
        MenuItem[] menuItemList1 = { chapter2, favorites, chapter1, history, reference1, reference2, button };
        GUINavSettings.assertMenuItemRow(menuItemList1);
    }

    /**
     * Тестирование перехода на карточку элемента меню и возврат по ссылке возврата к списку элементов верхнего навигационного меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент меню chapter типа Раздел</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку настроек Навигации раздела Интерфейс и навигация</li>
     * <li>Перейти на карточку chapter</li>
     * <br>
     * <b>Проверка</b>
     * <li>Находимся на карточке элемента меню chapter</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Нажать на ссыку "к списку элементов верхнего навигационного меню"</li>
     * <br>
     * <b>Проверка</b>
     * <li>Находимся на карточке настроек Навигации раздела Интерфейс и навигация</li>
     * </ol>
     */
    @Test
    public void testClickOnBackLink()
    {
        //Подготовка
        MenuItem chapter = DAOMenuItem.createChapter(false);
        DSLMenuItem.add(chapter);

        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUINavSettings.clickOnMenuItemTitle(chapter);

        //Проверка
        GUIMenuItem.assertThatMenuItemCard(chapter);

        //Выполнение действия
        GUINavSettings.clickOnBackLink();

        //Проверка
        GUINavSettings.assertThatNavigationCard();
    }

    /**
     * Тестирование перехода в родительский раздел из карточки элемента меню вложенного в раздел
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент меню chapter типа Раздел и в него элемент history типа Избранное</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку history</li>
     * <br>
     * <b>Проверка</b>
     * <li>Находимся на карточке элемента меню history</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Нажать ссылку на родительский раздел</li>
     * <br>
     * <b>Проверка</b>
     * <li>Находимся на карточке элемента меню chapter</li>
     * </ol>
     */
    @Test
    public void testClickOnParentItem()
    {
        //Подготовка
        MenuItem chapter = DAOMenuItem.createChapter(false);
        MenuItem history = DAOMenuItem.createHistory(chapter, false);
        DSLMenuItem.add(chapter, history);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMenuItem.goToTopMenuItemCard(history);

        //Проверка
        GUIMenuItem.assertThatMenuItemCard(history);

        //Выполнение действия
        GUIMenuItem.clickOnParentChapter();

        //Проверка
        GUIMenuItem.assertThatMenuItemCard(chapter);
    }

    /**
     * Тестирование удаления дочернего элемента и родительского раздела из карточки настроек навигации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать (в этом же порядке, все элементы выключены):<ul>
     *   <li>Раздел меню "charter"<ul>
     *     <li>Избранное "favorites"</li>
     *   </ul></li>
     * </ul></li>
     * <br>
     * <b>Действие:</b>
     * <li>Нажать кнопку удалить у элемента Избранное "favorites"</li>
     * <b>Проверка:</b>
     * <li>Появилось модальное окно с текстом "Вы действительно хотите удалить элемент меню "favorites"?"</li>
     * <li>При нажатии на "Да" элемент удаляется</li>
     * <br>
     * <b>Действие:</b>
     * <li>Нажать кнопку удалить у элемента Раздел меню "charter"</li>
     * <br>
     * <b>Проверка:</b>
     * <li>Появилось модальное окно с текстом "Вы действительно хотите удалить элемент меню "charter"?"</li>
     * <li>При нажатии на "Да" элемент удаляется</li>
     * </ol>
     */
    @Test
    public void testDeleteMenuItemFromNavigationCard()
    {
        //Подготовка
        MenuItem chapter = DAOMenuItem.createChapter(true);
        MenuItem favorites = DAOMenuItem.createFavorites(chapter, false);
        DSLMenuItem.add(chapter, favorites);

        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();

        //Тестовые действия
        GUINavSettings.clickDeleteMenuItem(favorites);
        GUIForm.assertQuestionAppear("Форма подтверждения удаления элемента меню не появилась.");
        GUIForm.assertQuestion("Вы действительно хотите удалить элемент меню '%s'?", favorites.getTitle());
        GUIForm.confirmByYes();

        //Проверка
        GUINavSettings.assertMenuItemAbsent(favorites);
        favorites.setExists(false);

        //Тестовые действия
        GUINavSettings.clickDeleteMenuItem(chapter);
        GUIForm.assertQuestionAppear("Форма подтверждения удаления элемента меню не появилась.");
        GUIForm.assertQuestion("Вы действительно хотите удалить элемент меню '%s'?", chapter.getTitle());
        GUIForm.confirmByYes();

        //Проверка
        GUINavSettings.assertMenuItemAbsent(chapter);
        chapter.setExists(false);
    }

    /**
     * Тестирование удаления раздела из его карточки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент меню chapter типа Раздел</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку history</li>
     * <li>Нажать кнопку Удалить</li>
     * <br>
     * <b>Проверка</b>
     * <li>Появилось модальное окно с текстом: "Вы действительно хотите удалить элемент меню 'chapter'?"</li>
     * <li>После подтверждения элемент chapter отсутствует в системе, произошел переход на страницу настроек навигации</li>
     * </ol>
     */
    @Test
    public void testDeleteParentMenuItemFromCard()
    {
        //Подготовка
        MenuItem chapter = DAOMenuItem.createChapter(false);
        DSLMenuItem.add(chapter);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMenuItem.goToTopMenuItemCard(chapter);

        //Проверки
        tester.click(GUIXpath.Div.DEL);
        GUIForm.assertQuestionAppear("Форма подтверждения удаления элемента меню не появилась.");
        GUIForm.assertQuestion("Вы действительно хотите удалить элемент меню '%s'?", chapter.getTitle());
        GUIForm.confirmByYes();

        GUINavSettings.assertThatNavigationCard();
        DSLMenuItem.assertAbsence(chapter);
        chapter.setExists(false);
    }

    /**
     * Тестирование удаления раздела с вложенным в него элементом типа Избранное из карточки раздела
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент меню chapter типа Раздел и в него элемент history типа Избранное</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку chapter</li>
     * <li>Нажать кнопку Удалить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появилось модальное окно с текстом: "Вы действительно хотите удалить элемент меню 'charter'?
     * Вместе с родительским элементом будут удалены все вложенные в него элементы: 'history'
     * Продолжить удаление?"</li>
     * <li>После подтверждения элементы chapter и history отсутствуют в системе</li>
     * </ol>
     */
    @Test
    public void testDeleteParentMenuItemWithChildFromParentCard()
    {
        //Подготовка
        MenuItem chapter = DAOMenuItem.createChapter(false);
        MenuItem history = DAOMenuItem.createHistory(chapter, false);
        DSLMenuItem.add(chapter, history);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMenuItem.goToTopMenuItemCard(chapter);

        //Проверки
        tester.click(GUIXpath.Div.DEL);
        GUIForm.assertQuestionAppear("Форма подтверждения удаления элемента меню не появилась.");
        String actual = tester.getText(GUIXpath.Any.QUESTION_DIALOG + GUIXpath.Any.DIALOG_WIDGET_BODY);
        String question = String.format("Вы действительно хотите удалить элемент меню '%s'?", chapter.getTitle());

        String msgPattern = "Вы действительно хотите удалить элемент меню '%s'?%n"
                + "Вместе с родительским элементом будут удалены все вложенные в него элементы:%n" + "'%s'%n"
                + "Продолжить удаление?";

        GUIForm.assertQuestion(msgPattern, chapter.getTitle(), history.getTitle());
        Assert.assertTrue("Полученное сообщение с подтверждением удаления не свпало с ожидаемым",
                actual.contains(question));
        GUIForm.confirmByYes();

        DSLMenuItem.assertAbsence(chapter);
        chapter.setExists(false);
        DSLMenuItem.assertAbsence(history);
        history.setExists(false);
    }

    /**
     * Тестирование удаления раздела с вложенным в него элементами из карточки настроек навигации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать (в этом же порядке, все элементы выключены):<ul>
     *   <li>Раздел меню "charter"<ul>
     *     <li>История "history"</li>
     *     <li>Ссылка на карточку "reference1"</li>
     *     <li>Ссылка на карточку "reference2"</li>
     *   </ul></li>
     * </ul></li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Нажать кнопку удалить у элемента Раздел меню "charter1"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появилось модальное окно с текстом "Вы действительно хотите удалить элемент меню "charter",
     * который имеет вложенные элементы: "history", "reference1", "reference2" - 
     * они будут удалены вместе с родительским элементом. "</li>
     * <li>При нажатии на "Да" элемент удаляется вместе во вложенными элементами</li>
     * </ol>
     */
    @Test
    public void testDeleteParentMenuItemWithChildsFromNavigationCard()
    {
        //Подготовка
        MenuItem chapter = DAOMenuItem.createChapter(true);
        MenuItem history = DAOMenuItem.createHistory(chapter, true);
        MenuItem reference1 = DAOMenuItem.createReference(chapter, DAORootClass.create(), true);
        MenuItem reference2 = DAOMenuItem.createReference(chapter, DAOOuCase.createClass(), true);
        DSLMenuItem.add(chapter, history, reference1, reference2);

        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();

        //Тестовые действия
        GUINavSettings.clickDeleteMenuItem(chapter);
        GUIForm.assertQuestionAppear("Форма подтверждения удаления элемента меню не появилась.");
        String actual = tester.getText(GUIXpath.Any.QUESTION_DIALOG + GUIXpath.Any.DIALOG_WIDGET_BODY);
        String question = String.format("Вы действительно хотите удалить элемент меню '%s'?", chapter.getTitle());

        String msgPattern = "Вы действительно хотите удалить элемент меню '%s'?%n"
                + "Вместе с родительским элементом будут удалены все вложенные в него элементы:%n"
                + "'%s'%n'%s'%n'%s'%n" + "Продолжить удаление?";

        GUIForm.assertQuestion(msgPattern, chapter.getTitle(), history.getTitle(), reference1.getTitle(),
                reference2.getTitle());
        Assert.assertTrue("Полученное сообщение с подтверждением удаления не свпало с ожидаемым",
                actual.contains(question));
        GUIForm.confirmByYes();

        //Проверки
        GUINavSettings.assertMenuItemAbsent(chapter);
        GUINavSettings.assertMenuItemAbsent(history);
        GUINavSettings.assertMenuItemAbsent(reference1);
        GUINavSettings.assertMenuItemAbsent(reference2);
        chapter.setExists(false);
        history.setExists(false);
        reference1.setExists(false);
        reference2.setExists(false);
    }

    /**
     * Проверка отсутствия селекта Вкладка контента на форме элемента верхнего меню, если на выбранной вкладке метакласса нет панелей вкладок
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Добавить вкладку tab на карточку метакласса employeeCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на страницу настроек навигации</li>
     * <li>Нажать на кнопку Добавить элемент</li>
     * <li>Выбрать Вид элемента - Ссылка на карточку</li>
     * <li>Выбрать Вкладка карточки - employeeCase-tab</li>
     * <br>
     * <b>Проверка </b>
     * <li>Список Вкладка контента отсутствует</li>
     * </ol>
     */
    @Test
    public void testDoNotShowContentTabSelectIfNoTabPanel()
    {
        //Подготовка
        MetaClass employeeCase = DAOEmployeeCase.create(DAOEmployeeCase.createClass());
        DSLMetaClass.add(employeeCase);

        ContentTab tab = DAOContentTab.createTab(employeeCase.getFqn());
        DSLContent.addTab(tab);

        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUINavSettings.clickAddTopMenuElement();
        GUIMenuItem.selectMenuItemType("reference");
        GUIMenuItem.selectCardTab(tab);
        //Проверка
        Assert.assertTrue(tester.waitDisappear(GUIMenuItem.MENU_ITEM_CONTENT_TAB));
    }

    /**
     * Проверка порядка элементов при нажатии кнопки добавления объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00513
     * http://sd-jira.naumen.ru/browse/NSDPRD-2686
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создаем пользовательский класс userClass, 5 пользовательских типов userCases</li>
     * <li>Добавляем в систему userClass и userCases</li>
     * <li>Устанавливаем видимость верхнего меню</li>
     * <li>Создаем кнопку добавления объектов с созданными типами запросов</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим как пользователь</li>
     * <li>Переходим в режим оператора</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем порядок элементов в добавленной кнопке в Верхнем меню (должен быть лексикографический)</li> 
     * </ol>
     */
    @Test
    public void testOrderOfElementsInAddButton()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass[] userCases = new MetaClass[5];

        for (int i = 0; i < userCases.length; i++)
        {
            userCases[i] = DAOUserCase.create(userClass);
        }

        MetaClass[] allMetaClasses = new MetaClass[6];
        Stream.concat(Stream.of(userClass), Stream.of(userCases)).collect(Collectors.toList()).toArray(allMetaClasses);
        DSLMetainfo.add(allMetaClasses);

        MenuItem menuItem = DAOMenuItem.createAddButton(true, userCases);
        DSLMenuItem.add(menuItem);

        DSLNavSettings.editVisibilitySettings(true, true, true);

        //Выполнение действий
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();
        //Проверка
        List<String> expected = ModelUtils.getTitles(userCases);
        Collections.sort(expected);
        GUINavSettingsOperator.assertLinksInTopMenu(menuItem, expected);
    }

    /**
     * Тестирование корректного сохранения названий элементов верхнего меню в разных локалях
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00038
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$83848617
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать элемент верхнего меню level1 типа «Раздел меню» с названием «Уровень 1» в русской локали</li>
     * <li>Создать элемент верхнего меню level2 (вложен в level1) типа «Раздел меню» с названием «Уровень 2» в
     * русской локали</li>
     * <li>Создать элемент верхнего меню level3 (вложен в level2) произвольного типа с названием «Уровень 3» в
     * русской локали</li>
     * <li>Создать суперпользователя superUser</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>В персональных настройках изменить язык пользователя на английский и нажать на кнопку «Сохранить»</li>
     * <li>Перейти в раздел «Навигация» в интерфейсе администратора</li>
     * <li>Нажать на иконку редактирования элемента меню level3</li>
     * <li>Изменить название элемента на «Level 3»</li>
     * <li>Нажать на кнопку «Сохранить»</li>
     * <li>Нажать на иконку редактирования элемента меню level2</li>
     * <li>Изменить название элемента на «Level 2»</li>
     * <li>Нажать на кнопку «Сохранить»</li>
     * <li>Нажать на иконку редактирования элемента меню level1</li>
     * <li>Изменить название элемента на «Level 1»</li>
     * <li>Нажать на кнопку «Сохранить»</li>
     * <li>В персональных настройках изменить язык пользователя на русский и нажать на кнопку «Сохранить»</li>
     * <li>Перейти в раздел «Навигация» в интерфейсе администратора</li>
     * <br>
     * <b>Проверки</b>
     * <li>Элемент level1 имеет название «Уровень 1»</li>
     * <li>Элемент level2 имеет название «Уровень 2»</li>
     * <li>Элемент level3 имеет название «Уровень 3»</li>
     * </ol>
     */
    @Test
    public void testSeparateLocaleTitlesForTopMenuElements()
    {
        // Подготовка
        MenuItem level1 = DAOMenuItem.createChapter(true);
        level1.setTitle("Уровень 1");
        MenuItem level2 = DAOMenuItem.createChapter(true);
        level2.setTitle("Уровень 2");
        level2.setParent(level1.getCode());
        MenuItem level3 = DAOMenuItem.createReference(level2, DAORootClass.create(), true);
        level3.setTitle("Уровень 3");
        DSLMenuItem.add(level1, level2, level3);

        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);
        // Выполнение действий
        GUILogon.login(superUser);
        GUINavigational.goToPersonalSettings();
        GUIPersonalSettings.setLanguage("en");
        GUIForm.applyForm();
        GUINavSettings.goToCard();
        GUIMenuItem.clickEditTopMenuItem(level3);
        GUIMenuItem.selectMenuItemTypeOfCard(MenuItemTypeOfCard.ROOT.getCode());
        GUIMenuItem.fillElementTitleField("Level 3");
        GUIForm.applyForm();
        GUIMenuItem.clickEditTopMenuItem(level2);
        GUIMenuItem.fillElementTitleField("Level 2");
        GUIForm.applyForm();
        GUIMenuItem.clickEditTopMenuItem(level1);
        GUIMenuItem.fillElementTitleField("Level 1");
        GUIForm.applyForm();

        GUINavigational.goToPersonalSettings();
        GUIPersonalSettings.setLanguage("ru");
        GUIForm.applyForm();
        GUINavSettings.goToCard();
        // Проверки
        GUIMenuItem.goToTopMenuItemCard(level1);
        GUIMenuItem.assertThatMenuItemCard(level1);
        GUIMenuItem.goToTopMenuItemCard(level2);
        GUIMenuItem.assertThatMenuItemCard(level2);
        GUIMenuItem.goToTopMenuItemCard(level3);
        GUIMenuItem.assertThatMenuItemCard(level3);
    }

    /**
     * Тестирование видимости элементов меню "Пользовательская кнопка" настроенных на системные действия
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00410
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$137259891
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Включить видимость верхнего и левого меню</li>
     * <li>Создать класс userClass и его тип userCase (без жизненного цикла и назначения ответственного)</li>
     * <li>Создать тип scCase класса "Запрос"</li>
     * <li>Добавить элемент верхнего меню changeState "Пользовательская кнопка" настроенный на системное действие
     * "изменить статус"</li>
     * <li>Добавить элемент верхнего меню changeResp "Пользовательская кнопка" настроенный на системное действие
     * "изменить ответственного"</li>
     * <li>Добавить элемент верхнего меню addComment "Пользовательская кнопка" настроенный на системное действие
     * "добавить комментарий"</li>
     * <li>Добавить БО userBo типа userCase и scBo типа scCase/li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником/li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Проверить, что кнопки changeState, changeResp, addComment в верхнем меню отсутствуют</li>
     * <li>Перейти на карточку объекта scBo</li>
     * <li>Проверить, что кнопки changeState, changeResp, addComment в верхнем меню присутствуют</li>
     * </ol>
     */
    @Test
    public void testVisibilitySystemToolToTopMenu()
    {
        //Подготовка
        DSLNavSettings.editVisibilitySettings(true, true, false);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass scCase = DAOScCase.create();
        DSLMetainfo.add(userClass, userCase, scCase);

        MenuItem changeState = DAOMenuItem.createCustomButton(true, null);
        MenuItem changeResp = DAOMenuItem.createCustomButton(true, null);
        MenuItem addComment = DAOMenuItem.createCustomButton(true, null);
        DSLMenuItem.add(changeState, changeResp, addComment);

        GUILogon.asSuper();
        GUINavSettings.goToCard();

        GUINavSettings.clickEditMenuItem(changeState);
        GUIMenuItem.fillElementTitleField(changeState.getTitle());
        GUISelect.selectByTitle(ACTION_VALUE, BTN_CHANGE_STATE_TITLE.toLowerCase(Locale.ROOT));
        GUIForm.applyForm();
        GUIMenuItem.clickSwitchOnTopMenuItem(changeState);

        GUINavSettings.clickEditMenuItem(changeResp);
        GUIMenuItem.fillElementTitleField(changeResp.getTitle());
        GUISelect.selectByTitle(ACTION_VALUE, BTN_CHANGE_RESPONSIBLE_TITLE.toLowerCase(Locale.ROOT));
        GUIForm.applyForm();
        GUIMenuItem.clickSwitchOnTopMenuItem(changeResp);

        GUINavSettings.clickEditMenuItem(addComment);
        GUIMenuItem.fillElementTitleField(addComment.getTitle());
        GUISelect.selectByTitle(ACTION_VALUE, BTN_ADD_COMMENT_TITLE.toLowerCase(Locale.ROOT));
        GUIForm.applyForm();
        GUIMenuItem.clickSwitchOnTopMenuItem(addComment);

        Bo user = DAOUserBo.create(userCase);
        Bo scBo = DAOSc.create(scCase);
        DSLBo.add(scBo, user);

        GUILogon.asTester();
        GUIBo.goToCard(user);
        GUINavSettingsOperator.assertAbsenceMenuItem(changeState);
        GUINavSettingsOperator.assertAbsenceMenuItem(changeResp);
        GUINavSettingsOperator.assertAbsenceMenuItem(addComment);

        GUIBo.goToCard(scBo);
        GUIButtonBar.assertPresentButtonByTitle(addComment.getTitle());
        GUIButtonBar.assertPresentButtonByTitle(changeResp.getTitle());
        GUIButtonBar.assertPresentButtonByTitle(addComment.getTitle());
    }
}
