package ru.naumen.selenium.cases.web.servlets;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.report.DAOReportInstance;
import ru.naumen.selenium.casesutil.model.report.DAOReportTemplate;
import ru.naumen.selenium.casesutil.model.report.ReportInstance;
import ru.naumen.selenium.casesutil.model.report.ReportTemplate;
import ru.naumen.selenium.casesutil.report.DSLReportTemplate;
import ru.naumen.selenium.casesutil.report.GUIReportInstance;
import ru.naumen.selenium.casesutil.report.GUIReportInstance.ExportFormat;
import ru.naumen.selenium.casesutil.report.GUIReportInstanceList;
import ru.naumen.selenium.casesutil.scripts.DSLAsyncServlets;
import ru.naumen.selenium.casesutil.scripts.DSLAsyncServlets.AsyncQueue;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тестирование экспорта адвлиста и экземпляров отчета через асинхронные сервлеты
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/dbaccess.properties
 * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$93279424
 * <AUTHOR>
 * @since 23.04.2020
 */
public class ExportAsyncServletsTest extends AbstractTestCase
{
    private static MetaClass userCase;

    /**
     * <b>Общая подготовка</b>
     * <ol>
     *     <li>Включить асинхронные сервлеты для очередей
     *      <ul>
     *          <li>report-export</li>
     *          <li>advlist-export</li>
     *      </ul>
     *     </li>
     *     <li>Добавить пользовательский класс userClass и тип userCase</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAsyncServlets.enableAsyncQueues(AsyncQueue.REPORT_EXPORT, AsyncQueue.ADVLIST_EXPORT);
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
    }

    private static MetaClass userClass;

    /**
     * Тестирование выгрузку адвлиста через асинхронные сервлеты
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/dbaccess.properties
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$93279424
     * <br>
     * <b>Подготовка</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>На карточку объектов типа userCase добавить контент "Список объектов" objectAdvlist,
     *     представление - сложный список, класс объектов - userClass</li>
     *     <li>Добавить пользовательский объект userBo типа userCase</li>
     * </ol>
     * <br>
     * <b>Действия и проверки</b>
     * <ol>
     *     <li>Зайти под сотрудником</li>
     *     <li>Перейти на карточку userBo</li>
     *     <li>В контенте objectAdvlist нажать "Экспорт списка"</li>
     *     <li>Проверить что список выгрузился</li>
     * </ol>
     */
    @Test
    public void testAdvlistExport()
    {
        final ContentForm objectAdvList = DAOContentCard.createObjectAdvList(userCase.getFqn(), userClass);
        DSLContent.add(objectAdvList);

        final Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);

        GUILogon.asTester();
        GUIBo.goToCard(bo);

        objectAdvList.advlist().toolPanel().exportFile();
    }

    /**
     * Тестирование экспорта онлайн отчета через асинхронные сервлеты
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/dbaccess.properties
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$93279424
     * <br>
     * <b>Подготовка</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Добавить шаблон отчета reportTemplate</li>
     *     <li>На карточку объектов типа userCase добавить контент "Отчет/печатная форма" report, с шаблоном отчета
     *     reportTemplate</li>
     * </ol>
     * <br>
     * <b>Действия и проверки</b>
     * <ol>
     *     <li>Зайти под сотрудником и перейти на карточку userBo</li>
     *     <li>В контенте report выгрузить отчет в xlsx</li>
     *     <li>Проверить что отчет выгрузился</li>
     * </ol>
     */
    @Test
    public void testOnlineReportExport()
    {
        final ReportTemplate reportTemplate = DAOReportTemplate.createReportTemplate(DAOReportTemplate.ALL_PARAMETERS);
        DSLReportTemplate.add(reportTemplate);

        final ContentForm report = DAOContentCard.createReport(userCase.getFqn(), reportTemplate);
        DSLContent.add(report);

        final Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIReportInstance.exportReport(report, ExportFormat.XLSX);
        GUIAdmin.waitExport("report", "xlsx");
    }

    /**
     * Тестирование экспорта отчета из списка отчетов через асинхронные сервлеты
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/dbaccess.properties
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$93279424
     * <br>
     * <b>Подготовка</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Добавить шаблон отчета reportTemplate</li>
     *     <li>На карточку объектов типа userCase добавить контент "Список отчетов/печатных форм" reportList, с
     *     шаблоном отчета reportTemplate</li>
     * </ol>
     * <br>
     * <b>Действия и проверки</b>
     * <ol>
     *     <li>Зайти под сотрудником и перейти на карточку userBo</li>
     *     <li>В контенте reportList добавить отчет reportInstance</li>
     *     <li>На карточе построенного reportInstance выполнить экспорт в xlsx</li>
     *     <li>Проверить что отчет выгрузился</li>
     * </ol>
     */
    @Test
    public void testReportExport()
    {
        final ReportTemplate reportTemplate = DAOReportTemplate.createReportTemplate(DAOReportTemplate.ALL_PARAMETERS);
        DSLReportTemplate.add(reportTemplate);

        final ContentForm reportList = DAOContentCard.createReportList(userCase.getFqn(), reportTemplate);
        DSLContent.add(reportList);

        final Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        final ReportInstance reportInstance = DAOReportInstance.create(userBo, reportList, reportTemplate);

        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIReportInstance.clickReportInList(reportTemplate, reportList);
        GUIReportInstanceList.createNewReport();
        GUIReportInstance.fillReportTitle(reportInstance);
        GUIForm.applyModalForm();
        /*
            Странная штука, несмотря на то, что setUuidByUrl выглядит так, как сделает всё что требуется для
            нормального удаления отчета,но он этого не делает.
            Он выдергивает число после reportInstance: и сохраняет его в UUID модели.
            Для нормального удаления нужно проставить reportInstance$ + UUID.
            Так везде who knows why
         */
        GUIReportInstance.setUuidByUrl(reportInstance);
        reportInstance.setUuid("reportInstance$" + reportInstance.getUuid());

        GUIReportInstance.exportReportInstance(ExportFormat.XLSX.getCode(), Div.REPORT_XLSX);
        GUIAdmin.waitExport("report", "xlsx");
    }
}
