package ru.naumen.selenium.cases.admin.catalogs.user;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLInterface;
import ru.naumen.selenium.casesutil.admin.interfaze.GUIInterface;
import ru.naumen.selenium.casesutil.admin.interfaze.GUILanguageForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.catalog.DSLCatalog;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.file.GUIFileAdmin;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.ActionType;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование элементов справочников (Элемент)
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00139
 * <AUTHOR>
 * @since 07.11.2011
 *
 */
public class UserCatalogItemTest extends AbstractTestCase
{
    private static final String FILE_DEL_ID_BUTTON = "//*[@id='gwt-debug-clearButton.%s']";

    /**
     * Тестирование добавления изображения при создании элемента пользовательского справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00190
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00272
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li> Создать пользовательский справочник userCatalog(ввести Название и Код, Плоский, С папками - нет)</li>
     * <li></li>
     * <li></li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем</li>
     * <li> Открыть форму добавления элемента справочника item, ввести Название, Код </li>
     * <li>Нажать кнопку "Выберите файл", выбрать файл с изображением (расширение (или): jpeg, jpg, png, tiff, gif, bmp)</li>
     * <li>Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>На карточке справочника в списке элементов отображается созданный элемент с параметрами: картинка + название, код </li>
     * <li>Зайти в карточку элемента item</li>
     * <li>заполнены поля: Название, Код, Изображение</li>
     * </ol>
     */
    @Test
    public void testAddImageAtCreateSimpleItemToUserCatalog()
    {
        // Подготовка
        Catalog userCatalog = DAOCatalog.createUser(true, false);
        CatalogItem item = DAOCatalogItem.createUser(userCatalog);
        DSLCatalog.add(userCatalog);
        // Действие
        GUILogon.asSuper();
        GUICatalogItem.callAddForm(item);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, item.getCode());
        GUIFileAdmin.uploadFile(GUIXpath.PropertyDialogBoxContent.ICON_VALUE, DSLFile.IMG_FOR_UPLOAD);
        GUIForm.applyForm();
        GUICatalogItem.goToCard(item);
        GUICatalogItem.setUuidByUrl(item);
        GUICatalogItem.goToCatalog(item);
        // Проверка
        item.setIconUuid(DSLCatalogItem.getFileUuidFromItem(item));
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование добавления изображения при редактировании элемента пользовательского справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00191
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00272
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский справочник userCatalog(ввести Название и Код, Плоский, С папками - нет)</li>
     * <li>В созданном справочнике userCatalog добавить элемент справочника item с заполненными полями:
     *  Название, код</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем</li>
     * <li>Открыть форму редактирования созданного элемента справочника item</li>
     * <li>Нажать кнопку "Выберите файл", выбрать файл с изображением (расширение (или):
     *  jpeg, jpg, png, tiff, gif, bmp)</li>
     *  <li>Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>На карточке справочника userCatalog в списке элементов отображается созданный элемент item с параметрами:
     *  картинка + название, код</li>
     * <br>
     * <b>Выполнение действия</b>
     *  <li>Зайти в карточку элемента item</li>
     *  <li>Заполнены поля: Название, Код, Изображение</li>
     * </ol>
     */
    @Test
    public void testAddImageAtEditSimpleItemToUserCatalog()
    {
        // Подготовка
        Catalog userCatalog = DAOCatalog.createUser(true, false);
        CatalogItem item = DAOCatalogItem.createUser(userCatalog);
        DSLCatalog.add(userCatalog);
        DSLCatalogItem.add(item);
        // Действие
        GUILogon.asSuper();
        GUICatalogItem.goToCard(item);
        GUICatalogItem.openEditForm();
        GUIFileAdmin.uploadFile(GUIXpath.PropertyDialogBoxContent.ICON_VALUE, DSLFile.IMG_FOR_UPLOAD);
        GUIForm.applyForm();
        //Проверка
        item.setIconUuid(DSLCatalogItem.getFileUuidFromItem(item));
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование добавления элемента справочника со включенным модулем проверки прав для нелицензированного пользователя
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00190
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$47074515
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский справочник catalog (не плоский, без папок)</li>
     * <li>Создать элемент item пользовательского справочника catalog</li>
     * <li>Добавить нелицензированного сотрудника employee</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под суперпользователем</li>
     * <li>Включаем модуль проверки прав через скрипт в консоли: beanFactory.getBean('permissionsCheckModuleServiceImpl').enableModule(employee.getUuid())</li>
     * <li>Заходим в карточку справочника catalog</li>
     * <li>В блоке Элементы справочника нажимаем кнопку Добавить</li>
     * <li>На форме добавления атрибута заполнить поля: название, код.</li>
     * <li>Нажамаем Сохранить</li>
     * <li>Проверяем, что форма добавления закрылась и мы перешли на карточку справочника catalog.</li>
     * <li>Переходим на карточку созданного элемента item</li>
     * <li>В блоке Элементы справочника напротив элемента item нажимаем пиктограмму редактировать</li>
     * <li>На форме редактирования элемента меняем название элемента item</li>
     * <li>Нажимаем Сохранить</li>
     * <li>Проверяем, что форма редактирования закрылась и ошибок не возникло</li>
     * </ol>
     */
    @Test
    public void testAddItemWithRightsModuleSwithedOn()
    {
        Cleaner.afterTest(true, () ->
        {
            new ScriptRunner("beanFactory.getBean('permissionsCheckModuleServiceImpl').disableModule()").runScript();
        });

        // Подготовка
        Catalog catalog = DAOCatalog.createUser(true, false);
        CatalogItem item = DAOCatalogItem.createUser(catalog);
        DSLCatalog.add(catalog);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(employee);
        new ScriptRunner("beanFactory.getBean('permissionsCheckModuleServiceImpl').disableModule()").runScript();

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUICatalogItem.callAddForm(item);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, item.getCode());

        GUIForm.applyForm();
        GUICatalogItem.assertPresent(item);

        GUICatalogItem.goToCard(item);
        tester.click(ActionType.EDIT.getXpath());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle() + item.getTitle());
        GUIForm.applyForm();
        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование добавления элемента справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00190
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский справочник А (плоский без папок)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника</li>
     * <li>В блоке Элементы справочника нажимаем кнопку Добавить</li>
     * <li>На форме добавления атрибута заполнить поля: название, код. Остальные поля оставить незаполненными</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма добавления закрылась. Мы перешли на карточку справочника A.</li>
     * <li>Карточка справочника, блок Элементы справочника: название(ссылка на карточку элемента) и код элемента</li>
     * </ol>
     */
    @Test
    public void testAddSimpleItemToUserCatalog()
    {
        Catalog catalog = DAOCatalog.createUser(true, false);
        CatalogItem item = DAOCatalogItem.createUser(catalog);
        DSLCatalog.add(catalog);
        GUILogon.asSuper();
        GUICatalogItem.callAddForm(item);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, item.getCode());
        GUIForm.applyForm();
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование добавления элемента(с родителем) справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00190
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский справочник А (не плоский, без папок)</li>
     * <li>Создать элемент A пользовательского справочника А</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника</li>
     * <li>В блоке Элементы справочника нажимаем кнопку Добавить</li>
     * <li>На форме добавления атрибута заполнить поля: название, код. Родителем указать элемент B</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма добавления закрылась. Мы перешли на карточку справочника A.</li>
     * <li>Карточка справочника, блок Элементы справочника: название(ссылка на карточку элемента) и код элемента</li>
     * </ol>
     */
    @Test
    public void testAddSimpleItemWithParentToUserCatalog()
    {
        Catalog catalog = DAOCatalog.createUser(false, false);
        CatalogItem itemA = DAOCatalogItem.createUser(catalog);
        CatalogItem itemB = DAOCatalogItem.createUser(catalog);
        DSLCatalog.add(catalog);
        DSLCatalogItem.add(itemA);
        GUILogon.asSuper();
        GUICatalogItem.callAddForm(itemB);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, itemB.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, itemB.getCode());
        GUISelect.select("//div[@id='gwt-debug-parent-value']//input", itemA.getUuid());
        GUIForm.applyForm();
        GUICatalogItem.assertPresent(itemB);
    }

    /**
     * Тестирование копирования элемента справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00143
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский справочник А (плоский без папок)</li>
     * <li>Создать элемент B справочника A</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника A</li>
     * <li>В блоке Элементы справочника напротив элемента B нажимаем пиктограмму копировать</li>
     * <li>На форме копирования элемента: вводим новое название и новый код соотвтествующие элементу справочника C.</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма копирования закрылась</li>
     * <li>Карточка справочника, блок Элементы справочника: присутствует элемент B и элемент C</li>
     * </ol>
     */
    @Test
    public void testCopySimpleItemToUserCatalog()
    {
        Catalog catalog = DAOCatalog.createUser(false, false);
        CatalogItem itemA = DAOCatalogItem.createUser(catalog);
        CatalogItem itemB = DAOCatalogItem.createUser(catalog);
        DSLCatalog.add(catalog);
        DSLCatalogItem.add(itemA);
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(itemA);
        //Нажимаем кнопку "Редактировать элемент."
        GUICatalog.clickPictogram(itemA, "copy");
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, itemB.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, itemB.getCode());
        GUIForm.applyForm();
        GUICatalogItem.assertPresent(itemA);
        GUICatalogItem.assertPresent(itemB);
    }

    /**
     * Тестирование удаления изображения при редактировании созданного элемента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00191
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00272
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский справочник userCatalog(ввести Название и Код, Плоский, С папками - нет)</li>
     * <li>Открыть форму добавления элемента справочника item, ввести Название, Код</li>
     * <li>Нажать кнопку "Выберите файл", выбрать файл с изображением (расширение (или):
     * jpeg, jpg, png, tiff, gif, bmp)</li>
     * <li>Сохранить</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем</li>
     * <li>Открыть форму редактирования созданного элемента справочника item</li>
     * <li>Нажать кнопку удаления изображения</li>
     * <li>Сохранить </li>
     * <br>
     * <b>Проверка</b>
     * <li>На карточке справочника userCatalog в списке элементов отображается созданный элемент item с параметрами:
     * название, код</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в карточку элемента item</li>
     * <br>
     * <b>Проверка</b>
     * <li>В карточке элемента item заполнены поля: Название, Код, в поле Изображение пусто</li>
     * </ol>
     */
    @Test
    public void testDeleteImageAtEditSimpleItemToUserCatalog()
    {
        // Подготовка
        Catalog userCatalog = DAOCatalog.createUser(true, false);
        DSLCatalog.add(userCatalog);
        CatalogItem item = DAOCatalogItem.createUser(userCatalog);
        item.setIconPath(DSLFile.IMG_FOR_UPLOAD);
        DSLCatalogItem.add(item);

        // Действие
        GUILogon.asSuper();
        GUICatalogItem.goToCard(item);
        GUICatalogItem.openEditForm();
        String deleteFileXpath = String.format(FILE_DEL_ID_BUTTON, item.getIconUuid());
        tester.click(deleteFileXpath);
        GUIForm.applyForm();
        // Проверка
        item.setIconUuid(null);
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование удаления элемента справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00192
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский справочник А (плоский без папок)</li>
     * <li>Создать элемент А справочника A</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника А</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму Удалить</li>
     * <li>Подтверждаем удаление</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника А, блок Элементы справочника: элемент отсутствует</li>
     * </ol>
     */
    @Test
    public void testDeleteSimpleItemToUserCatalog()
    {
        Catalog catalog = DAOCatalog.createUser(false, false);
        CatalogItem item = DAOCatalogItem.createUser(catalog);
        DSLCatalog.add(catalog);
        DSLCatalogItem.add(item);
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(item);
        //Нажимаем кнопку "Удалить Элемент"
        GUICatalog.clickPictogramRemoveAndConfirm(item);

        GUICatalogItem.assertAbsence(item);
    }

    /**
     * Тестирование удаления элемента справочника из карточки элемента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00192
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский справочник А (плоский без папок)</li>
     * <li>Создать элемент B справочника A</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку элемента B</li>
     * <li>Нажимаем кнопку Удалить</li>
     * <li>Подтверждаем удаление</li>
     * <br>
     * <b>Проверки</b>
     * <li>Мы перешли в карточку справочника А</li>
     * <li>Карточка справочника А, блок Элементы справочника: элемент отсутствует</li>
     * </ol>
     */
    @Test
    public void testDeleteSimpleItemToUserCatalogFromCard()
    {
        Catalog catalog = DAOCatalog.createUser(false, false);
        CatalogItem item = DAOCatalogItem.createUser(catalog);
        DSLCatalog.add(catalog);
        DSLCatalogItem.add(item);
        GUILogon.asSuper();
        GUICatalogItem.goToCard(item);
        tester.click(GUIXpath.Div.DEL);

        //Ожидаем форму подтверждения удаления
        GUIForm.confirmDelete();
        GUICatalogItem.assertAbsence(item);
    }

    /**
     * Тестирование редактирования элемента справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00191
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский справочник А (плоский без папок)</li>
     * <li>Создать элемент B справочника A</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку элемента справочника B</li>
     * <li>В блоке Элементы справочника напротив элемента B нажимаем пиктограмму редактировать</li>
     * <li>На форме редактирования элемента проверяем, что код не редактируется.</li>
     * <li>На форме редактирования элемента: меняем название</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма редактирования закрылась</li>
     * <li>Карточка справочника, блок Элементы справочника: название(ссылка на карточку элемента) и код элемента</li>
     * </ol>
     */
    @Test
    public void testEditSimpleItemToUserCatalog()
    {
        Catalog catalog = DAOCatalog.createUser(false, false);
        CatalogItem item = DAOCatalogItem.createUser(catalog);
        DSLCatalog.add(catalog);
        DSLCatalogItem.add(item);
        item.setTitle(ModelUtils.createTitle());
        GUILogon.asSuper();
        GUICatalogItem.goToCard(item);
        //Нажимаем кнопку "Редактировать Элемент"
        tester.click(ActionType.EDIT.getXpath());
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        //Меняем название элемента справочника
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        GUIForm.applyForm();
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование копирования элемента справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00143
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский справочник А (плоский без папок)</li>
     * <li>Создать элемент B справочника A</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника A</li>
     * <li>В блоке Элементы справочника напротив элемента B нажимаем пиктограмму копировать</li>
     * <li>На форме копирования элемента: вводим новое название
     * и старый код.</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появилось сообщение о невозможности копирования.</li>
     * </ol>
     */
    @Test
    public void testTryCopySimpleItemToUserCatalog()
    {
        Catalog catalog = DAOCatalog.createUser(false, false);
        CatalogItem itemA = DAOCatalogItem.createUser(catalog);
        CatalogItem itemB = DAOCatalogItem.createUser(catalog);
        itemB.setCode(itemA.getCode());
        DSLCatalog.add(catalog);
        DSLCatalogItem.add(itemA);
        GUILogon.asSuper();
        GUICatalog.goToCard(catalog);
        //Нажимаем кнопку "Копировать элемент."
        GUICatalog.clickPictogram(itemA, "copy");
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, itemB.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, itemB.getCode());

        String expected = String.format(
                "Значение атрибута 'Код элемента справочника': Элемент справочника '%s' с кодом '%s' уже существует!",
                catalog.getTitle(), itemA.getCode());
        GUIForm.applyFormAssertError(expected);
        GUIForm.cancelForm();
    }

    /**
     * Тестирование корректности локализации ошибки при копировании элемента справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00192
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00147
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский справочник catalog (плоский без папок)</li>
     * <li>Создать элемент itemA справочника catalog</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим в настройки интерфейса</li>
     * <li>Меняем язык по умолчанию на английский</li>
     * <li>Заходим в карточку  справочника catalog</li>
     * <li>В блоке Элементы справочника напротив элемента itemA нажимаем пиктограмму копировать</li>
     * <li>На форме копирования элемента: вводим новое название
     * и старый код.</li>
     * <li>Нажать Сохранить</li>
     * <li>Появилось сообщение о невозможности копирования с корректной локализацией</li>
     * </ol>
     */
    @Test
    public void testTryCopySimpleItemToUserCatalogWithLocalizationSwitch()
    {
        //Подготовка
        Catalog catalog = DAOCatalog.createUser(false, false);
        CatalogItem itemA = DAOCatalogItem.createUser(catalog);
        DSLCatalog.add(catalog);
        DSLCatalogItem.add(itemA);
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.EN_LANGUAGE);

        GUICatalog.goToCard(catalog);
        GUICatalog.clickPictogram(itemA, "copy");

        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        GUICatalogItem.setCode(itemA.getCode());
        GUICatalogItem.setTitle(itemA.getTitle());
        GUIForm.applyFormAssertError(String.format(
                "Attribute value 'Catalog item code': Catalog '%s' item with the code '%s' already exists!",
                catalog.getTitle(), itemA.getCode()));

        Catalog catalog2 = DAOCatalog.createUser(false, false);
        CatalogItem itemB = DAOCatalogItem.createUser(catalog2);
        DSLCatalog.add(catalog2);
        DSLCatalogItem.add(itemB);

        GUIForm.cancelForm();
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.RU_LANGUAGE);

        GUICatalog.goToCard(catalog2);
        GUICatalog.clickPictogram(itemB, "copy");

        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        GUICatalogItem.setCode(itemB.getCode());
        GUICatalogItem.setTitle(itemB.getTitle());
        GUIForm.applyFormAssertError(String.format(
                "Значение атрибута 'Код элемента справочника': Элемент справочника '%s' с кодом '%s' уже существует!",
                catalog2.getTitle(), itemB.getCode()));
    }

    /**
     * Тестирование корректности локализации ошибки при копировании элемента справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00192
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00147
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Включить английскую раскладку стенда</li>
     * <li>Создать пользовательский справочник catalog (плоский без папок)</li>
     * <li>Создать элемент itemA справочника catalog</li>
     * <li>Создать класс типа "Запрос"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим в настройки интерфейса</li>
     * <li>Меняем язык на русский</li>
     * <li>Заходим в карточку класса "Запрос"</li>
     * <li>Создаем атрибут attr1 типа "Эдемент справичника" справочника catalog со значением по умолчанию itemA</li>
     * <li>Заходим в карточку  справочника catalog</li>
     * <li>Пытаемся удалить атрибут attr1</li>
     * <li>Появилось сообщение о невозможности удаления с корректной локализацией</li>
     * </ol>
     */
    @Test
    public void testTryDeleteCatalogItemWhileInUseWithLocalizationSwitch()
    {
        //Подготовка
        DSLInterface.editSystemLanguage(DSLInterface.EN_LANGUAGE);
        Catalog catalog = DAOCatalog.createUser(false, false);
        CatalogItem itemA = DAOCatalogItem.createUser(catalog);
        DSLCatalog.add(catalog);
        DSLCatalogItem.add(itemA);
        MetaClass scClass = DAOScCase.createClass();

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.RU_LANGUAGE);

        GUIMetaClass.goToCard(scClass);
        Attribute attr1 = DAOAttribute.createCatalogItem(scClass.getFqn(), catalog, itemA);
        GUIAttribute.clickAdd();
        GUIAttribute.fillAttrTitle(attr1.getTitle());
        GUIAttribute.fillAttrCode(attr1.getCode());
        GUIAttribute.selectAttrType(AttributeConstant.CatalogItemType.CODE);
        GUIAttribute.selectCatalog(catalog);
        GUIAttribute.setCatalogItemDefaultValue(attr1);
        GUIForm.applyForm();

        GUICatalog.goToCard(catalog);
        GUICatalogItem.tryToDeleteAndCheckErrorMessage(itemA,
                String.format(
                        "Элемент справочника '%s' '%s' не может быть удален по следующим причинам:\n"
                                + "1. Элемент справочника '%s' используется в качестве значения по умолчанию атрибутов: \"%s\" (класс 'Запрос').",
                        catalog.getTitle(), itemA.getTitle(), catalog.getTitle(), attr1.getTitle()));
        DSLAttribute.delete(attr1);
    }

    /**
     * Тестирование попытки удаления элемента справочника с уникальной иконкой,
     * если он используется в качестве значения по умолчанию для атрибута метакласса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00272
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00192
     * http://sd-jira.naumen.ru/browse/NSDPRD-4249
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить новый пользовательский справочник catalog (Плоский - да, С папками - нет)</li>
     * <li>Добавить новый элемент item справочника catalog</li>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Добавить в userClass атрибут attr типа "Элемент справочника"
     * (Справочник - catalog, Значение по умолчанию - item)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в интерфейс технолога под суперпользователем</li>
     * <li>Перейти на карточку справочника catalog</li>
     * <li>Нажать пиктограмму "Редактировать" напротив элемента item</li>
     * <li>Загрузить еще не существующее в системе изображение в качестве иконки элемента</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Нажать пиктограмму "Удалить" напротив элемента item</li>
     * <li>Подтвердить удаление элемента</li>
     * <li>Проверить, что на экране появилось сообщение об ошибке следующего вида:
     * <br>
     * "Элемент справочника catalog item не может быть удален по следующим причинам:
     * <br>
     * 1. Элемент справочника catalog используется в качестве значения по умолчанию атрибутов: attr (класс userClass)."
     * </li>
     * <li>Проверить, что item присутствует в списке элементов справочника</li>
     * <li>Перейти на карточку элемента item</li>
     * <li>Проверить, что на карточке заполнены поля Код, Название и Изображение</li>
     * </ol>
     */
    @Test
    public void testTryDeleteCatalogItemWithUniqueIconWhileInUse()
    {
        // Подготовка
        Catalog catalog = DAOCatalog.createUser(true, false);
        DSLCatalog.add(catalog);
        CatalogItem item = DAOCatalogItem.createUser(catalog);
        DSLCatalogItem.add(item);
        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);
        Attribute attr = DAOAttribute.createCatalogItem(userClass.getFqn(), catalog, item);
        DSLAttribute.add(attr);
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUICatalog.goToCard(catalog);
        GUICatalog.clickPictogram(item, "edit");
        GUIForm.assertDialogAppear("Форма редактирования элемента справочника не появилась.");
        GUIFileAdmin.uploadFile(GUIXpath.PropertyDialogBoxContent.ICON_VALUE, DSLFile.IMG_FOR_UPLOAD_24_X_24);
        GUIForm.applyModalForm();

        String message = String.format(ErrorMessages.NOT_DEL_CATALOG_ITEM, catalog.getTitle(), item.getTitle());
        String reason1 = String.format(
                ErrorMessages.CAT_ITEM + "'%s' " + ErrorMessages.USE_AS_DEF_VALUE + "\"%s\" (класс '%s')",
                catalog.getTitle(), attr.getTitle(), userClass.getTitle());
        String expected = String.format("%s\n1. %s.", message, reason1);
        GUICatalogItem.tryToDeleteAndCheckErrorMessage(item, expected);

        item.setIconUuid(DSLCatalogItem.getFileUuidFromItem(item));
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование возможности сохранения элемента справочника с названием максимально допустимой длины (350 символов).
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$74479663
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00272
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский справочник catalog (плоский без папок)</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Заходим в карточку справочника</li>
     * <li>В блоке Элементы справочника нажимаем кнопку Добавить</li>
     * <li>На форме добавления атрибута заполнить поля: название (длиной 350 символов), код. Остальные поля оставить
     * незаполненными</li>
     * <li>Нажать Сохранить</li>
     * <li>Проверить наличие элемента справочника с указанными полями</li>
     * <li>Изменить название справочника на другое, тоже длиной 350 символов</li>
     * <li>Проверить наличие элемента справочника с указанными полями</li>
     * </ol>
     */
    @Test
    public void testCatalogItemMaxTitleLength()
    {
        //подготовка
        Catalog catalog = DAOCatalog.createUser(true, false);
        CatalogItem item = DAOCatalogItem.createUser(catalog);
        item.setTitle(ModelUtils.createTitle(350));
        DSLCatalog.add(catalog);
        GUILogon.asSuper();
        //Выполнения действий и проверки
        //Переходим на форму добавления элемента
        GUICatalogItem.callAddForm(item);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        //Вводим значения
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, item.getCode());
        GUIForm.applyForm();
        //Проверяем введённые значения
        GUICatalogItem.assertPresent(item);
        //Задаём новое название элемента
        item.setTitle(ModelUtils.createTitle(350));
        GUICatalogItem.goToCard(item);
        //Нажимаем кнопку "Редактировать Элемент"
        tester.click(ActionType.EDIT.getXpath());
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        //Меняем название элемента справочника
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        GUIForm.applyForm();
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование вычислимого атрибута типа "Элемент справочника" c родительским элементом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00445
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00162
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104183386
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский справочник catalog (не плоский, без папок)</li>
     * <li>В справочнике catalog добавить элемент parent и дочерний для него элемент child</li>
     * <li>Создать пользовательский класс userClass и его тип userCase</li>
     * <li>В классе userClass создать атрибут attr с кодом 'testCat' типа "Элемент справочника"
     * по справочнику catalog</li>
     * <li>В классе userClass создать вычислимый атрибут compAttr типа "Элемент справочника" по каталогу catalog,
     * скрипт вычисления:
     *      <pre>
     *      ----------------------------------------------------------------
     *      return subject.testCat?.parent ?: subject.testCat
     *      ----------------------------------------------------------------
     *      </pre>
     * </li>
     * <li>Добавить группу атрибутов groupAttr с атрибутами: attr, compAttr</li>
     * <li>На карточку Компании вывести контент "Список объектов" класса userClass по группе groupAttr</li>
     * <li>Создать объект userCase класса userClass, в качестве значения атрибута attr выбрать child</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку Компании</li>
     * <li>Проверить, что атрибут compAttr имеет значение элемента parent для объекта user</li>
     * </ol>
     */
    @Test
    public void testComputableAttributeCatalogItemWithParent()
    {
        //Подготовка
        Catalog catalog = DAOCatalog.createUser(false, false);
        DSLCatalog.add(catalog);
        CatalogItem parent = DAOCatalogItem.createUser(catalog);
        DSLCatalogItem.add(parent);
        CatalogItem child = DAOCatalogItem.createUser(catalog, parent);
        DSLCatalogItem.add(child);

        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        Attribute attr = DAOAttribute.createCatalogItem(userClass.getFqn(), catalog, child);
        attr.setCode("testCat");
        Attribute compAttr = DAOAttribute.createCatalogItem(userClass.getFqn(), catalog, null);
        DAOAttribute.changeToComputable(compAttr, "return subject.testCat?.parent ?: subject.testCat");
        DSLAttribute.add(attr, compAttr);

        GroupAttr groupAttr = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(groupAttr, attr, compAttr);

        ContentForm objectList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), groupAttr,
                userClass, userCase);
        DSLContent.add(objectList);

        Bo user = DAOUserBo.create(userCase);
        DSLBo.add(user);

        //Выполнение действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root().getUuid());
        compAttr.setValue(parent.getTitle());objectList.advlist().content().asserts().attrValue(user, compAttr);
    }

    /**
     * Тестирование работы параметра greedyFetchingMaxSize ограничение для загрузки данных списков
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00135
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00139
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00162
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$183510456
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский справочник userCatalog(ввести Название и Код, Плоский, С папками - нет)</li>
     * <li>Создать элементы item1, item2, item3 справочника userCatalog</li>
     * <li>Установить значение ru.naumen.greedyFetching.maxSize равное 2</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на карточку справочника userCatalog</li>
     * <li>Проверить, что вылетела ошибка "Many objects in result"</li>
     * </ol>
     */
    @Test
    public void testGreedyFetchingMaxSizeParameter()
    {
        // Подготовка
        Catalog userCatalog = DAOCatalog.createUser(true, true);
        DSLCatalog.add(userCatalog);

        CatalogItem item1 = DAOCatalogItem.createUser(userCatalog);
        CatalogItem item2 = DAOCatalogItem.createUser(userCatalog);
        CatalogItem item3 = DAOCatalogItem.createUser(userCatalog);
        DSLCatalogItem.add(item1, item2, item3);
        DSLConfiguration.setGreedyFetchingMaxSize(2);

        // Действие
        GUILogon.asSuper();
        GUICatalog.goToCard(userCatalog);
        GUIError.assertDialogError("Many objects in result");
    }
}
