package ru.naumen.selenium.cases.admin.mobile;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.GUIMobileCard;
import ru.naumen.selenium.casesutil.mobile.contents.GUIMobileContentActions;
import ru.naumen.selenium.casesutil.mobile.contents.GUIMobileContentsXpath.PropertiesList.Actions;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileCard;
import ru.naumen.selenium.casesutil.model.mobile.MobilePropertiesList;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тесты на действия в контенте "Параметры объекта" и объекты, участвующие в механизме создания действий.
 *
 * <AUTHOR>
 * @since 19.04.2024
 */
public class MobileObjectCardContentActions2Test extends AbstractTestCase
{
    private static MetaClass userClass, userCase, userCase2;

    /**
     * <ol>
     * <b>Общая часть подготовки:</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать тип userCase класса userClass</li>
     * <li>Создать подтип userCase2 типа userCase</li>
     * <li>Загрузить лицензию, содержащую мобильное приложение</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        userCase2 = DAOUserCase.create(userCase);
        DSLMetaClass.add(userClass, userCase, userCase2);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);
    }

    /**
     * Тестирование отображения параметризованных ДПС в контенте "Параметры объекта", если ДПС ограничено на тип, а
     * карточка объекта ограничена на класс
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$249824820
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать и включить ДПС eventAction:
     * <ul>
     *     <li>Объекты: userCase</li>
     *     <li>Событие: [пользовательское событие]</li>
     *     <li>Действие: скрипт</li>
     *     <li>Код скрипта: return true</li>
     * </ul></li>
     * <li>Создать карточку card для класса userClass в МК</li>
     * <li>Создать контент content типа "Параметры объекта" на карточке объекта card</li>
     * <b>Действия и проверки:</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на карточку card во вкладке "Мобильное приложение"</li>
     * <li>Открыть настройки действий в контенте content</li>
     * <li>Открыть форму добавления действия в контенте</li>
     * <li>Проверить, что в списке возможных значений поля "Действие" eventAction доступно для выбора</li>
     * </ol>
     */
    @Test
    public void testPropertiesListHasUserActionAvailableByCases()
    {
        //Подготовка:
        ScriptInfo script = DSLScriptInfo.createScriptInfo("return true");

        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script, true, userCase);
        DSLEventAction.add(eventAction);

        MobileCard card = DAOMobile.createMobileCard(userClass);
        DSLMobile.add(card);

        MobilePropertiesList content = DAOMobile.createMobilePropertiesList();
        DSLMobile.addContents(card, content);

        //Действия и проверки:
        GUILogon.asSuper();
        GUIMobileCard.goToCard(card);
        GUIMobileContentActions.openContentActionsSettings(content);
        GUIMobileCard.clickAddElement();

        GUISelect.assertDisplayed(Actions.OBJECT_ACTION_VALUE_INPUT_XPATH, eventAction.getUserEventUuid());
    }

    /**
     * Тестирование отображения параметризованных ДПС в контенте "Параметры объекта", если ДПС ограничено на подтип, а
     * карточка объекта ограничена на тип с подтипами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$249824820
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать и включить ДПС eventAction:
     * <ul>
     *     <li>Объекты: userCase2</li>
     *     <li>Событие: [пользовательское событие]</li>
     *     <li>Действие: скрипт</li>
     *     <li>Код скрипта: return true</li>
     * </ul></li>
     * <li>Создать карточку card для типа userCase и его подтипа userCase2 в МК</li>
     * <li>Создать контент content типа "Параметры объекта" на карточке объекта card</li>
     * <b>Действия и проверки:</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на карточку card во вкладке "Мобильное приложение"</li>
     * <li>Открыть настройки действий в контенте content</li>
     * <li>Открыть форму добавления действия в контенте</li>
     * <li>Проверить, что в списке возможных значений поля "Действие" eventAction доступно для выбора</li>
     * </ol>
     */
    @Test
    public void testPropertiesListHasUserActionAvailableBySubcases()
    {
        //Подготовка:
        ScriptInfo script = DSLScriptInfo.createScriptInfo("return true");

        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script, true, userCase2);
        DSLEventAction.add(eventAction);

        MobileCard card = DAOMobile.createMobileCard(userClass);
        card.setCases(userCase, userCase2);
        DSLMobile.add(card);

        MobilePropertiesList content = DAOMobile.createMobilePropertiesList();
        DSLMobile.addContents(card, content);

        //Действия и проверки:
        GUILogon.asSuper();
        GUIMobileCard.goToCard(card);
        GUIMobileContentActions.openContentActionsSettings(content);
        GUIMobileCard.clickAddElement();

        GUISelect.assertDisplayed(Actions.OBJECT_ACTION_VALUE_INPUT_XPATH, eventAction.getUserEventUuid());
    }
}
