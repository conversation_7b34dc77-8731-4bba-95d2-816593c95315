package ru.naumen.selenium.cases.admin.system;

import java.util.Random;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.schedulertask.DSLSchedulerTask;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тесты на источник данных планировщика
 *
 * <AUTHOR>
 * @since 01.07.2024
 */
public class SchedulerDataSourceTest extends AbstractTestCase
{
    private static final int DEFAULT_DATASOURCE_TIMEOUT_SECOND = 15;
    private static final int MAX_DATASOURCE_TIMEOUT_SECOND = 3600;

    private final Random random = new Random();

    /**
     * Тестирование изменения тайм-аута соединений к ресурсам планировщика в БД, изначально заданного параметром
     * <code>quartz.datasource.connection.timeout</code>.
     * https://projects.naumen.ru/ServiceDesk/Servers/Quartz_Scheduler <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$268229728 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Сформировать случайное значение таймаута customConnectionTimeout и ожидаемое значение таймаута
     * expectedTimeout</li>
     * <b>Действия и проверки</b>
     * <li>Установить значение customConnectionTimeout в качестве таймаута соединений планировщика</li>
     * <li>Проверить, что в качестве таймаута соединений планировщика установлено значение expectedTimeout</li>
     * </ol>
     */
    @Test
    public void testSetSchedulerDataConnectionTimeout()
    {
        //Подготовка
        int customConnectionTimeout = random.nextInt();
        int expectedTimeout = customConnectionTimeout > MAX_DATASOURCE_TIMEOUT_SECOND
                || customConnectionTimeout < DEFAULT_DATASOURCE_TIMEOUT_SECOND ? 15 : customConnectionTimeout;

        //Действия
        DSLSchedulerTask.setDataSourceConnectionTimeout(customConnectionTimeout);

        //Проверки
        Assert.assertEquals("Тайм-аут соединений источника данных планировщика имеет не корректное значение!",
                expectedTimeout, DSLSchedulerTask.getDataSourceConnectionTimeout());
    }
}
