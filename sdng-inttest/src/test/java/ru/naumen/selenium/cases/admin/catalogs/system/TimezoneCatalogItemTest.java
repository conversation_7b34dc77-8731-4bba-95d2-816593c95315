package ru.naumen.selenium.cases.admin.catalogs.system;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.catalog.TimeZones;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.personalsettings.GUIPersonalSettings;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.util.DateTimeUtils;
import java.text.SimpleDateFormat;
import java.util.concurrent.TimeUnit;

/**
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00320
 *  Тестирование элементов системного справочника Часовые пояса
 * <AUTHOR>
 * @since 12.01.2012
 *
 */
public class TimezoneCatalogItemTest extends AbstractTestCase
{
    private static final String TIMEZONE_CODE_INPUT = GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE + GUIXpath.Input.INPUT_PREFIX;
    //Формат даты для сообщения об ошибке
    private SimpleDateFormat dateFormat = new SimpleDateFormat(DateTimeUtils.DD_MM_YYYY_HH_MM_SS);

    /**
     * Тестирование добавления элемента справочника Часовые пояса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00320
     * <br>
     * <ol>
     * <b>Подготовка.</b>нет
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника Часовые пояса</li>
     * <li>В блоке Элементы справочника нажимаем кнопку Добавить</li>
     * <li>На форме добавления атрибута заполнить поля: название, выбрать код Asia/Ashkhabad</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Часовые пояса, блок Элементы справочника: название , код Asia/Ashkhabad</li>
     * <li>Карточка элемента: название , код Asia/Ashkhabad</li>
     * </ol>
     */
    @Test
    public void testAddSimpleTimezoneCatalogItem()
    {
        //Выполнение действия
        GUILogon.asSuper();
        CatalogItem item = DAOCatalogItem.createTimeZone("Asia/Ashkhabad");
        GUICatalogItem.callAddForm(item);
        GUIForm.assertDialogAppear("Форма добавления элемента справочника \"Часовые пояса\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        tester.sendKeys(TIMEZONE_CODE_INPUT, "Asia/Ashkhabad");
        GUISelect.selectByXpath(TIMEZONE_CODE_INPUT,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, item.getCode()));
        GUIForm.applyModalForm();
        //Проверки
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тест на дефект 834  - не все часовые пояса в списке
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00320
     * <br>
     * <ol>
     * <b>Подготовка.</b>нет
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника Часовые пояса</li>
     * <li>В блоке Элементы справочника нажимаем кнопку Добавить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем что пояса:Alaska Atlantic Ecuador Georgia Hawaii Yekaterinburg на месте</li>
     * <li>Карточка элемента: название , код Asia/Ashkhabad</li>
     * </ol>
     */
    @Test
    public void testAssertTimeZonePresent()
    {
        //Выполнение действия
        GUILogon.asSuper();
        CatalogItem item = DAOCatalogItem.createTimeZone("Asia/Ashkhabad");
        GUICatalogItem.callAddForm(item);
        GUIForm.assertDialogAppear("Форма добавления элемента справочника \"Часовые пояса\" не появилась.");
        tester.click(TIMEZONE_CODE_INPUT);

        tester.sendKeys(TIMEZONE_CODE_INPUT, "US/Alaska");
        GUISelect.selectByXpath(TIMEZONE_CODE_INPUT, String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "US/Alaska"));
        tester.sendKeys(TIMEZONE_CODE_INPUT, "US/Alaska");
        GUISelect.selectByXpath(TIMEZONE_CODE_INPUT, String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "US/Alaska"));
        tester.sendKeys(TIMEZONE_CODE_INPUT, "US/Alaska");
        GUISelect.selectByXpath(TIMEZONE_CODE_INPUT, String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "US/Alaska"));
        tester.sendKeys(TIMEZONE_CODE_INPUT, "America/Barbados");
        GUISelect.selectByXpath(TIMEZONE_CODE_INPUT,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "America/Barbados"));
        tester.sendKeys(TIMEZONE_CODE_INPUT, "Africa/Tunis");
        GUISelect.selectByXpath(TIMEZONE_CODE_INPUT,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "Africa/Tunis"));
        tester.sendKeys(TIMEZONE_CODE_INPUT, "Europe/Uzhgorod");
        GUISelect.selectByXpath(TIMEZONE_CODE_INPUT,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "Europe/Uzhgorod"));
        tester.sendKeys(TIMEZONE_CODE_INPUT, "America/Guayaquil");
        GUISelect.selectByXpath(TIMEZONE_CODE_INPUT,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "America/Guayaquil"));
        tester.sendKeys(TIMEZONE_CODE_INPUT, "Asia/Tbilisi");
        GUISelect.selectByXpath(TIMEZONE_CODE_INPUT,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "Asia/Tbilisi"));
        tester.sendKeys(TIMEZONE_CODE_INPUT, "HST");
        GUISelect.selectByXpath(TIMEZONE_CODE_INPUT, String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "HST"));
        tester.sendKeys(TIMEZONE_CODE_INPUT, "Asia/Kathmandu");
        GUISelect.selectByXpath(TIMEZONE_CODE_INPUT,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "Asia/Kathmandu"));
    }

    /**
     * Тестирование удаления элемента справочника Часовые пояса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00320
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника Часовые пояса. А (America/Marigot)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника Часовые пояса</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму Удалить</li>
     * <li>Подтверждаем удаление</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Часовые пояса, блок Элементы справочника: элемент А отсутствует</li>
     * </ol>
     */
    @Test
    public void testDeleteTimezoneCatalogItem()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createTimeZone("America/Marigot");
        DSLCatalogItem.add(item);
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(item);
        GUICatalog.clickPictogramRemoveAndConfirm(item);

        //Проверки
        GUICatalogItem.assertAbsence(item);
    }

    /**
     * Тестирование редактирования элемента справочника Часовые пояса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00320
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника Часовые пояса А (America/Yakutat)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника Часовые пояса</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму редактировать</li>
     * <li>На форме редактирования элемента проверяем, что код не редактируется.</li>
     * <li>На форме редактирования элемента: меняем название</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Часовые пояса: название и код</li>
     * <li>Карточка элемента: название и код.</li>
     * </ol>
     */
    @Test
    public void testEditTimezoneCatalogItem()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createTimeZone("America/Yakutat");
        DSLCatalogItem.add(item);
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(item);
        item.setTitle(ModelUtils.createTitle());
        GUICatalog.clickPictogram(item, "edit");
        GUIForm.assertDialogAppear("Форма редактирования элемента справочника \"Часовые пояса\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        String actual = tester.getText(GUIXpath.PropertyDialogBoxContent.PROPERTY_DIALOG_BOX_CODE_CAPTION + "/span").trim();
        String message = String.format(
                "Код элемента справочника \"Часовые пояса\" на форме редактирования не совпал с ожидаемым. Ожидаемый код:%s; полученный код:%s",
                item.getCode(), actual);
        Assert.assertTrue(message, actual.contains(item.getCode()));
        GUIForm.applyModalForm();
        //Проверки
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование корректного отображения текущего времени в справочнике "Часовые пояса" при выборе разных часовых поясов в персональных настройках пользователя
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00320
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$100614467
     * <ol>
     * <b>Подготовка:</b>
     * <li>В системном справочнике "Часовые пояса" создать 2 часовых пояса: Екатеринбург и Москва
     * <b>Выполнение действий и проверки:</b>
     * <li>Зайти в систему под суперпользователем</li>
     * <li>Изменить в персональных настройках у суперпользователя часовой пояс на "Екатеринбург"</li>
     * <li>Перейти на страницу справочника "Часовые пояса"</li>
     * <li>Проверка: Текущее время сервера на карточке справочника = реальному текущему времени (new Date())</li>
     * <li>Проверка: Текущее время в часовом поясе "Екатеринбург" = реальному текущему времени (new Date()) с
     * учетом разницы с Екатеринбургом</li>
     * <li>Проверка: Текущее время в часовом поясе "Москва" = реальному текущему времени (new Date()) с учетом
     * разницы с Москвой</li>
     * <li>Изменить в персональных настройках у суперпользователя часовой пояс на "Москва"</li>
     * <li>Проверка: Текущее время сервера на карточке справочника = реальному текущему времени (new Date()) с
     * учетом разницы с Екатеринбургом</li>
     * <li>Проверка: Текущее время в часовом поясе "Екатеринбург" = реальному текущему времени (new Date())</li>
     * <li>Проверка: Текущее время в часовом поясе "Москва" = реальному текущему времени (new Date()) с учетом
     * разницы с Москвой</li>
     * </ol>
     */
    @Test
    public void testCurrentTimeAtTimezoneCatalogItem()
    {
        //Подготовка
        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);
        CatalogItem timeZoneMsk = DAOCatalogItem.createTimeZone(TimeZones.MOSCOW);
        CatalogItem timeZoneEkb = DAOCatalogItem.createTimeZone(TimeZones.YEKATERINBURG);
        DSLCatalogItem.add(timeZoneMsk, timeZoneEkb);

        //Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToPersonalSettings();
        GUIPersonalSettings.selectTimeZone(TimeZones.YEKATERINBURG);
        GUIForm.applyForm();
        GUICatalogItem.goToCatalog(timeZoneEkb);

        assertServerTimeCompare();
        assertTimeZoneAndServerTimeCompare(timeZoneEkb);
        assertTimeZoneAndServerTimeCompare(timeZoneMsk);

        GUINavigational.goToPersonalSettings();
        GUIPersonalSettings.selectTimeZone(TimeZones.MOSCOW);
        GUIForm.applyForm();
        GUICatalogItem.goToCatalog(timeZoneMsk);

        assertServerTimeCompare();
        assertTimeZoneAndServerTimeCompare(timeZoneEkb);
        assertTimeZoneAndServerTimeCompare(timeZoneMsk);
    }

    /**
     * Проверяет, что текущее время в Часовом поясе элемента справочника в таблице элементов справочника Часовые пояса
     * совпадает с текущим временм для соответствующего часового пояса c погрешностью 3 сек
     * @param timeZone элемент справочника Часовые пояса, для которого проверяем текущее время
     */
    private void assertTimeZoneAndServerTimeCompare(CatalogItem timeZone)
    {
        //Приводим полученное значение времени ко времени в часовом поясе сервера для сравнения
        long currentTimeElement = DateTimeUtils.getDateFromString(GUICatalog.getTimeZoneCurrentTime(timeZone),
                DateTimeUtils.DD_MM_YYYY_HH_MM_SS, timeZone.getCode()).getTime();
        long currentTimeServer = System.currentTimeMillis();

        //Формируем сообщение об ошибке
        String msg =
                "Время не совпало: текущее время в часовом поясе " + timeZone.getCode() + " = " + dateFormat.format(currentTimeElement) +
                        ", ожидаемое текущее время = " + dateFormat.format(currentTimeServer);
        Assert.assertTrue(msg, Math.abs(currentTimeServer - currentTimeElement) < TimeUnit.SECONDS.toMillis(3)); // погрешность 3 сек
    }

    /**
     * Проверяет, что время в атрибуте "Текущее время в данном часовом поясе" на карточке справочника Часовые пояса
     * совпадает с текущим временм сервера c погрешностью 3 сек
     */
    private void assertServerTimeCompare()
    {
        long catalogServerTime = DateTimeUtils.getDateFromString(GUICatalog.getServerCurrentTime(),
                DateTimeUtils.DD_MM_YYYY_HH_MM_SS, null).getTime();
        long currentTimeServer = System.currentTimeMillis();

        //Формируем сообщение об ошибке
        String msg =
                "Время не совпало: текущее время в часовом поясе сервера на карточке справочника = " + dateFormat.format(catalogServerTime) +
                        ", ожидаемое текущее время = " + dateFormat.format(currentTimeServer);
        Assert.assertTrue(msg,
                Math.abs(currentTimeServer - catalogServerTime) < TimeUnit.SECONDS.toMillis(3)); // погрешность 3 сек
    }
}
