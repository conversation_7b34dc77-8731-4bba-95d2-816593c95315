package ru.naumen.selenium.cases.admin.system.administration;

import java.io.File;
import java.nio.file.Paths;
import java.util.Set;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.admin.GUIConsole;
import ru.naumen.selenium.casesutil.admin.GUIConsole.LogLevel;
import ru.naumen.selenium.casesutil.admin.GUIScriptField;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScript;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.scripts.DSLLog;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.core.exception.DialogErrorException;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.FileUtils;

/**
 * Тестирование раздела консоль
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
 * <AUTHOR>
 * @since 20.05.2013
 */
public class ConsoleTest extends AbstractTestCase
{
    private static final String CONSOLE_GROOVY = Config.get().getResourceDir() + "testScriptFromFileConsole.groovy";

    /**
     * Тестирование автоматического обновления консоли
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * <ol>
     * <b>Проверки</b>
     * <li>Заходим на страницу Консоль.
     * <li>Выбираем уровень логирования ERROR
     * <li>Нажимаем галку автоматического обновления
     * <li>Выполняем скрипт logger.error('prefix')
     * <li>Проверяем что в консоли появился текст prefix
     * </ol>
     */
    @Test
    public void testAutoUpdate()
    {
        String prefix = ModelUtils.createTitle();
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        GUIConsole.setLogLevel(LogLevel.ERROR);
        GUIConsole.setAutoUpdate(true);
        ScriptRunner script = new ScriptRunner(String.format("logger.error('" + prefix + "');"));
        script.runScript();
        GUITester.assertPresent(GUIConsole.LOG_CONTAINER + "//div[contains(text(), '" + prefix + "')]",
                "В логе отсутствует запись о " + prefix);
        GUIConsole.assertPresent(prefix);
    }

    /**
     * Тестирование скрипта фильтрации, использующего обратную ссылку, на форме добавления запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00461
     * http://sd-jira.naumen.ru/browse/NSDPRD-5980
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создаем тип запроса scCase</li>
     * <li>Создаем тип сотрудника employeeCase</li>
     * <li>Создаем сотрудника employee типа employeeCase</li>
     * <li>Создаем соглашение agreement стандартного типа</li>
     * <li>Делаем employee получателем agreement</li>
     * <li>Создаем атрибут link ссылочного типа в типе scCase на класс запроса</li>
     * <li>Создаем атрибут backLink с кодом backLink типа "обратная ссылка" в типе scCase на атрибут link, тип scCase</li>
     * <li>Создаем атрибут testAttr ссылочного типа в типе scCase на класс запроса</li>
     * <li>Добавляем атрибуту testAttr скрипт фильтрации при редактировании:</li>
     * <li>
     *      <pre>
     *          if (subject==null)
     *          return []
     *          logger.error('BackLink = ' + subject?.backLink)
     *          return []
     *      </pre>
     * </li>
     * <li>Создать группу groupAttr, поместить в нее атрибуты backLink, testAttr</li>
     * <li>Создать контент "Параметры на форме" с группой атрибутов groupAttr на форме добавления запроса типа scCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Нажимаем кнопку "Добавить запрос"</li>
     * <li>Выбираем соглашение agreement на открывшейся форме</li>
     * <li>Выбираем тип запроса scCase</li>
     * <li>Заходим под суперпользователем</li>
     * <li>Переходим в консоль</li>
     * <li>Ставим уровень логирования ERROR</li>
     * <li>Устанавливаем фильтр по строке "BackLink"</li>
     * <li>Обновляем лог</li>
     * <br>
     * <b>Проверка</b>
     * <li>В логе есть строка строка "BackLink = []"</li>
     * </ol>
     */
    @Test
    public void testBackLinkIsNotNull()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        DSLBo.add(agreement);
        DSLAgreement.addToRecipients(agreement, employee);

        Attribute link = DAOAttribute.createObjectLink(scCase, DAOScCase.createClass(), null);
        Attribute backLink = DAOAttribute.createBackBOLinks(scCase.getFqn(), link, scCase);
        backLink.setCode("backLink");
        Attribute testAttr = DAOAttribute.createObjectLink(scCase, DAOScCase.createClass(), null);
        String script = "if (subject==null)\n " + "return []\n" + "logger.error('BackLink = ' + subject?.backLink)\n"
                + "return []";
        DAOAttribute.changeToEditFilter(testAttr, script);
        DSLAttribute.add(link, backLink, testAttr);

        GroupAttr groupAttr = DAOGroupAttr.create(scCase);
        DSLGroupAttr.add(groupAttr, backLink, testAttr);

        ContentForm content = DAOContentAddForm.createEditablePropertyList(scCase, groupAttr);
        DSLContent.add(content);

        //Выполнение действий
        GUILogon.login(employee);
        GUIButtonBar.addSC();
        GUISc.selectAssociation(agreement, null);
        GUISc.selectScCase(scCase);
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        GUIConsole.setLogLevel(LogLevel.ERROR);
        GUIConsole.setFilter("BackLink");
        GUIConsole.refreshLog();

        //Проверка
        GUIConsole.assertPresent("BackLink = []");
    }

    /**
     * Тестирование очистки консоли
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в Консоль</li>
     *  <li>Очищаем, выствляем уровень фильтрации на INFO</li>
     * <li>Выполняем скрипт: logger.info('prefix');</li>
     * <li>Обновляем консоль</li>
     * <li>Проверяем, что сообщение prefix присутствует в логе</li>
     * <li>Очищаем консоль</li>
     * <li>Проверяем, что сообщение prefix отсутствует в логе</li>
     * </ol>
     */
    @Test
    public void testClear()
    {
        String scriptPattern = "logger.info('%s');";
        GUILogon.asSuper();
        GUIConsole.goToConsole();

        GUIConsole.clearLog();
        GUIConsole.setLogLevel(LogLevel.INFO);
        String prefix = ModelUtils.createCode();
        ScriptRunner script = new ScriptRunner(String.format(scriptPattern, prefix));
        script.runScript();
        GUIConsole.refreshLog();
        GUIConsole.assertPresent(prefix);
        GUIConsole.clearLog();
        GUIConsole.assertAbsense(prefix);
    }

    /**
     * Тестирование логирования скриптов, выполняемых в консоли
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в Консоль</li>
     * <li>Выполнить скрипт: return 'prefix';</li>
     * <li>Обновить консоль</li>
     * <li>Проверить, что сообщение prefix присутствует в логе в 1 экземпляре</li>
     * <li>Выполнить скрипт, содержащий ошибку: def a = '" + prefix + "'; returns;</li>
     * <li>Обновить консоль</li>
     * <li>Проверить, что сообщение prefix присутствует в логе в 1 экземпляре</li>
     * <li>Выполнить скрипт в тихом режиме: /\*&silent*\/ return 'prefix';</li>
     * <li>Обновить консоль</li>
     * <li>Проверить, что сообщение prefix в логе отсутствует</li>
     * <li>Выполнить скрипт в тихом режиме, содержащий ошибку: /\*&silent*\/def a = '" + prefix + "'; returns;</li>
     * <li>Обновить консоль</li>
     * <li>Проверить, что сообщение prefix в логе отсутствует</li>
     * </ol>
     */
    @Test
    public void testHideScriptBody()
    {
        String prefix = ModelUtils.createTitle();
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        GUIConsole.runGroovyScriptInText("return '" + prefix + "';");
        GUIConsole.refreshLog();
        GUIConsole.assertPresentWithCount(prefix, 1);

        prefix = ModelUtils.createTitle();
        GUIConsole.runScriptInTextWithoutChecks("def a = '" + prefix + "'; returns;");
        WaitTool.waitMills(2000);
        GUIError.ignoreError(2);
        GUIConsole.refreshLog();
        GUIConsole.assertPresentWithCount(prefix, 1);

        prefix = ModelUtils.createTitle();
        GUIConsole.runGroovyScriptInText("/*&silent*/ return '" + prefix + "';");
        GUIConsole.refreshLog();
        GUIConsole.assertAbsense(prefix);

        prefix = ModelUtils.createTitle();
        GUIConsole.runScriptInTextWithoutChecks("/*&silent*/ def a = '" + prefix + "'; returns;");
        WaitTool.waitMills(2000);
        GUIError.ignoreError(2);
        GUIConsole.refreshLog();
        GUIConsole.assertAbsense(prefix);
    }

    /**
     * Тестирование отсутствия в логе сообщения о загрузке LazyScriptDtObject из базы данных.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * http://sd-jira.naumen.ru/browse/NSDPRD-6136
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать строковый атрибут strAttr в типе ouCase, задать ему произвольное значение по умолчанию</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в консоль</li>
     * <li>Выполнить в консоли скрипт:<br>
     * <pre>logger.trace(utils.find('%FQN типа ouCase%', [:])[0].%код атрибута strAttr%)</pre></li>
     * <li>Установить уровень логирования TRACE, обновить лог</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что в логе есть запись уровня TRACE:
     * "script.Script - %значение по умолчанию атрибута strAttr%"</li>
     * <li>Проверить, что в логе отсутствует запись уровня TRACE:
     * "script.Script - Used lazy object uploaded data of DataBase for..."</li>
     * </ol>
     */
    @Test
    public void testLazyObjectTraceMessageAbsence()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);
        Attribute strAttr = DAOAttribute.createString(ouCase);
        strAttr.setDefaultValue(ModelUtils.createTitle());
        DSLAttribute.add(strAttr);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        // Выполнение действий
        GUILogon.asSuper();
        GUINavigational.goToConsole();
        GUIConsole.runGroovyScriptInText(
                String.format("logger.trace(utils.find('%s', [:])[0].%s)", ouCase.getFqn(), strAttr.getCode()));
        GUIConsole.setLogLevel(LogLevel.TRACE);
        GUIConsole.refreshLog();
        // Проверки
        GUIConsole.assertPresent("script.Script - " + strAttr.getDefaultValue());
        GUIConsole.assertAbsense("script.Script - Used lazy object uploaded data of DataBase for");
    }

    /**
     * Тестирование фильтра логов консоли
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в Консоль</li>
     * <li>Очищаем, выставляем уровень фильтрации на INFO</li>
     * <li>Выполняем два скрипта: logger.info('prefix+present'); logger.info('absense');</li>
     * <li>Обновляем консоль</li>
     * <li>Выставляем фильтр по prefix</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сообщение prefix+present присутствует в логе</li>
     * <li>Сообщение absense отсутствует в логе</li>
     * </ol>
     */
    @Test
    public void testLogFilter()
    {
        String scriptPattern = "logger.info('%s');";
        GUILogon.asSuper();
        GUIConsole.goToConsole();

        GUIConsole.clearLog();
        GUIConsole.setLogLevel(LogLevel.INFO);
        String prefix = ModelUtils.createCode();
        String present = ModelUtils.createCode();
        String absense = ModelUtils.createCode();
        ScriptRunner script1 = new ScriptRunner(String.format(scriptPattern, prefix + present));
        script1.runScript();
        ScriptRunner script2 = new ScriptRunner(String.format(scriptPattern, absense));
        script2.runScript();
        GUIConsole.setFilter(prefix);
        GUIConsole.refreshLog();
        GUIConsole.assertPresent(present);
        GUIConsole.assertAbsense(absense);
    }

    /**
     * Тестирование фильтров по уровню логирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * <ol>
     * <b>Проверки</b>
     * <li>Для каждого фильтра выполняем скрипт, выводящий уникальные сообщения на каждом уровне</li>
     * <li>Проверяем, что в фильтр попали только сообщния уровня и выше</li>
     * </ol>
     */
    @Test
    public void testLogLevels()
    {
        String scriptPattern = "logger.error('%1$sERROR');%n logger.warn('%1$sWARN');%n logger.info('%1$sINFO');%n logger.debug('%1$sDEBUG');%n logger.trace('%1$sTRACE');%n";
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        for (LogLevel selectedLevel : LogLevel.values())
        {
            GUIConsole.clearLog();
            GUIConsole.setLogLevel(selectedLevel);

            String hash = ModelUtils.createCode();
            ScriptRunner script = new ScriptRunner(String.format(scriptPattern, hash));
            script.runScript();
            GUIConsole.refreshLog();

            for (LogLevel assertLevel : LogLevel.values())
            {
                if (assertLevel.equals(LogLevel.ALL))
                {
                    continue;
                }
                if (assertLevel.getValue() <= selectedLevel.getValue())
                {
                    GUIConsole.assertPresent(hash + assertLevel.getCode());
                }
                else
                {
                    GUIConsole.assertAbsense(hash + assertLevel.getCode());
                }
            }
        }
    }

    /**
     * Тестирование сохранения лога консоли
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * <ol>
     * <b>Проверки</b>
     * <li>Заходим на страницу Консоль.
     * <li>Выбираем уровень логирования ERROR
     * <li>Выполняем скрипт logger.error('prefix')
     * <li>Нажимаем обновить консоль.
     * <li>Нажимаем Сохранить консоль
     * <li>Проверяем, что загрузился файл и в нем есть текст
     * </ol>
     */
    @Test
    public void testSaveConsole()
    {
        String prefix = ModelUtils.createTitle();
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        GUIConsole.setLogLevel(LogLevel.ERROR);
        GUIConsole.refreshLog();
        GUIConsole.clearLog();
        ScriptRunner script = new ScriptRunner(String.format("logger.error('" + prefix + "');"));
        script.runScript();
        GUIConsole.refreshLog();
        GUIConsole.assertPresent(prefix);
        //действие и проверки
        String logFileName = GUIConsole.exportLog();
        GUIError.waitError(GUIConsole.TIME_WAIT_ERROR);
        Assert.assertTrue("Файл с логом не содержит искомой подстароки",
                FileUtils.readAll(logFileName).contains(prefix));
    }

    /**
     * Тестирование сохранения тела консольного скрипта после перехода на другую страницу
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти на страницу Консоль</li>
     * <li>Ввести произвольный текст в поле ввода скрипта</li>
     * <li>Перейти на форму редактирования любого скрипта и изменить тело скрипта</li>
     * <li>Вернуться на страницу Консоль</li>
     * <li>Проверить, что в поле ввода скрипта находися введенный ранее текст</li>
     * </ol>
     */
    @Test
    public void testSaveConsoleScript()
    {
        // Подготовка
        DSLConfiguration.saveInLocalStorage(true);
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo("test script");
        DSLScriptInfo.addScript(scriptInfo);

        // Выолнение действий и проверки
        GUILogon.asSuper();
        GUIConsole.goToConsole();

        String scriptBody = ModelUtils.createText(20);
        GUIScriptField.sendKeys(GUIScriptField.FIELD_INPUT_SCRIPT, scriptBody);
        GUITester.assertTextContainsWithMsg(GUIScriptField.FIELD_INPUT_SCRIPT, scriptBody, "Текст не введён в консоль");

        GUIScript.goToCard(scriptInfo);
        GUIScript.editScriptBody("[]");

        GUIConsole.goToConsole();
        GUIScriptField.assertContainsText(scriptBody);
    }

    /**
     * Тестирование блокировки кнопки выполнения скрипта из файла при выполнении скрипта из файла
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * http://sd-jira.naumen.ru/browse/NSDPRD-2697
     * <ol>
     * <b>Выполнение действия</b>
     * <li>Заходим на страницу Консоль</li>
     * <li>Загружаем файл со скриптом:
     * <pre>
     *   --------------------------------
     *  def SECONDS = 5;
     *  def obj = new Object();
     *  synchronized (obj){ obj.wait(SECONDS * 1000); }
     *   --------------------------------
     * </pre>
     * </li>
     * <b>Проверки</b>
     * <li>Кнопка 'Выполнить скрипт' задизейблена</li
     * <li>Форма 'Выполнение скрипта из файла' скрыта</li>
     * <li>Диалог с результатом выполнения скрипта появился</li>
     * <li>Кнопка 'Выполнить скрипт' не задизейблена</li>
     * <li>Форма 'Выполнение скрипта из файла' не скрыта</li>
     * </ol>
     */
    @Test
    public void testScriptFromFileConsoleDisabled()
    {
        //Выполнение действий
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        String script = "def SECONDS = 5; def obj = new Object(); synchronized (obj){ obj.wait(SECONDS * 1000); }";
        FileUtils.saveDataInFile(CONSOLE_GROOVY, script);
        Cleaner.afterTest(() -> FileUtils.forceDelete(CONSOLE_GROOVY));

        File file = Paths.get(CONSOLE_GROOVY).toFile();
        tester.sendKeysWithoutClear(GUIConsole.FILE_UPLOAD, file.getAbsolutePath());

        //Проверка, что кнопка 'Выполнить скрипт' задизейблена и форма 'Выполнение скрипта из файла' скрыта
        GUIConsole.assertScriptFromFileDisabled();

        //Дожидаемся окончания работы скрипта
        GUIForm.assertQuestionAppear("Диалог с результатом выполнения скрипта не появился");

        //Проверка, что кнопка 'Выполнить скрипт' не задизейблена и форма 'Выполнение скрипта из файла' не скрыта
        GUIConsole.assertScriptFromFileEnabled();
    }

    /**
     * Тестирование выполнения скрипта из файла
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * <ol>
     * <b>Проверки</b>
     * <li>Заходим на страницу Консоль.
     * <li>Загружаем файл со скриптом
     * <li>Проверяем результат выполнения скрипта
     * </ol>
     */
    @Test
    public void testScriptFromFileConsoleTest()
    {
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        FileUtils.saveDataInFile(CONSOLE_GROOVY, "def a = 4; a += 1;\nreturn a;");
        Cleaner.afterTest(() -> FileUtils.forceDelete(CONSOLE_GROOVY));
        String result = GUIConsole.runGroovyScriptFromConsole(CONSOLE_GROOVY);
        Assert.assertEquals("Скрипт возвратил неправильный результат", result, "5");
    }

    /**
     * Тестирование блокировки кнопки выполнения скрипта при выполнении скрипта из поля ввода
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * http://sd-jira.naumen.ru/browse/NSDPRD-2697
     * <ol>
     * <b>Выполнение действия</b>
     * <li>Заходим на страницу Консоль</li>
     * <li>Добавляем скрипт в поле ввода:
     * <pre>
     *   --------------------------------
     *  def SECONDS = 5; Thread.sleep(SECONDS * 1000)
     *   --------------------------------
     * </pre>
     * </li>
     * <b>Проверки</b>
     * <li>Кнопка 'Выполнить скрипт' задизейблена</li>
     * <li>Диалог с результатом выполнения скрипта появился</li>
     * <li>Кнопка 'Выполнить скрипт' не задизейблена</li>
     * </ol>
     */
    @Test
    public void testScriptTextConsoleDisabled()
    {
        //Выполнение действий
        GUILogon.asSuper();
        GUIConsole.goToConsole();

        String script = "Thread.sleep(5000)";
        GUIConsole.runScriptInTextWithoutChecks(script);

        //Проверка, что кнопка 'Выполнить скрипт' задизейблена
        GUIConsole.assertScriptButtonsDisabled();

        //Дожидаемся окончания работы скрипта
        GUIForm.assertQuestionAppear("Диалог с результатом выполнения скрипта не появился");

        //Проверка, что кнопка 'Выполнить скрипт' не задизейблена
        GUIConsole.assertScriptButtonsEnabled();
    }

    /**
     * Тестирование выполнения скрипта через консоль
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * <ol>
     * <b>Проверки</b>
     * <li>Проверка, что скрипт выполнился и возвратил правильный результат</li>
     * <li>Проверить, что фокус вернулся в поле ввода кода</li>
     * </ol>
     */
    @Test
    public void testScriptTextConsoleTest()
    {
        String script = "def a = 3; a += 1; return a";
        String focusedXpath = "id(\"gwt-debug-console\")/div[2]/div[1]/div[2]/div[1]/textarea[1]";

        GUILogon.asSuper();
        GUIConsole.goToConsole();
        tester.click(GUIScriptField.FIELD_INPUT_SCRIPT);

        Assert.assertEquals("Скрипт возвратил неправильный результат", GUIConsole.runGroovyScriptInText(script), "4");
        WaitTool.waitMills(1000);
        String currentFocusXpath = tester.getFocusElementXpath();
        Assert.assertEquals("Focus didn't return to text input field!", focusedXpath, currentFocusXpath);
    }

    /**
     * Тестирование сообщения об ошибке компиляции при попытке выполнения скрипта в консоли
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * http://sd-jira.naumen.ru/browse/NSDPRD-5820
     * <br>
     * <ol>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в консоль</li>
     * <li>Выполнить в консоли скрипт:
     * <pre>utils count 'serviceCall', [:]</pre></li>
     * <br>
     * <b>Проверка</b>
     * <li>На экране появилось сообщение об ошибке компиляции скрипта</li>
     * </ol>
     */
    @Test
    public void testSyntaxError()
    {
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        try
        {
            GUIConsole.runGroovyScriptInText("utils count 'serviceCall', [:]");
        }
        catch (DialogErrorException e)
        {
            Assert.assertTrue("Полученное сообщение не является сообщением об ошибке компиляции.",
                    e.getMessage().contains("Script compilation error"));
            GUIError.ignoreError();
        }
    }

    /**
     * Тестирование максимального времени выполнения скрипта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * http://sd-jira.naumen.ru/browse/NSDPRD-2210
     * <ol>
     * <b>Выполнение действия.</b>
     * <li>Заходим на страницу Консоль.</li>
     * <li>Загружаем файл со скриптом:
     * <pre>
     *   --------------------------------
     *   /*& 1 * /
     *   sleep 2000;
     *   return 1;
     *   --------------------------------
     * </pre>
     * </li>
     * <b>Проверки</b>
     * <li>Появилось сообщение об ошибке: Транзакция скрипта отменена, возможно превышено время выполнения.</li>
     * </ol>
     */
    @Test
    public void testTimeoutScript()
    {
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        FileUtils.saveDataInFile(CONSOLE_GROOVY, "/*& 1 */\nsleep 2000;\nreturn 1;");
        Cleaner.afterTest(() -> FileUtils.forceDelete(CONSOLE_GROOVY));
        try
        {
            GUIConsole.runGroovyScriptFromConsole(CONSOLE_GROOVY);
        }
        catch (DialogErrorException e)
        {
            Assert.assertEquals(ErrorMessages.CANCEL_TRANSACTION, e.getMessage());
            GUIError.ignoreError();
        }
    }

    /**
     * Тестирование сворачивания и разворачивания консоли
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * <ol>
     * <b>Проверки</b>
     * <li>Заходим на страницу Консоль.
     * <li>Выбираем уровень логирования ERROR
     * <li>Выполняем скрипт logger.error('prefix')
     * <li>Обновляем консоль
     * <li>Сворачиваем лог
     * <li>Проверяем что лог не отображается
     * <li>Разворачиваем лог
     * <li>Проверяем что лог отображается
     * </ol>
     */
    @Test
    public void testTurnLog()
    {
        String prefix = ModelUtils.createTitle();
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        GUIConsole.setLogLevel(LogLevel.ERROR);
        ScriptRunner script = new ScriptRunner(String.format("logger.error('" + prefix + "');"));
        script.runScript();
        GUIConsole.refreshLog();
        GUIConsole.turnLog();
        GUIConsole.assertLogNotDisplayed(prefix);
        GUIConsole.turnLog();
        GUIConsole.assertLogDisplayed(prefix);
    }

    /**
     * Тестирование сообщения об ошибке песочницы при попытке выполнения скрипта в консоли
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$159015784
     * <br>
     * <ol>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в консоль</li>
     * <li>Выполнить в консоли скрипты</li>
     * <br>
     * <b>Проверка</b>
     * <li>На экране появилось сообщение об ошибке песочницы</li>
     * </ol>
     */
    @Test
    public void testSandboxError()
    {
        Set<String> scripts = Set.of(
                "\"uname -a\".execute()",
                "'uname -a'.execute()",
                "['ls', 'la'].execute()",
                "Runtime.getRuntime().exec(\"uname -a\")",
                "def processBuilder = new ProcessBuilder();\nprocessBuilder.command(\"uname\", \"-a\");",
                "GroovyShell groovyShell = new GroovyShell();\ngroovyShell.run(\"uname -a\", \"scripts\", List.of())",
                "GroovySystem.getMetaClassRegistry()"
        );
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        scripts.forEach(script ->
        {
            try
            {
                GUIConsole.runGroovyScriptInText(script);
                throw new RuntimeException("Полученное сообщение не является ошибкой песочницы.");
            }
            catch (DialogErrorException e)
            {
                Assert.assertTrue("Полученное сообщение не является ошибкой песочницы.",
                        e.getMessage().contains("SandboxSecurityException"));
                GUIError.ignoreError();
            }
        });
    }

    /**
     * Тестирование отсутствия в логах приложения сообщений о различных операциях в приложении, содержащих имя класса
     * "ProgressBeanPostProcessor", в котором логгер должен отключаться после перехвата события старта приложения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00431
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$187747120
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать модель сотрудника employee</li>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в консоль</li>
     * <li>Выполнить добавление и удаление пользователя employee в системе</li>
     * <li>Проверить отсутствие в логах сообщений, содержащих "ProgressBeanPostProcessor"</li>
     * </ol>
     */
    @Test
    public void testProgressBeanPostProcessorLogMessages()
    {
        //Подготовка
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);

        //Действия и проверки
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        GUIConsole.setLogLevel(LogLevel.INFO);
        GUIConsole.setAutoUpdate(true);
        GUIConsole.refreshLog();
        GUIConsole.clearLog();

        DSLBo.add(employee);
        DSLBo.delete(employee);

        tester.waitAsyncCall(2);
        GUIConsole.assertAbsense("ProgressBeanPostProcessor");
    }

    /**
     * Тестирование наличия в логе сообщения об ошибке, возникшей в результате выполнения скрипта из консоли
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/RequirementsLog/Exceptions
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$236461507
     * <br>
     * <ol>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт, выбрасывающий исключение
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     int a = 5/0;
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Проверить, что причина ошибки записалась в лог 1 раз</li>
     * </ol>
     */
    @Test
    public void testStackTracePresenceAfterExecutingConsoleScript()
    {
        String expectedLine = "Caused by: java.lang.ArithmeticException: / by zero";
        new ScriptRunner("int a = 5/0;").assertScriptError("/ by zero");
        DSLLog.assertCountOccurrencesInCurrentTestLogAsString(1, expectedLine);
    }
}
