package ru.naumen.selenium.cases.admin.catalogs.system;

import java.util.List;

import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUIRulesSettings;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование ссылочной целостности таблиц соответствий
 * <AUTHOR>
 * @since 06.08.2014
 */
public class RulesSettingsIntegrityTest extends AbstractTestCase
{
    /**
     * Тестирование архивирования и восстановления только что созданной таблицы соответствий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать таблицу соответствий rulSet (Объекты: Запрос, Определяемые: Влияние, Определяющие: Срочность)</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Поместить в архив rulSet - rulSet в архиве</li>
     * <li>Восстановить из архива rulSet - rulSet не в архиве</li>
     * </ol>
     */
    @Test
    public void testArchiveAndRestoreCreatedRs()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();

        CatalogItem rulSet = DAOCatalogItem.createRulesSettings(scClass, SysAttribute.impact(scClass).getCode(),
                SysAttribute.urgency(scClass).getCode());
        DSLCatalogItem.add(rulSet);

        // Действия и проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCard(rulSet);

        GUIRulesSettings.archive();
        DSLCatalogItem.assertArchived(rulSet);

        GUICatalogItem.restore();
        DSLCatalogItem.assertNotArchived(rulSet);
    }

    /**
     * Тестирование архивирования таблицы соответствий, являющейся значением атрибута
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать таблицу соответствий rulSet (Объекты: Запрос, Определяемые: Влияние, Определяющие: Срочность)</li>
     * <li>В класс Запрос добавить атрибут catalogItemAttr (типа "Элемент справочника", справочник Таблицы соответствий)</li>
     * <li>Создать запрос sc, значение catalogItemAttr = rulSet</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Поместить rulSet в архив, подтвердить</li>
     * <li>rulSet в ахиве</li>
     * </ol>
     */
    @Test
    public void testArchiveRsWhichIsValueOfAttribute()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();

        CatalogItem rulSet = DAOCatalogItem.createRulesSettings(scClass, SysAttribute.impact(scClass).getCode(),
                SysAttribute.urgency(scClass).getCode());
        DSLCatalogItem.add(rulSet);

        Attribute catalogItemAttr = DAOAttribute.createCatalogItem(scClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.RULESSETTINGS), null);
        DSLAttribute.add(catalogItemAttr);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);
        catalogItemAttr.setValue(rulSet.getUuid());
        DSLBo.editAttributeValue(sc, catalogItemAttr);

        // Действия и проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCard(rulSet);
        GUIRulesSettings.archive();
        DSLCatalogItem.assertArchived(rulSet);
    }

    /**
     * Тестирование неархивирования таблицы соответствий, являющейся значением по умолчанию атрибута
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать таблицу соответствий rulSet (Объекты: Запрос, Определяемые: Влияние, Определяющие: Срочность)</li>
     * <li>В класс Запрос добавить атрибут catalogItemAttr (типа "Элемент справочника", справочник Таблицы соответствий, 
     * значение по умолчанию = rulSet)</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Поместить rulSet в архив, подтвердить</li>
     * <li>Ошибка: Элемент справочника 'Таблицы соответствий' 'rulSet' не может быть помещен в архив по следующим причинам:
     * 1. Элемент справочника 'Таблицы соответствий' используется в качестве значения по умолчанию атрибутов: 
     * "attrCatItem" (класс 'Запрос').</li>
     * </ol>
     */
    @Test
    public void testNotArchivingRsWhichIsDefaultValueOfAttribute()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();

        CatalogItem rulSet = DAOCatalogItem.createRulesSettings(scClass, SysAttribute.impact(scClass).getCode(),
                SysAttribute.urgency(scClass).getCode());
        DSLCatalogItem.add(rulSet);

        Attribute catalogItemAttr = DAOAttribute.createCatalogItem(scClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.RULESSETTINGS), rulSet);
        DSLAttribute.add(catalogItemAttr);

        String catalogTitle = SystemCatalog.RULESSETTINGS.getTitle();
        String errorMessage = String.format(
                ErrorMessages.NOT_ARCHIVED_CATALOG_ITEM + "\n" + ErrorMessages.NO_FORMATTED_FIRST
                        + ErrorMessages.CAT_ITEM + "'%s' " + ErrorMessages.USE_AS_DEF_VALUE
                        + "\"%s\" (класс 'Запрос').",
                catalogTitle, rulSet.getTitle(), catalogTitle, catalogItemAttr.getTitle());

        // Действия и проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(rulSet);
        GUICatalogItem.tryToArchiveOrRestoreAndCheckErrorMessage(rulSet, errorMessage);
    }

    /**
     * Тестирование неархивирования таблицы соответствий, с которой связано соглашение
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создаем таблицу соответствий rulesSettings (класс Запрос): определяющий атрибут - Соглашение, 
     * определяемые - приоритет и нормативное время</li>
     * <li>Создаем соглашение agreement, связываем его с rulesSettings</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Пытаемся архивировать rulesSettings</li>
     * <li>Появилось сообщение об ошибке: Элемент справочника 'Таблицы соответствий' 'rulesSettings.TITLE' не может 
     * быть помещен в архив по следующим причинам: 1. Элемент справочника 'Таблицы соответствий' связан со следующими 
     * объектами: соглашение 'agreement.TITLE'</li>
     * <li>Помещаем agreement в архив</li>
     * <li>Пытаемся архивировать rulesSettings</li>
     * <li>Появилось сообщение об ошибке: Элемент справочника 'Таблицы соответствий' 'rulesSettings.TITLE' не может 
     * быть помещен в архив по следующим причинам: 1. Элемент справочника 'Таблицы соответствий' связан со следующими 
     * объектами: соглашение 'agreement.TITLE'*</li>
     * </ol>
     */
    @Test
    public void testNotArchivingRsWithAgreement()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();

        List<MetaClass> metaClasses = Lists.newArrayList(scClass);
        List<String> targetAttrs = Lists.newArrayList(SysAttribute.priority(scClass).getCode(),
                SysAttribute.resolutionTime(scClass).getCode());
        List<String> sourceAttrs = Lists.newArrayList(SysAttribute.agreement(scClass).getCode());
        CatalogItem rulesSettings = DAOCatalogItem.createRulesSettings(metaClasses, targetAttrs, sourceAttrs);
        DSLCatalogItem.add(rulesSettings);

        Bo agreement = DAOAgreement.createWithRules(SharedFixture.agreementCase(), SharedFixture.serviceTime(),
                SharedFixture.serviceTime(), rulesSettings, rulesSettings);
        DSLBo.add(agreement);

        String catalogTitle = SystemCatalog.RULESSETTINGS.getTitle();
        String errorMessage = String.format(
                ErrorMessages.NOT_ARCHIVED_CATALOG_ITEM + "\n" + ErrorMessages.NO_FORMATTED_FIRST
                        + ErrorMessages.RELATED_OBJECT,
                catalogTitle, rulesSettings.getTitle(), catalogTitle, "соглашение", agreement.getTitle());

        // Действия и проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(rulesSettings);
        GUICatalogItem.tryToArchiveOrRestoreAndCheckErrorMessage(rulesSettings, errorMessage);

        DSLBo.archive(agreement);
        tester.refresh();
        GUICatalogItem.tryToArchiveOrRestoreAndCheckErrorMessage(rulesSettings, errorMessage);
    }

    /**
     * Тестирование неудаления таблицы соответствий, являющейся значением по умолчанию атрибута
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать таблицу соответствий rulSet (Объекты: Запрос, Определяемые: Влияние, Определяющие: Срочность)</li>
     * <li>В класс Запрос добавить атрибут catalogItemAttr (типа "Элемент справочника", справочник Таблицы соответствий, 
     * значение по умолчанию = rulSet)</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Удалить rulSet, подтвердить</li>
     * <li>Ошибка: Элемент справочника 'Таблицы соответствий' 'rulSet' не может быть удален по следующим причинам:
     * 1. Элемент справочника 'Таблицы соответствий' используется в качестве значения по умолчанию атрибутов: 
     * "attrCatItem" (класс 'Запрос').</li>
     * </ol>
     */
    @Test
    public void testNotDeletingRsWhichIsDefaultValueOfAttribute()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();

        CatalogItem rulSet = DAOCatalogItem.createRulesSettings(scClass, SysAttribute.impact(scClass).getCode(),
                SysAttribute.urgency(scClass).getCode());
        DSLCatalogItem.add(rulSet);

        Attribute catalogItemAttr = DAOAttribute.createCatalogItem(scClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.RULESSETTINGS), rulSet);
        DSLAttribute.add(catalogItemAttr);

        String catalogTitle = SystemCatalog.RULESSETTINGS.getTitle();
        String errorMessage = String.format(
                ErrorMessages.NOT_DEL_CATALOG_ITEM + "\n" + ErrorMessages.NO_FORMATTED_FIRST + ErrorMessages.CAT_ITEM
                        + "'%s' " + ErrorMessages.USE_AS_DEF_VALUE + "\"%s\" (класс 'Запрос').",
                catalogTitle, rulSet.getTitle(), catalogTitle, catalogItemAttr.getTitle());

        // Действия и проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(rulSet);
        GUICatalogItem.tryToDeleteAndCheckErrorMessage(rulSet, errorMessage);
    }

    /**
     * Тестирование неудаления таблицы соответствий, являющейся значением атрибута
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать таблицу соответствий rulSet (Объекты: Запрос, Определяемые: Влияние, Определяющие: Срочность)</li>
     * <li>В класс Запрос добавить атрибут catalogItemAttr (типа "Элемент справочника", справочник Таблицы соответствий)</li>
     * <li>Создать запрос sc, значение catalogItemAttr = rulSet</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Удалить rulSet, подтвердить</li>
     * <li>Ошибка: Элемент справочника 'Таблицы соответствий' 'rulSet' не может быть удален по следующим причинам:
     * 1. Элемент справочника 'Таблицы соответствий' связан со следующими объектами: запрос 'sc'.</li>
     * </ol>
     */
    @Test
    public void testNotDeletingRsWhichIsValueOfAttribute()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();

        CatalogItem rulSet = DAOCatalogItem.createRulesSettings(scClass, SysAttribute.impact(scClass).getCode(),
                SysAttribute.urgency(scClass).getCode());
        DSLCatalogItem.add(rulSet);

        Attribute catalogItemAttr = DAOAttribute.createCatalogItem(scClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.RULESSETTINGS), null);
        DSLAttribute.add(catalogItemAttr);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);
        catalogItemAttr.setValue(rulSet.getUuid());
        DSLBo.editAttributeValue(sc, catalogItemAttr);

        String catalogTitle = SystemCatalog.RULESSETTINGS.getTitle();
        String errorMessage = String.format(
                ErrorMessages.NOT_DEL_CATALOG_ITEM + "\n" + ErrorMessages.NO_FORMATTED_FIRST
                        + ErrorMessages.RELATED_OBJECT,
                catalogTitle, rulSet.getTitle(), catalogTitle, "запрос", sc.getTitle());

        // Действия и проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(rulSet);
        GUICatalogItem.tryToDeleteAndCheckErrorMessage(rulSet, errorMessage);
    }

    /**
     * Тестирование невосстановления таблицы соответствий, если значение атрибута в ней - архивный элемент справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить пользовательский справочник userCatalog, в нем элемент userCatalogItem</li>
     * <li>В справочник "Уровни влияния" добавить элемент impact</li>
     * <li>В класс Запрос добавить атрибут catalogItemAttr (типа "Элемент справочника", справочник userCatalog)</li>
     * <li>Создать таблицу соответствий rulSet (Объекты: Запрос, Определяемые: Влияние, Определяющие: catalogItemAttr)</li>
     * <li>Добавить в rulSet строку: [impact : userCatalogItem]</li>
     * <li>Поместить rulSet в архив</li>
     * <li>Поместить userCatalogItem в архив</li>
     * <br>
     * <b>Действия</b>
     * <li>Восстановить из архива rulSet, подтвердить</li>
     * <br>
     * <b>Проверка</b>
     * <li>Ошибка: Элемент справочника 'Таблицы соответствий' 'rulSet' не может быть восстановлен из архива 
     * по следующим причинам:
     * 1. элемент справочника 'Таблицы соответствий' связан со следующими архивными объектами: элемент справочника 
     * 'userCatalog' 'userCatalogItem'</li>
     * </ol>
     */
    @Test
    public void testUnrestoreRsIfAttrValueIsArchivedCatalogItem()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();

        Catalog userCatalog = DAOCatalog.createUser(false, false);
        Attribute impactAttr = SysAttribute.impact(scClass);
        Attribute catalogItemAttr = DAOAttribute.createCatalogItem(scClass.getFqn(), userCatalog, null);
        DSLMetainfo.add(userCatalog, catalogItemAttr);

        CatalogItem userCatalogItem = DAOCatalogItem.createUser(userCatalog);
        CatalogItem impact = DAOCatalogItem.createImpact();
        CatalogItem rulSet = DAOCatalogItem.createRulesSettings(scClass, impactAttr, catalogItemAttr);
        DSLCatalogItem.add(userCatalogItem, impact, rulSet);

        RsRow rsRow = DAORsRow.create(rulSet, impactAttr, impact.getUuid(), catalogItemAttr, userCatalogItem.getUuid());
        DSLRsRows.addRowToRSItem(rsRow);

        DSLCatalogItem.archive(rulSet);
        DSLCatalogItem.archive(userCatalogItem);

        String errorMessage = String.format(ErrorMessages.HAS_ARCHIVED_CATALOG_ITEM, rulSet.getTitle(),
                userCatalog.getTitle().toLowerCase(), userCatalogItem.getTitle());

        // Действия и проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(rulSet);
        GUICatalogItem.tryToArchiveOrRestoreAndCheckErrorMessage(rulSet, errorMessage);
    }

    /**
     * Тестирование работоспособности таблицы соответствий, если атрибут типа Элемент справочника 
     * (с указанным справочником Таблицы соответствий) используется в таблице соответствий в качестве 
     * Определяющего атрибута и в правиле определения соответствий указана текущая таблица соответствий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$47637880
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе Запрос создать атрибут testAttr, тип значения: Элемент справочника, справочник: Таблицы соответствий</li>
     * <li>В системном справочнике Таблицы соответствий создать элемент test, объекты: Запрос, 
     * определяемые атрибуты: testAttr, определяющие атрибуты: Часовой пояс</li>
     * <li>Для элемента test добавить правило определения соответствий: testAttr: со значением test (созданный нами элемент ТС) 
     * и Часовой пояс: со значением [Любой]</li>
     * <br>
     * <b>Действия</b>
     * <li>Перейти на карточку элемента справочника test</li>
     * <br>
     * <b>Проверка</b>
     * <li>Открылась карточка элемента test
     * и на карточке присутствует строка в таблице соответствий с параметрами 
     * название таблицы соответствия test и часовой пояс запроса [Любой]</li>
     * </ol>
     */
    @Test
    public void testRulesSettingsIfAttrCatalogItemSetOnRulesSettingsUseAsTargetAttr()
    {
        MetaClass scClass = DAOScCase.createClass();
        Attribute timeZoneAttr = SysAttribute.timeZone(scClass);
        Catalog rulesSettings = DAOCatalog.createSystem(SystemCatalog.RULESSETTINGS);

        Attribute testAttr = DAOAttribute.createCatalogItemSet(scClass.getFqn(), rulesSettings);
        DSLAttribute.add(testAttr);

        //Создаем таблицу соответствий
        CatalogItem test = DAOCatalogItem.createRulesSettings(scClass, testAttr.getCode(), timeZoneAttr.getCode());
        DSLCatalogItem.add(test);

        //Добавляем строки в таблицу соответствий
        String testValue1 = AttributeUtils.prepareObjectsForRuleSettings(test.getUuid());
        RsRow row1 = DAORsRow.create(test, testAttr, testValue1, timeZoneAttr, "*");
        DSLRsRows.addRowToRSItem(row1);

        //Проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCard(test);
        GUIRulesSettings.assertRowInRSItem(testAttr.getCode(), test.getTitle(), timeZoneAttr.getCode(),
                "[Любое значение]");
    }
}
