package ru.naumen.selenium.cases.admin.system;

import static ru.naumen.selenium.casesutil.metaclass.GUIEventAction.ACTION_VALUE;

import java.io.File;
import java.nio.file.Paths;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLBreadCrumb;
import ru.naumen.selenium.casesutil.admin.DSLLeftMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.admin.DSLQuickAccessTile;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.admin.GUIMenuItem;
import ru.naumen.selenium.casesutil.admin.GUINavSettings;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.admin.Crumb;
import ru.naumen.selenium.casesutil.model.admin.DAOBreadCrumb;
import ru.naumen.selenium.casesutil.model.admin.DAOLeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOQuickAccessTile;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.admin.QuickAccessTile;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.TxType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoExportModel;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тестирование настроек навигации
 * <AUTHOR>
 * @since 22.08.2022
 */
public class NavigationSettings4Test extends AbstractTestCase
{
    /**
     * Тестирование загрузки частично выгруженной метаинформации, содержащей настройки навигации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104567815
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В настройках Навигации добавить элемент верхнего меню:
     * <pre>
     *      Название - Кнопка
     *      Вид элемента - Кнопка добавления объектов
     *      Содержимое - Запрос
     * </pre>
     * </li>
     * <li>Выгрузить частичную метаинформацию, содержащую настройки Навигации
     * (Все настройки - Настройка системы - Интерфейс и навигация - Навигация)</li>
     * <li>В настройках Навигации добавить элемент хлебных крошек:
     * <pre>
     *      объект - Отдел
     *      атрибуты связи - Родитель
     * </pre>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под naumen в ИА</li>
     * <li>Залить на стенд метаинформацию, выгруженную в п2. подготовки
     * <li>В настройках Навигации имеется элемент хлебных крошек для класса Отдел
     * </ol>
     */
    @Test
    public void testImportPartMetainfoWithNavSettings()
    {
        // Подготовка
        MetaClass ouCl = DAOOuCase.createClass();
        MetaClass scClass = DAOScCase.createClass();
        MenuItem button = DAOMenuItem.createAddButton(true, scClass);
        DSLMenuItem.add(button);

        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectNavigation(exportModel);
        File metaInfoFile = DSLMetainfoTransfer.exportMetainfo(exportModel);

        Crumb crumb = DAOBreadCrumb.create(ouCl, SysAttribute.parent(ouCl));
        DSLBreadCrumb.add(crumb);

        // Действия и проверки
        GUILogon.asNaumen();
        DSLMetainfoTransfer.importMetainfo(metaInfoFile);

        GUINavSettings.goToCard();
        GUINavSettings.assertCrumbTitleInList(crumb);
    }

    /**
     * Тестирование загрузки частично выгруженной метаинформации, содержащей только настройки верхнего меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104567815
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В настройках Навигации добавить элемент верхнего меню:
     * <pre>
     *      Название - Кнопка
     *      Вид элемента - Кнопка добавления объектов
     *      Содержимое - Запрос
     * </pre>
     * </li>
     * <li>Выгрузить частичную метаинформацию, содержащую только настройки Верхнего меню
     * (Все настройки - Настройка системы - Интерфейс и навигация - Навигация - Верхнее меню)</li>
     * <li>В настройках Навигации в блоке Видимость включить пункты:
     * <pre>
     *      Показывать верхнее меню
     *      Показывать "хлебные крошки"
     *      остальные пункты также остаются включены.
     * </pre>
     * </li>
     * <li>Добавить элемент в Левое меню:
     * <pre>
     *      название - Раздел
     *      вид элемента - Раздел меню
     *      сокращение - РЛ
     *      остальные пункты по умолчанию
     * </pre>
     * </li>
     * <li>Добавить элемент хлебных крошек:
     * <pre>
     *      объект - Отдел
     *      атрибуты связи - Родитель
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под naumen в ИА</li>
     * <li>Залить на стенд метаинформацию, выгруженную в п2. подготовки
     * <li>Проверить, что В настройках Навигации в блоке Видимость включены пункты: Показывать верхнее меню,
     * Показывать "хлебные крошки"
     * <li>В настройках Навигации имеется элемент левого меню Раздел</li>
     * <li>В настройках Навигации имеется элемент хлебных крошек для класса Отдел</li>
     * </ol>
     */
    @Test
    public void testImportPartMetainfoWithTopMenuSettings()
    {
        // Подготовка
        MetaClass ouCl = DAOOuCase.createClass();
        MetaClass scClass = DAOScCase.createClass();
        MenuItem button = DAOMenuItem.createAddButton(true, scClass);
        DSLMenuItem.add(button);

        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectTopMenu(exportModel);
        File metaInfoFile = DSLMetainfoTransfer.exportMetainfo(exportModel);

        DSLNavSettings.editVisibilitySettings(true, true, true);
        Cleaner.afterTest(true, () ->
        {
            DSLNavSettings.resetVisibilitySettings();
        });

        LeftMenuItem menuChapter= DAOLeftMenuItem.createChapter(true, null);
        DSLLeftMenuItem.add(menuChapter);

        Crumb crumb = DAOBreadCrumb.create(ouCl, SysAttribute.parent(ouCl));
        DSLBreadCrumb.add(crumb);

        // Действия и проверки
        GUILogon.asNaumen();
        DSLMetainfoTransfer.importMetainfo(metaInfoFile);

        GUINavSettings.goToCard();
        GUINavSettings.assertNavSettingsInfoBlock(true, true);
        GUINavSettings.assertBreadCrumbsCheck(true);
        GUINavSettings.assertLeftMenuItemPresent(menuChapter);
        GUINavSettings.assertCrumbTitleInList(crumb);
    }

    /**
     * Тестирование загрузки частично выгруженной метаинформации, содержащей только настройки левого меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104567815
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В настройках Навигации добавить элемент левого меню:
     * <pre>
     *      Название - Раздел
     *      Вид элемента - Раздел меню
     *      Сокращение - РЛ
     * </pre>
     * </li>
     * <li>Выгрузить частичную метаинформацию, содержащую только настройки Левого меню
     * (Все настройки - Настройка системы - Интерфейс и навигация - Навигация - Настройки левого меню)</li>
     * <li>В настройках Навигации в блоке Видимость включить пункты:
     * <pre>
     *      Показывать верхнее меню
     *      Показывать "хлебные крошки"
     *      остальные пункты также остаются включены.
     * </pre>
     * </li>
     * <li>Добавить элемент в Верхнее меню:
     * <pre>
     *      название - Кнопка
     *      вид элемента - Кнопка добавления объектов
     *      содержимое - запрос
     * </pre>
     * <li>Добавить элемент хлебных крошек:
     * <pre>
     *      объект - Отдел
     *      атрибуты связи - Родитель
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под naumen в ИА</li>
     * <li>Залить на стенд метаинформацию, выгруженную в п2. подготовки
     * <li>В настройках Навигации в блоке Видимость включены все пункты
     * <li>В настройках Навигации имеется элемент верхнего меню Кнопка</li>
     * <li>В настройках Навигации имеется элемент хлебных крошек для класса Отдел</li>
     * </ol>
     */
    @Test
    public void testImportPartMetainfoWithLeftMenuSettings()
    {
        // Подготовка
        MetaClass ouCl = DAOOuCase.createClass();
        MetaClass scClass = DAOScCase.createClass();
        LeftMenuItem menuChapter= DAOLeftMenuItem.createChapter(true, null);
        DSLLeftMenuItem.add(menuChapter);

        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectLeftMenuSettings(exportModel);
        File metaInfoFile = DSLMetainfoTransfer.exportMetainfo(exportModel);

        DSLNavSettings.editVisibilitySettings(true, true, true);
        Cleaner.afterTest(true, () ->
        {
            DSLNavSettings.resetVisibilitySettings();
        });

        MenuItem button = DAOMenuItem.createAddButton(true, scClass);
        DSLMenuItem.add(button);

        Crumb crumb = DAOBreadCrumb.create(ouCl, SysAttribute.parent(ouCl));
        DSLBreadCrumb.add(crumb);

        // Действия и проверки
        GUILogon.asNaumen();
        DSLMetainfoTransfer.importMetainfo(metaInfoFile);

        GUINavSettings.goToCard();
        GUINavSettings.assertNavSettingsInfoBlock(true, true);
        GUINavSettings.assertBreadCrumbsCheck(true);
        GUINavSettings.assertMenuItemPresent(button);
        GUINavSettings.assertCrumbTitleInList(crumb);
    }

    /**
     * Тестирование работоспособности ссылки на список объектов в левом меню, если этот пункт меню был загружен с
     * метаинформацией
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00237
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$219109961
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выгрузить частичную метаинформацию с настройками левого меню до выполнения теста, с целью их загрузки после
     * завершения теста и сохранения тестовой системы в исходном состоянии</li>
     * <li>Загрузить частичную метаинформацию, содержащую настройки левого меню с одним пунктом меню listLink -
     * ссылка на список запросов</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Раскрыть левое меню и кликнуть на пункте меню listLink</li>
     * <br>
     * <b>Проверки</b>
     * <li>Ошибки отсутствуют</li>
     * <li>На странице отобразился список</li>
     * </ol>
     */
    @Test
    public void testImportMetainfoLeftMenuLinkToList()
    {
        // Подготовка
        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectLeftMenuSettings(exportModel);
        File metaInfoFile = DSLMetainfoTransfer.exportMetainfo(exportModel);
        Cleaner.afterTest(true, () -> DSLMetainfoTransfer.importMetainfo(metaInfoFile));

        // Выполнение действий
        File file = Paths.get(DSLAdmin.FOLDER_METAINFO_PATH, DSLAdmin.METAINFI_FOR_LEFT_MENU_TEST).toFile();
        DSLMetainfoTransfer.importMetainfo(file);

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.clickLeftMenuItem("listLink");

        // Проверки
        GUIError.assertErrorAbsence();
        GUIAdvListUtil advlist = GUIAdvListUtil.forStandaloneList(tester.getWebDriver().getCurrentUrl());
        advlist.asserts().presence();
    }

    /**
     * Тестирование загрузки частично выгруженной метаинформации, содержащей только настройки видимости элементов навигации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104567815
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В настройках Навигации в блоке Видимость включить пункты:
     * <pre>
     *      Показывать верхнее меню
     *      Показывать "хлебные крошки"
     *      Остальные пункты также остаются включены.
     * </pre>
     * </li>
     * <li>Выгрузить частичную метаинформацию, содержащую только настройки Видимости блоков навигации
     * (Все настройки - Настройка системы - Интерфейс и навигация - Навигация - Видимость)</li>
     * <li>Добавить элемент в Верхнее меню:
     * <pre>
     *      название - Кнопка
     *      вид элемента - Кнопка добавления объектов
     *      содержимое - запрос
     * </pre>
     * </li>
     * <li>Добавить элемент в Левое меню:
     * <pre>
     *      название - Раздел
     *      вид элемента - Раздел меню
     *      сокращение - РЛ
     *      Остальные пункты по умолчанию
     * </pre>
     * </li>
     * <li>Добавить плитку на Панель быстрого доступа левого меню:
     * <pre>
     *      элемент левого меню - Раздел
     *      сокращение - РЛ
     * </pre>
     * </li>
     * <li>Добавить элемент хлебных крошек:
     * <pre>
     *      объект - Отдел
     *      атрибуты связи - Родитель
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под naumen в ИА</li>
     * <li>Залить на стенд метаинформацию, выгруженную в п2. подготовки
     * <li>В настройках Навигации имеется элемент верхнего меню Кнопка</li>
     * <li>В настройках Навигации имеется элемент левого меню Раздел</li>
     * <li>В настройках Навигации в Панели быстрого доступа имеется плитка для элемента Раздел</li>
     * <li>В настройках Навигации имеется элемент хлебных крошек для класса Отдел</li>
     * </ol>
     */
    @Test
    public void testImportPartMetainfoWithVisibilityNavSettings()
    {
        MetaClass ouCl = DAOOuCase.createClass();
        MetaClass scClass = DAOScCase.createClass();

        DSLNavSettings.editVisibilitySettings(true, true, true);
        Cleaner.afterTest(true, () ->
        {
            DSLNavSettings.resetVisibilitySettings();
        });

        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectVisibilitySettings(exportModel);
        File metaInfoFile = DSLMetainfoTransfer.exportMetainfo(exportModel);

        MenuItem button = DAOMenuItem.createAddButton(true, scClass);
        DSLMenuItem.add(button);

        LeftMenuItem menuChapter= DAOLeftMenuItem.createChapter(true, null);
        DSLLeftMenuItem.add(menuChapter);

        QuickAccessTile tile = DAOQuickAccessTile.createQuickAccessTile(menuChapter, true);
        DSLQuickAccessTile.add(tile);

        Crumb crumb = DAOBreadCrumb.create(ouCl, SysAttribute.parent(ouCl));
        DSLBreadCrumb.add(crumb);

        // Действия и проверки
        GUILogon.asNaumen();
        DSLMetainfoTransfer.importMetainfo(metaInfoFile);

        GUINavSettings.goToCard();
        GUINavSettings.assertMenuItemPresent(button);
        GUINavSettings.assertLeftMenuItemPresent(menuChapter);
        GUINavSettings.assertQuickAccessPanelTilePresent(menuChapter);
        GUINavSettings.assertCrumbTitleInList(crumb);
    }

    /**
     * Тестирование загрузки частично выгруженной метаинформации, содержащей элемент левого меню со ссылкой на
     * несуществующую вкладку карточки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104567815
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$251773489
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>На карточку Компании добавить вкладку Tab, код tab</li>
     * <li>В настройках Навигации добавить элемент левого меню tabLink:
     * <pre>
     *      название - Ссылка на вкладку
     *      вид элемента - ссылка на карточку
     *      вкладка карточки - Компания / Tab
     *      сокращение - СВ
     *      остальные пункты по умолчанию
     * </pre>
     * </li>
     * <li>Включить элемент левого меню tabLink</li>
     * <li>Выгрузить частичную метаинформацию, содержащую только настройки Левого меню
     * (Все настройки - Настройка системы - Интерфейс и навигация - Навигация - Настройки левого меню )</li>
     * <li>Удалить элемент левого меню tabLink</li>
     * <li>Удалить вкладку Tab с карточки Компании</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под naumen в ИА</li>
     * <li>Залить на стенд метаинформацию, выгруженную в п3. подготовки
     * <li>Проверить, что метаинформация загрузилась с ошибкой.</li>
     * <li>Перейти на вкладку "Навигация" в разделе "Интерфейс и навигация"</li>
     * <li>Проверить, что элемент левого меню tabLink выключен</li>
     * <li>Кликнуть на иконку включения элемента tabLink</li>
     * <li>Проверить, что появилась ошибка: "Не найдена вкладка карточки, указанная в настройках элемента. Для того,
     * чтобы элемент можно было включить измените настройки."</li>
     * </ol>
     */
    @Test
    public void testImportMetainfoWithEmptyLeftMenuElement()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();
        ContentTab tab = DAOContentTab.createTab(rootClass);
        DSLContent.addTab(tab);

        LeftMenuItem tabLink = DAOLeftMenuItem.createReference(true, null, rootClass, tab);
        tabLink.setEnabled(Boolean.TRUE.toString());
        DSLLeftMenuItem.add(tabLink);
        Cleaner.afterTest(() ->
        {
            DSLLeftMenuItem.delete(tabLink);
            DSLContent.deleteTab(tab);
        });

        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectLeftMenuSettings(exportModel);
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(exportModel);

        DSLLeftMenuItem.delete(tabLink);
        DSLContent.deleteTab(tab);

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToAdministration();
        GUIAdmin.loadMetainfoWithProblems(metainfoFile.getAbsolutePath());
        GUINavSettings.goToCard();
        GUINavSettings.assertLeftMenuItemEnabled(tabLink, false);
        GUINavSettings.clickOnLeftMenuItem(tabLink);
        GUIError.assertDialogError("Не найдена вкладка карточки, указанная в настройках элемента. Для того, чтобы элемент можно было включить измените настройки.");
    }

    /**
     * Тестирование загрузки частично выгруженной метаинформации, содержащей плитку для элемента левого меню, который
     * отсутствует на стенде
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104567815
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В настройках Навигации добавить элемент левого меню:
     * <pre>
     *      Название - Раздел
     *      Вид элемента - Раздел меню,
     *      Сокращение - РЛ
     *      остальные пункты по умолчанию
     * </pre>
     * </li>
     * <li>Для элемента Раздел добавить плитку в панели быстрого доступа левого меню:
     * <pre>
     *      элемент левого меню - Раздел,
     *      сокращение - РЛ
     *      остальные пункты по умолчанию
     * </pre>
     * </li>
     * <li>Выгрузить частичную метаинформацию, содержащую только настройки Панели быстрого доступа Левого меню
     * (Все настройки - Настройка системы - Интерфейс и навигация - Навигация - Настройки левого меню - Панель быстрого доступа)</li>
     * <li>Удалить элемент левого меню Раздел со стенда (вместе с ним удаляется плитка)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под naumen в ИА</li>
     * <li>Залить на стенд метаинформацию, выгруженную в п3. подготовки
     * <li>Проверить, что заливка не удалась, появилось сообщение об ошибке: Метаинформация не может быть загружена.
     * В панели быстрого доступа присутствуют плитки для несуществующих элементов левого меню с кодами: '#код#'</li>
     * </ol>
     */
    @Test
    public void testImportPartMetainfoWithEmptyLeftMenuTile()
    {
        // Подготовка
        GUILogon.asNaumen();
        LeftMenuItem menuChapter= DAOLeftMenuItem.createChapter(true, null);
        DSLLeftMenuItem.add(menuChapter);

        QuickAccessTile tile = DAOQuickAccessTile.createQuickAccessTile(menuChapter, true);
        DSLQuickAccessTile.add(tile);

        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectQuickAccess(exportModel);
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(exportModel);

        DSLQuickAccessTile.delete(tile);
        DSLLeftMenuItem.delete(menuChapter);

        // Действия и проверки
        GUINavigational.goToAdministration();
        GUIAdmin.uploadMetainfoFailed(metainfoFile.getAbsolutePath(),
                String.format(ErrorMessages.METAINFO_UPLOAD_LEFT_MENU_EMPTY_TILES, menuChapter.getCode()));
    }

    /**
     * Тестирование отображения в верхнем меню элемента "Пользовательская кнопка", настроенному на ПДПС, после нажатия
     * на эту кнопку
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00410
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$192040227
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Включить видимость верхнего и левого меню</li>
     * <li>Создать класс userClass и его тип userCase</li>
     * <li>Создать синхронное пользовательское ДПС event на userClass со скриптом:
     * <pre>utils.edit(subject, ['title':'%s'])</pre></li>
     * <li>Добавить элемент верхнего меню menuItem "Пользовательская кнопка" настроенный на ПДПС event и включить
     * его</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником/li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Кликнуть по элементу верхнего меню menuItem</li>
     * <li>Проверить, что кнопка menuItem по-прежнему отображается</li>
     * </ol>
     */
    @Test
    public void testVisibleCustomButtonsAfterEditObject()
    {
        //Подготовка
        DSLNavSettings.editVisibilitySettings(true, true, false);
        MetaClass userClass = SharedFixture.userClass();
        MetaClass userCase = SharedFixture.userCase();

        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo("return true");
        DSLScriptInfo.addScript(scriptInfo);
        EventAction event = DAOEventAction.createEventScript(
                EventType.userEvent, scriptInfo, true, TxType.Sync, userClass);
        DSLEventAction.add(event);

        MenuItem menuItem = DAOMenuItem.createCustomButton(true, null);
        DSLMenuItem.add(menuItem);

        GUILogon.asSuper();
        GUINavSettings.goToCard();

        GUINavSettings.clickEditMenuItem(menuItem);
        GUIMenuItem.fillElementTitleField(menuItem.getTitle());
        GUISelect.selectByTitle(ACTION_VALUE, event.getTitle());
        GUIForm.applyForm();
        GUIMenuItem.goToTopMenuItemCard(menuItem);
        GUIMenuItem.clickSwitchButton();

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIButtonBar.clickButtonByText(menuItem.getTitle());
        GUIButtonBar.assertPresentButtonByTitle(menuItem.getTitle());
    }

    /**
     * Тестирование того, что после внесения любых изменений в настройках прав доступа любого метакласса, в верхнем меню
     * отображаются только те элементы, на которые есть права у пользователя (БЕЗ необходимости перелогина
     * пользователя).
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00538
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$229341429
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и его тип userCase</li>
     * <li>Создать группу пользователей group</li>
     * <li>Создать профиль profile:
     * <pre>
     *     для лицензированных пользователей - да
     *     группы пользователей - group
     *     набор ролей - системная роль "Сотрудник"
     * </pre>
     * </li>
     * <li>Выдать все права во всех метаклассах профилю profile</li>
     * <li>Создать лицензионного сотрудника employee и добавить его в группу group</li>
     * <li>Создать элемент верхнего меню addButton типа "Кнопка добавления объектов" на класс userClass. Включить
     * эту кнопку</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти в ИО под сотрудником employee</li>
     * <li>Проверить, что в верхнем меню отображается кнопка addButton</li>
     * <li>Забрать у пользователя employee права на добавление объектов класса userClass</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что кнопка addButton не отображается в верхнем меню</li>
     * </ol>
     */
    @Test
    public void testTopMenuUpdatesAfterChangingMetaClassRights()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);
        SecurityProfile profile = DAOSecurityProfile.create(true, group, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.grantAllPermissions(profile);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        employee.setSecurityGroupCode(Json.listToString(group.getCode()));
        DSLEmployee.addToSecurityGroup(employee);

        MenuItem addButton = DAOMenuItem.createAddButton(true, userClass);
        DSLNavSettings.editVisibilitySettings(true, false);
        DSLMenuItem.add(addButton);

        // Действия и проверки
        GUILogon.login(employee);
        GUINavSettingsOperator.assertMenuItemExists(true, addButton);
        DSLSecurityProfile.removeRights(userClass, profile, AbstractBoRights.ADD);
        getTester().refresh();

        // нужно подождать пока страница полностью загрузиться. делаем это путем ожидания загрузки кнопки
        // редактирования профиля (если не подождать пока страница прогрузиться, то тест может дать ложно
        // положительный результат)
        GUITester.assertPresent(GUIXpath.Any.EDIT_PROFILE, "Отсутствует ссылка на редактирование профиля");

        GUINavSettingsOperator.assertMenuItemExists(false, addButton);
    }
}
