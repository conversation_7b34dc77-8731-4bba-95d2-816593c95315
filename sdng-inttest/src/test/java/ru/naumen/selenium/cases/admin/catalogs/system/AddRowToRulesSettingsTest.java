package ru.naumen.selenium.cases.admin.catalogs.system;

import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUIRulesSettings;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BooleanType;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.util.DateTimeUtils;

/**
 * Тестирование скрипта добавления строки в таблицу соответствий
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
 * <AUTHOR>
 * @since 29.05.2013
 */
public class AddRowToRulesSettingsTest extends AbstractTestCase
{
    private static MetaClass scClass, teamClass, ouCase, teamCase, employeeCase;

    private static Bo ou, employee1, employee2, team1, team2;
    private static Catalog categories;
    private static CatalogItem category1, category2;

    //testAggrByBool
    private static Attribute aggrAttr, boolAttr;
    //testBoLinksByCatalogItem
    private static Attribute boLinksAttr, catItemAttr;

    //testCatalogItemsByBackLink
    private static Attribute objectLink, catItemsAttr, backLinkAttr;
    //testDoubleByObject
    private static Attribute doubleAttr, objectAttr;

    //testHyperlinkByInteger
    private static Attribute hyperlinkAttr, intAttr;

    //testMultiAttrByCatalogItem
    private static Attribute dateAttr, dateTimeAttr, stringAttr, textAttr, dtIntervalAttr, catItemAttr2;

    //testStringByResponsible
    private static Attribute stringAttr2;

    @BeforeClass
    public static void prepareFixture()
    {
        categories = DAOCatalog.createSystem(SystemCatalog.CATEGORY);

        scClass = DAOScCase.createClass();
        teamClass = DAOTeamCase.createClass();
        ouCase = DAOOuCase.create();
        teamCase = DAOTeamCase.create();
        employeeCase = DAOEmployeeCase.create();

        //testAggrByBool
        aggrAttr = DAOAttribute.createAggregate(scClass, AggregatedClasses.OU_AND_TEAM, null, null);
        boolAttr = DAOAttribute.createBool(scClass).setViewPresentation(BooleanType.VIEW_YES_NO);

        //testBoLinksByCatalogItem
        boLinksAttr = DAOAttribute.createBoLinks(scClass.getFqn(), teamClass);
        catItemAttr = DAOAttribute.createCatalogItem(scClass.getFqn(), categories, null);

        //testCatalogItemsByBackLink
        objectLink = DAOAttribute.createObjectLink(teamClass, scClass, null);
        catItemsAttr = DAOAttribute.createCatalogItemSet(scClass.getFqn(), categories);
        backLinkAttr = DAOAttribute.createBackBOLinks(scClass.getFqn(), objectLink);

        //testDoubleByObject
        doubleAttr = DAOAttribute.createDouble(scClass.getFqn());
        objectAttr = DAOAttribute.createObjectLink(scClass, teamClass, null);

        //testHyperlinkByInteger
        hyperlinkAttr = DAOAttribute.createHyperlink(scClass.getFqn());
        intAttr = DAOAttribute.createInteger(scClass.getFqn());

        //testMultiAttrByCatalogItem
        dateAttr = DAOAttribute.createDate(scClass.getFqn());
        dateTimeAttr = DAOAttribute.createDateTime(scClass.getFqn());
        stringAttr = DAOAttribute.createString(scClass.getFqn());
        textAttr = DAOAttribute.createText(scClass.getFqn());
        dtIntervalAttr = DAOAttribute.createTimeInterval(scClass.getFqn());
        catItemAttr2 = DAOAttribute.createCatalogItem(scClass.getFqn(), categories, null);

        //testStringByResponsible
        stringAttr2 = DAOAttribute.createString(scClass.getFqn());

        DSLMetainfo.add(ouCase, teamCase, employeeCase, aggrAttr, boolAttr, boLinksAttr, catItemAttr, objectLink,
                catItemsAttr, backLinkAttr, doubleAttr, objectAttr, hyperlinkAttr, intAttr, dateAttr, dateTimeAttr,
                stringAttr, textAttr, dtIntervalAttr, catItemAttr2, stringAttr2);

        category1 = DAOCatalogItem.createCategory();
        category2 = DAOCatalogItem.createCategory();
        DSLCatalogItem.add(category1, category2);

        ou = DAOOu.create(ouCase);
        team1 = DAOTeam.create(teamCase);
        team2 = DAOTeam.create(teamCase);
        DSLBo.add(ou, team1, team2);
        employee1 = DAOEmployee.create(employeeCase, ou, true);
        employee2 = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee1, employee2);
        DSLTeam.addEmployeesPairs(team1, employee1, team2, employee2);
    }

    /**
     * Определяемый атрибут: агрегирующий, определяющий: логический
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     */
    @Test
    public void testAggrByBool()
    {
        //Создаем таблицу соответствий
        CatalogItem rs = DAOCatalogItem.createRulesSettings(scClass, aggrAttr.getCode(), boolAttr.getCode());
        DSLCatalogItem.add(rs);

        RsRow row1 = DAORsRow.create(rs, aggrAttr, AttributeUtils.prepareAggregateForRuleSettings(team1, employee1),
                boolAttr, Boolean.TRUE.toString());

        RsRow row2 = DAORsRow.create(rs, aggrAttr, AttributeUtils.prepareAggregateForRuleSettings(ou, employee1),
                boolAttr, Boolean.FALSE.toString());
        DSLRsRows.addRowToRSItem(row1, row2);
        String id1 = row1.getId();
        String id2 = row2.getId();

        //Проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCard(rs);
        GUIRulesSettings.assertRowIdInRSItem(id1, aggrAttr.getCode(), team1.getTitle(), aggrAttr.getCode(),
                employee1.getTitle(), boolAttr.getCode(), "да");
        GUIRulesSettings.assertRowIdInRSItem(id2, aggrAttr.getCode(), ou.getTitle(), aggrAttr.getCode(),
                employee1.getTitle(), boolAttr.getCode(), "нет");
    }

    /**
     * Определяемый атрибут: набор ссылок на БО, определяющий: элемент справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     */
    @Test
    public void testBoLinksByCatalogItem()
    {
        //Создаем таблицу соответствий
        CatalogItem rs = DAOCatalogItem.createRulesSettings(scClass, boLinksAttr.getCode(), catItemAttr.getCode());
        DSLCatalogItem.add(rs);

        //Добавляем строки в таблицу соответствий
        String boLinksValue1 = AttributeUtils.prepareObjectsForRuleSettings(team1.getUuid());
        RsRow row1 = DAORsRow.create(rs, catItemAttr, category1.getUuid(), boLinksAttr, boLinksValue1);
        DSLRsRows.addRowToRSItem(row1);

        String boLinksValue2 = AttributeUtils.prepareObjectsForRuleSettings(team2.getUuid());
        RsRow row2 = DAORsRow.create(rs, catItemAttr, category2.getUuid(), boLinksAttr, boLinksValue2);
        DSLRsRows.addRowToRSItem(row2);

        //Проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCard(rs);
        GUIRulesSettings.assertRowInRSItem(boLinksAttr.getCode(), team1.getTitle(), catItemAttr.getCode(),
                category1.getTitle());
        GUIRulesSettings.assertRowInRSItem(boLinksAttr.getCode(), team2.getTitle(), catItemAttr.getCode(),
                category2.getTitle());
    }

    /**
     * Определяемый атрибут: набор элементов справочника, определяющий: обратная ссылка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     */
    @Test
    public void testCatalogItemsByBackLink()
    {
        //Создаем таблицу соответствий
        CatalogItem rs = DAOCatalogItem.createRulesSettings(scClass, catItemsAttr.getCode(), backLinkAttr.getCode());
        DSLCatalogItem.add(rs);

        //Добавляем строки в таблицу соответствий
        String catItemsValue1 = AttributeUtils.prepareObjectsForRuleSettings(category1.getUuid());
        String backLinksValue1 = AttributeUtils.prepareObjectsForRuleSettings(team1.getUuid());
        RsRow row1 = DAORsRow.create(rs, catItemsAttr, catItemsValue1, backLinkAttr, backLinksValue1);
        DSLRsRows.addRowToRSItem(row1);

        String catItemsValue2 = AttributeUtils.prepareObjectsForRuleSettings(category2.getUuid());
        String backLinksValue2 = AttributeUtils.prepareObjectsForRuleSettings(team2.getUuid());
        RsRow row2 = DAORsRow.create(rs, catItemsAttr, catItemsValue2, backLinkAttr, backLinksValue2);
        DSLRsRows.addRowToRSItem(row2);

        //Проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCard(rs);
        GUIRulesSettings.assertRowInRSItem(catItemsAttr.getCode(), category1.getTitle(), backLinkAttr.getCode(),
                team1.getTitle());
        GUIRulesSettings.assertRowInRSItem(catItemsAttr.getCode(), category2.getTitle(), backLinkAttr.getCode(),
                team2.getTitle());
    }

    /**
     * Определяемый атрибут: вещественное число, определяющий: ссылка на БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     */
    @Test
    public void testDoubleByObject()
    {
        //Создаем таблицу соответствий
        CatalogItem rs = DAOCatalogItem.createRulesSettings(scClass, doubleAttr.getCode(), objectAttr.getCode());
        DSLCatalogItem.add(rs);

        //Добавляем строки в таблицу соответствий
        String double1 = "99.09";
        String double2 = "-0.01";
        RsRow row1 = DAORsRow.create(rs, doubleAttr, double1, objectAttr, team1.getUuid());
        RsRow row2 = DAORsRow.create(rs, doubleAttr, double2, objectAttr, team2.getUuid());
        DSLRsRows.addRowToRSItem(row1, row2);

        //Проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCard(rs);
        GUIRulesSettings.assertRowInRSItem(doubleAttr.getCode(), double1, objectAttr.getCode(), team1.getTitle());
        GUIRulesSettings.assertRowInRSItem(doubleAttr.getCode(), double2, objectAttr.getCode(), team2.getTitle());
    }

    /**
     * Определяемый атрибут: гиперссылка, определяющий: целое число
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     */
    @Test
    public void testHyperlinkByInteger()
    {
        //Создаем таблицу соответствий
        CatalogItem rs = DAOCatalogItem.createRulesSettings(scClass, hyperlinkAttr.getCode(), intAttr.getCode());
        DSLCatalogItem.add(rs);

        //Добавляем строки в таблицу соответствий
        String link1 = AttributeUtils.prepareHyperlinkValue("NAUMEN", "http://naumen.ru");
        String int1 = "-1";
        RsRow row1 = DAORsRow.create(rs, hyperlinkAttr, link1, intAttr, int1);
        DSLRsRows.addRowToRSItem(row1);

        String link2 = AttributeUtils.prepareHyperlinkValue("Google", "http://google.com");
        String int2 = "99999";
        RsRow row2 = DAORsRow.create(rs, hyperlinkAttr, link2, intAttr, int2);
        DSLRsRows.addRowToRSItem(row2);

        //Проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCard(rs);
        GUIRulesSettings.assertRowInRSItem(hyperlinkAttr.getCode(), "NAUMEN", intAttr.getCode(), int1);
        GUIRulesSettings.assertRowInRSItem(hyperlinkAttr.getCode(), "Google", intAttr.getCode(), int2);
    }

    /**
     * Определяемые атрибуты: дата, дата/время, строка, текст, временной интервал, определяющий: элемент справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     */
    @Test
    public void testMultiAttrByCatalogItem()
    {
        //Создаем таблицу соответствий
        List<MetaClass> metaclasses = Lists.newArrayList(scClass);
        List<String> targetAttrs = Lists.newArrayList(dateAttr.getCode(), dateTimeAttr.getCode(), stringAttr.getCode(),
                textAttr.getCode(), dtIntervalAttr.getCode());
        List<String> sourceAttrs = Lists.newArrayList(catItemAttr2.getCode());
        CatalogItem rs = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);
        DSLCatalogItem.add(rs);

        //Добавляем строки в таблицу соответствий
        String dateFormatter = "dd.MM.yyyy";
        String dateTimeFormatter = "dd.MM.yyyy HH:mm";

        String date1 = DateTimeUtils.getRandomDateTimeddMMyyyy();
        String dateTime1 = DateTimeUtils.getRandomDateTimeddMMyyyyHHmm();
        String string1 = ModelUtils.createTitle();
        String text1 = ModelUtils.createDescription();
        String dtInterval1 = "5 HOUR";
        String catItem1 = category1.getUuid();
        String scriptDate1 = AttributeUtils.prepareDateForRuleSettings(date1, dateFormatter);
        String scriptDateTime1 = AttributeUtils.prepareDateForRuleSettings(dateTime1, dateTimeFormatter);

        RsRow row1 = DAORsRow.createManyTargets(rs, catItemAttr2, catItem1, dateAttr, scriptDate1, dateTimeAttr,
                scriptDateTime1, stringAttr, string1, textAttr, text1, dtIntervalAttr, dtInterval1);

        DSLRsRows.addRowToRSItem(row1);

        String date2 = DateTimeUtils.getRandomDateTimeddMMyyyy();
        String dateTime2 = DateTimeUtils.getRandomDateTimeddMMyyyyHHmm();
        String string2 = ModelUtils.createTitle();
        String text2 = ModelUtils.createDescription();
        String dtInterval2 = "55 MINUTE";
        String catItem2 = category2.getUuid();
        String scriptDate2 = AttributeUtils.prepareDateForRuleSettings(date2, dateFormatter);
        String scriptDateTime2 = AttributeUtils.prepareDateForRuleSettings(dateTime2, dateTimeFormatter);
        RsRow row2 = DAORsRow.createManyTargets(rs, catItemAttr2, catItem2, dateAttr, scriptDate2, dateTimeAttr,
                scriptDateTime2, stringAttr, string2, textAttr, text2, dtIntervalAttr, dtInterval2);
        DSLRsRows.addRowToRSItem(row2);

        //Проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCard(rs);
        GUIRulesSettings.assertRowInRSItem(dateAttr.getCode(), date1, dateTimeAttr.getCode(), dateTime1,
                stringAttr.getCode(), string1, textAttr.getCode(), text1, dtIntervalAttr.getCode(), "5 часов",
                catItemAttr2.getCode(), category1.getTitle());
        GUIRulesSettings.assertRowInRSItem(dateAttr.getCode(), date2, dateTimeAttr.getCode(), dateTime2,
                stringAttr.getCode(), string2, textAttr.getCode(), text2, dtIntervalAttr.getCode(), "55 минут",
                catItemAttr2.getCode(), category2.getTitle());
    }

    /**
     * Определяемый атрибут: строка, определяющий: ответственный
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     */
    @Test
    public void testStringByResponsible()
    {
        //Создаем атрибуты
        Attribute responsibleAttr = SysAttribute.responsible(scClass);

        //Создаем таблицу соответствий
        CatalogItem rs = DAOCatalogItem.createRulesSettings(scClass, stringAttr2.getCode(), responsibleAttr.getCode());
        DSLCatalogItem.add(rs);

        //Добавляем строки в таблицу соответствий
        String str1 = ModelUtils.createTitle();
        RsRow row1 = DAORsRow.create(rs, stringAttr2, str1, responsibleAttr,
                AttributeUtils.prepareAggregateForResponsibleSettings(team1, employee1));
        DSLRsRows.addRowToRSItem(row1);

        String str2 = ModelUtils.createTitle();
        RsRow row2 = DAORsRow.create(rs, stringAttr2, str2, responsibleAttr,
                AttributeUtils.prepareAggregateForResponsibleSettings(team2, null));
        DSLRsRows.addRowToRSItem(row2);

        String str3 = ModelUtils.createTitle();
        RsRow row3 = DAORsRow.create(rs, stringAttr2, str3, responsibleAttr,
                AttributeUtils.prepareAggregateForResponsibleSettings(null, null));
        DSLRsRows.addRowToRSItem(row3);

        String str4 = ModelUtils.createTitle();
        RsRow row4 = DAORsRow.create(rs, stringAttr2, str4, responsibleAttr,
                AttributeUtils.prepareAggregateForResponsibleSettings(team2, employee2));
        DSLRsRows.addRowToRSItem(row4);

        //Проверки
        GUILogon.asSuper();
        GUICatalogItem.goToCard(rs);
        GUIRulesSettings.assertRowInRSItem(stringAttr2.getCode(), str1, responsibleAttr.getCode(), team1.getTitle());
        GUIRulesSettings.assertRowInRSItem(stringAttr2.getCode(), str2, responsibleAttr.getCode(), team2.getTitle());
        GUIRulesSettings.assertRowInRSItem(stringAttr2.getCode(), str3, responsibleAttr.getCode(), "\u00a0");
        GUIRulesSettings.assertRowInRSItem(stringAttr2.getCode(), str4, responsibleAttr.getCode(), employee2.getTitle());
    }
}
