package ru.naumen.selenium.cases.admin.catalogs.system;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00077
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00186
 * Тестирование элементов системного справочника Коды закрытия
 * <AUTHOR>
 * @since 11.01.2012
 *
 */

public class ClosureCodeCatalogItemTest extends AbstractTestCase
{

    /**
     * Тестирование добавления элемента справочника Коды закрытия
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00190
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника Коды закрытия</li>
     * <li>В блоке Элементы справочника нажимаем кнопку Добавить</li>
     * <li>На форме добавления атрибута заполнить поля: название, код</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Коды закрытия, блок Элементы справочника: название(ссылка на карточку элемента) и код элемента</li>
     * <li>Карточка элемента: название и код.  </li>
     * </ol>
     */
    @Test
    public void testAddSimpleClosureCodeCatalogItem()
    {
        //Выполнение действия
        GUILogon.asSuper();
        CatalogItem item = DAOCatalogItem.createClosureCode();
        GUICatalogItem.callAddForm(item);
        GUIForm.assertDialogAppear("Форма добавления элемента справочника \"Коды закрытия\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, item.getCode());
        GUIForm.applyModalForm();
        //Проверки
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование копирования элемента справочника Коды закрытия
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00143
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент Коды закрытия A.</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника Коды закрытия</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму копировать</li>
     * <li>На форме копирования элемента: вводим новое название, код соотвтествующие элементу справочника B.</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма копирования закрылась</li>
     * <li>Карточка справочника Коды закрытия, блок Элементы справочника: присутствует элемент А и элемент В. </li>
     * <li>Карточка элемента B, присутствуют название,  код</li>
     * </ol>
     */
    @Test
    public void testCopyClosureCodeCatalogItem()
    {
        //Подготовка
        CatalogItem itemA = DAOCatalogItem.createClosureCode();
        DSLCatalogItem.add(itemA);
        CatalogItem itemB = DAOCatalogItem.createClosureCode();
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(itemA);
        GUICatalog.clickPictogram(itemA, "copy");
        GUIForm.assertDialogAppear("Форма копирования элемента справочника \"Коды закрытия\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, itemB.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, itemB.getCode());
        GUIForm.applyModalForm();
        //Проверки
        GUICatalogItem.assertPresent(itemB);
    }

    /**
     * Тестирование удаления элемента справочника Коды закрытия
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00192
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника Коды закрытия. А</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку справочника Коды закрытия</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму Удалить</li>
     * <li>Подтверждаем удаление</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Коды закрытия, блок Элементы справочника: элемент А отсутствует</li>
     * </ol>
     */
    @Test
    public void testDeleteClosureCodeCatalogItem()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createClosureCode();
        DSLCatalogItem.add(item);
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(item);
        GUICatalog.clickPictogramRemoveAndConfirm(item);

        //Проверки
        GUICatalogItem.assertAbsence(item);
    }

    /**
     * Тестирование редактирования элемента справочника Коды закрытия
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00191
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника Коды закрытия А</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника Коды закрытия</li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму редактировать</li>
     * <li>На форме редактирования элемента проверяем, что код не редактируется.</li>
     * <li>На форме редактирования элемента: меняем название</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка справочника Коды закрытия: название и код</li>
     * <li>Карточка элемента: название и код.</li>
     * </ol>
     */
    @Test
    public void testEditClosureCodeCatalogItem()
    {
        //Подготовка
        CatalogItem item = DAOCatalogItem.createClosureCode();
        DSLCatalogItem.add(item);
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(item);
        item.setTitle(ModelUtils.createTitle());
        GUICatalog.clickPictogram(item, "edit");
        GUIForm.assertDialogAppear("Форма редактирования элемента справочника \"Коды закрытия\" не появилась.");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, item.getTitle());
        String actual = tester.getText(GUIXpath.PropertyDialogBoxContent.PROPERTY_DIALOG_BOX_CODE_CAPTION + "/span").trim();
        String message = String
                .format("Код элемента справочника \"Коды закрытия\" на форме редактирования не совпал с ожидаемым. Ожидаемый код:%s; полученный код:%s",
                        item.getCode(), actual);
        Assert.assertEquals(message, item.getCode(), actual);
        GUIForm.applyModalForm();
        //Проверки
        GUICatalogItem.assertPresent(item);
    }

    /**
     * Тестирование попытки копирования элемента справочника 
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00077
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать элемент справочника A.</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку  справочника </li>
     * <li>В блоке Элементы справочника напротив элемента A нажимаем пиктограмму копировать</li>
     * <li>На форме копирования элемента: вводим новое название, код соотвтествующие элементу справочника А</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появилось сообщение о невозможности копирования. Форма не закрылась</li>
     * </ol>
     */
    @Test
    public void testTryCopyCatalogItem()
    {
        //Подготовка
        CatalogItem itemA = DAOCatalogItem.createClosureCode();
        DSLCatalogItem.add(itemA);
        CatalogItem itemB = DAOCatalogItem.createClosureCode();
        itemB.setCode(itemA.getCode());
        //Тестовые действия
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(itemA);
        GUICatalog.clickPictogram(itemA, "copy");
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, itemB.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, itemB.getCode());
        String message = String.format(ErrorMessages.DEL_CL_CODE, itemB.getCode());
        GUIForm.applyFormAssertError(message);
        GUIForm.cancelForm();
    }
}
