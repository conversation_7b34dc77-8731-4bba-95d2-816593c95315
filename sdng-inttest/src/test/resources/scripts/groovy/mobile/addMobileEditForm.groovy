[BEGIN IMPORT]
com.google.common.collect.Lists;
ru.naumen.mobile.metainfoadmin.shared.editforms.AddMobileEditFormAction;
ru.naumen.metainfo.shared.mobile.editforms.EditForm;
ru.naumen.metainfo.shared.ClassFqn;
ru.naumen.metainfo.shared.mobile.MobileAttribute;
ru.naumen.commons.shared.utils.CollectionUtils;
[END IMPORT]
[BEGIN DECLARATION]
addMobileEditForm('%s')
[END DECLARATION]
[BEGIN BODY]

/**
 * Добавить мобильную форму редактирования объекта
 * @param dataForScript данные для скрипта
 * @return uuid списка
 */
def addMobileEditForm(def dataForScript)
{
    Map data = GSON.SELF.fromJson(dataForScript, GSON.MAP_TYPE);
    
    EditForm editForm = new EditForm();
    def cases = prepareCases(data.cases);
    if (CollectionUtils.isEmpty(cases))
    {
        editForm.setClazz(ClassFqn.parse(data.fqnOfClass));
    }
    editForm.setCases(cases);
    
    if (data.profiles)
        editForm.setProfiles(Lists.newArrayList(data.profiles));
    
    if (data.tags)
        editForm.setTags(Lists.newArrayList(data.tags));

    if(data.mobileAttributes)
    {
        def mobileAttributes = data.mobileAttributes.split(':');
        mobileAttributes.each 
        {
            if(it != "")
            {
                def mobileAttribute = new MobileAttribute();
                mobileAttribute.setCode(it);
                editForm.addAttribute(mobileAttribute);
            }
        }
    }

    dispatch.execute(new AddMobileEditFormAction(editForm));
    return editForm.getUuid();
}

def prepareCases(def value)
{
    def resultList = [];

    if (!value)
        return resultList;

    value.each {
        resultList.add(ClassFqn.parse(it));
    }
    return resultList;
}

[END BODY]