/*!
 * froala_editor v4.1.4 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2023 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(r){"use strict";(r=r&&r.hasOwnProperty("default")?r["default"]:r).URLRegEx="(^| |\\u00A0)(".concat(r.LinkRegEx,"|([a-z0-9+-_.]{1,}@[a-z0-9+-_.]{1,}\\.[a-z0-9+-_]{1,}))$"),r.PLUGINS.url=function(u){var c=u.$,o=null;function t(e,t,n){for(var r="";n.length&&"."==n[n.length-1];)r+=".",n=n.substring(0,n.length-1);var a=n;if(u.opts.linkConvertEmailAddress)u.helpers.isEmail(a)&&!/^mailto:.*/i.test(a)&&(a="mailto:".concat(a));else if(u.helpers.isEmail(a))return t+n;return/^((http|https|ftp|ftps|mailto|tel|sms|notes|data)\:)/i.test(a)||(a="//".concat(a)),(t||"")+"<a".concat(u.opts.linkAlwaysBlank?' target="_blank"':"").concat(o?' rel="'.concat(o,'"'):"",' data-fr-linked="true" href="').concat(a,'">').concat(n.replace(/&amp;/g,"&").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),"</a>").concat(r)}var d=function d(){return new RegExp(r.URLRegEx,"gi")};function g(e){return u.opts.linkAlwaysNoFollow&&(o="nofollow"),u.opts.linkAlwaysBlank&&(u.opts.linkNoOpener&&(o?o+=" noopener":o="noopener"),u.opts.linkNoReferrer&&(o?o+=" noreferrer":o="noreferrer")),e.replace(d(),t)}function h(e){var t=e.split(" ");return t[t.length-1]}function n(){var e=u.selection.ranges(0),t=e.startContainer;if(t.nodeType!==Node.TEXT_NODE)return!1;if(!t||e.startOffset!==(t.textContent||"").length){u.markers.insert();var n=u.$el.find(".fr-marker"),r=t.textContent.match(/(^| |\u00A0)(((([a-z\u0080-\u009f\u00a1-\uffff0-9-_\.]{1,})(\.(com|net|org|edu|mil|gov|co|biz|info|me|dev)))|((ftp|http|https):\/\/[a-z\u0080-\u009f\u00a1-\uffff0-9-_\.]{1,})|(www\.[a-z\u0080-\u009f\u00a1-\uffff0-9-_\.]{1,}\.[a-z0-9-]{2,24})|((ftp|http|https):\/\/[\u0021-\uffff]{1,}@[a-z\u0080-\u009f\u00a1-\uffff0-9-_\.]{1,}))((:[0-9]{1,5})|)(((\/|\?|#)[a-z\u00a1-\uffff0-9@?\|!^=%&amp;\/~+#-\'*-_{}]*)|())|([a-z0-9+-_.]{1,}@[a-z0-9+-_.]{1,}\.[a-z0-9+-_]{1,}))$/gi);if(n.remove(),!r)return!1}if(function p(e){return!!e&&("A"===e.tagName||!(!e.parentNode||e.parentNode==u.el)&&p(e.parentNode))}(t))return!1;if(d().test(h(t.textContent))){var a,o=t.textContent.length;if(0<o&&","===t.textContent.charAt(o-1)){c(t).before(g(t.textContent.substring(0,o-1))),(a=c(t.parentNode).find("a[data-fr-linked]")).removeAttr("data-fr-linked"),t.textContent=",";var i=u.win.getSelection().getRangeAt(0);i.setStart(t,1),i.setEnd(t,1)}else c(t).before(g(t.textContent)),(a=c(t.parentNode).find("a[data-fr-linked]")).removeAttr("data-fr-linked"),t.parentNode.removeChild(t);u.events.trigger("url.linked",[a.get(0)])}else if(t.textContent.split(" ").length<=2&&t.previousSibling&&"A"===t.previousSibling.tagName){var l=t.previousSibling.innerText+t.textContent,f=t.textContent.length;if(d().test(h(l))&&","!==t.textContent.charAt(f-1)){var s=(new DOMParser).parseFromString(g(l),"text/html").body.childNodes;t.parentNode.replaceChild(s[0],t.previousSibling),s.length&&c(t).before(s[0]),t.parentNode.removeChild(t)}}}return{_init:function e(){u.events.on("keypress",function(e){!u.selection.isCollapsed()||")"!=e.key&&"("!=e.key||n()},!0),u.events.on("keydown",function(e){var t=e.which;!u.selection.isCollapsed()||t!=r.KEYCODE.ENTER&&t!=r.KEYCODE.SPACE||n()},!0),u.events.on("paste.beforeCleanup",function(e){if(u.helpers.isURL(e)&&!u.$el[0].getAttribute("plainpaste")){var t=null;return u.opts.linkAlwaysBlank&&(u.opts.linkNoOpener&&(t?t+=" noopener":t="noopener"),u.opts.linkNoReferrer&&(t?t+=" noreferrer":t="noreferrer")),"<a".concat(u.opts.linkAlwaysBlank?' target="_blank"':"").concat(t?' rel="'.concat(t,'"'):"",' href="').concat(e,'" >').concat(e,"</a>")}if(u.$el[0].getAttribute("plainpaste"))return function n(e){if("string"==typeof e)return(e=e.replace(/&amp;/g,"&")).replace(/(?:(?:https?|ftp|file):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/gim,g).replace(/([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9_-]+)/gim,g)}(e)})}}}});