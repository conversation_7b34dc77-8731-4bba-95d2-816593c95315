import {
	UIGridArea,
	UIGridAreaOverflowYEnum,
	UIGridAreaPositionEnum,
	UILayoutGrid,
	UILayoutSubTypeEnum,
} from '@ui/rest-api';

const subType = UILayoutSubTypeEnum.UI_LAYOUT_GRID;

const headerArea: UIGridArea = {
	contentId: 'headerId',
	gridColumnEnd: 3,
	gridColumnStart: 1,
	gridRowEnd: 2,
	gridRowStart: 1,
	height: '44px',
	position: UIGridAreaPositionEnum.STICKY,
	zIndex: 1,
};

const createMainAreaMock = (contentId: string) => ({
	contentId,
	gridColumnEnd: 3,
	gridColumnStart: 2,
	gridRowEnd: 3,
	gridRowStart: 2,
	height: 'calc(100vh - 44px)',
	overflowY: UIGridAreaOverflowYEnum.AUTO,
});

export const sidebarArea: UIGridArea = {
	contentId: 'sideBarId',
	gridColumnEnd: 2,
	gridColumnStart: 1,
	gridRowEnd: 3,
	gridRowStart: 2,
	height: 'calc(100vh - 44px)',
};

export const rootLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				headerArea,
				sidebarArea,
				createMainAreaMock('objectCardId'),
			],
			gridTemplateColumns: '304px 1fr', // новое свойство
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const mainLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'commentList01IdMock',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 3,
					gridRowStart: 2,
				},
				{
					contentId: 'tabBarIdMock',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: '1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const tab1LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'informerIdMock',
					gridColumnEnd: 3,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
				{
					contentId: 'nestedTabContainerId',
					gridColumnEnd: 3,
					gridColumnStart: 1,
					gridRowEnd: 3,
					gridRowStart: 2,
				},
				{
					contentId: 'objectPropsIdMock13',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 4,
					gridRowStart: 3,
				},
			],
			gridTemplateColumns: '1fr 1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const nestedTabContainerLayout: UILayoutGrid = {
	grids: [
		{
			areas: [],
			gridTemplateColumns: '1fr',
			minWidth: 0,
		},
	],
	subType,
};

export const tab2LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'objectPropsIdMock12',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: '1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const nestedTab1LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'objectListMock',
					gridColumnEnd: 3,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
				{
					contentId: 'uiFileListMock',
					gridColumnEnd: 3,
					gridColumnStart: 1,
					gridRowEnd: 3,
					gridRowStart: 2,
				},
				{
					contentId: 'objectPropertiesIdMock21',
					gridColumnEnd: 3,
					gridColumnStart: 2,
					gridRowEnd: 4,
					gridRowStart: 3,
				},
				{
					contentId: 'objectPropertiesIdMock22',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 4,
					gridRowStart: 3,
				},
			],
			gridTemplateColumns: '4fr 6fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const nestedTab2LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'objectPropertiesId21',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
				{
					contentId: 'objectPropertiesId22',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 3,
					gridRowStart: 2,
				},
			],
			gridTemplateColumns: '1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const nestedTab3LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'informerIdMock',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
				{
					contentId: 'objectPropertiesId22',
					gridColumnEnd: 3,
					gridColumnStart: 2,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: '4fr 6fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const searchResultsPageLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				headerArea,
				sidebarArea,
				createMainAreaMock('searchResultsContentContainerId'),
			],
			gridTemplateColumns: '304px 1fr', // новое свойство
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const searchResultsContentLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'searchResultsHeaderIdMock',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
				{
					contentId: 'searchResultsContentId',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 3,
					gridRowStart: 2,
				},
			],
			gridTemplateColumns: '1fr',
			minWidth: 0,
		},
	],
	subType,
};

export const searchByClassResultsPageLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'headerId',
					gridColumnEnd: 3,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
					// новые свойства
					height: '44px',
					position: UIGridAreaPositionEnum.STICKY,
					zIndex: 1,
				},
				{
					contentId: 'searchByClassResultsContentContainerId',
					gridColumnEnd: 3,
					gridColumnStart: 2,
					gridRowEnd: 3,
					gridRowStart: 2,
					// новые свойства
					height: 'calc(100vh - 44px)',
					overflowY: UIGridAreaOverflowYEnum.AUTO,
				},
				{
					contentId: 'sideBarId',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 3,
					gridRowStart: 2,
					// новые свойства
					height: 'calc(100vh - 44px)',
				},
			],
			gridTemplateColumns: '304px 1fr', // новое свойство
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const searchByClassResultsContentLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'searchByClassResultsHeaderIdMock',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
				{
					contentId: 'searchByClassResultsContentId',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 3,
					gridRowStart: 2,
				},
			],
			gridTemplateColumns: '1fr',
			minWidth: 0,
		},
	],
	subType,
};

export const formPageLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				headerArea,
				sidebarArea,
				createMainAreaMock('formIdMock'),
			],
			gridTemplateColumns: '304px 1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};
