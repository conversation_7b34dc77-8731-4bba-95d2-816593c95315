package ru.naumen.omnichannel.server.connectionSettings.dispatch.omnigate;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import ru.naumen.core.server.TransactionHelper;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.omnichannel.server.OmnichannelConfigurationService;
import ru.naumen.omnichannel.server.connectionSettings.ConnectionSettingsService;
import ru.naumen.omnichannel.server.connectionSettings.log.omnigate.OmniGateConnectionSettingsLogService;
import ru.naumen.omnichannel.shared.connectionSettings.OmniGateConnectionSettings;
import ru.naumen.omnichannel.shared.connectionSettings.dispatch.omnigate.GetOmniGateConnectionSettingsAction;
import ru.naumen.omnichannel.shared.connectionSettings.dispatch.omnigate.SaveOmniGateConnectionSettingsAction;

/**
 * Обработчик события {@link SaveOmniGateConnectionSettingsAction} сохранения настроек подключения к шлюзу "OmniGate"
 * <AUTHOR>
 * @since 06.10.2023
 */
@Component
public class SaveOmniGateConnectionSettingsActionHandler
        extends TransactionalActionHandler<SaveOmniGateConnectionSettingsAction,
        SimpleResult<OmniGateConnectionSettings>>
{
    private final ConnectionSettingsService settingsService;
    private final OmniGateConnectionSettingsLogService logService;
    private final Dispatch dispatch;
    private final OmnichannelConfigurationService configurationService;

    @Inject
    public SaveOmniGateConnectionSettingsActionHandler(ConnectionSettingsService settingsService,
            OmniGateConnectionSettingsLogService logService,
            @Lazy Dispatch dispatch,
            OmnichannelConfigurationService configurationService)
    {
        super();
        this.settingsService = settingsService;
        this.logService = logService;
        this.dispatch = dispatch;
        this.configurationService = configurationService;
    }

    @Override
    public SimpleResult<OmniGateConnectionSettings> executeInTransaction(SaveOmniGateConnectionSettingsAction action,
            ExecutionContext context)
    {
        OmniGateConnectionSettings oldSettings = settingsService.getOmniGateSettings();
        OmniGateConnectionSettings newSettings = action.getSettings();

        boolean save = settingsService.saveOmniGateSettings(newSettings);

        if (save)
        {
            logService.editOmniGateConnectionSettings(newSettings, oldSettings);
            TransactionHelper.afterSuccessCommit(
                    () -> configurationService.applyOmniGateConnectionConfig(oldSettings, newSettings));
        }

        return dispatch.executeExceptionSafe(new GetOmniGateConnectionSettingsAction());
    }
}
