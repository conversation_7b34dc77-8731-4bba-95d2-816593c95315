package ru.naumen.core.server.script.api;

import ru.naumen.core.server.keystore.IX509Certificate;

/**
 * Документированные методы API для работы с KeyStore
 * @see IDocumentedApi
 * <AUTHOR> mpleshhyov
 * @since 07.09.2022
 */
public interface IDocumentedKeyStoreApi extends IDocumentedApi
{
    /**
     * Удаление сертификата из локального KeyStore
     * @param alias - псевдоним сертификата в KeyStore
     */
    void deleteCertificate(String alias);

    /**
     * Получение сертификата из локального KeyStore
     * @param alias - псевдоним сертификата в KeyStore
     * @return найденный сертификат
     */
    IX509Certificate getCertificate(String alias);

    /**
     * Добавить сертификат в локальный KeyStore.
     * @param alias - псевдоним сертификата в KeyStore
     * @param certificatePem - строковое представление сертификата в PEM формате
     */
    void importCertificate(String alias, String certificatePem);
}
