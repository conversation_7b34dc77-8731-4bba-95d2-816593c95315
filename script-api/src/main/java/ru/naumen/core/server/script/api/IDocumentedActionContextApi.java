package ru.naumen.core.server.script.api;

/**
 * Документированные методы API для работы с контекстом выполняемых действий
 * @see IDocumentedApi
 * <AUTHOR> mpleshhyov
 * @since 07.09.2022
 */
public interface IDocumentedActionContextApi extends IDocumentedApi
{
    /**
     * Проверяет, является ли выполняющееся действие инициированным в МК
     * @return true, если действие инициализировано в МК
     */
    boolean isMobile();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме добавления (полноэкранной)
     * @return true, если действие инициализировано на форме добавления (полноэкранной)
     */
    boolean isStandaloneAddForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на карточке объекта
     * @return true, если действие инициализировано на карточке объекта
     */
    boolean isCard();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме результатов поиска
     * @return true, если действие инициализировано на форме результатов поиска
     */
    boolean isStandaloneSearch();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме редактирования (полноэкранной)
     * @return true, если действие инициализировано на форме редактирования (полноэкранной)
     */
    boolean isStandaloneEditForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме редактирования (модальной)
     * @return true, если действие инициализировано на форме редактирования (модальной)
     */
    boolean isContentEditForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме быстрого добавления
     * @return true, если действие инициализировано на форме быстрого добавления
     */
    boolean isQuickAddForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме быстрого редактирования
     * @return true, если действие инициализировано на форме быстрого редактирования
     */
    boolean isQuickEditForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме инлайн редактирования
     * @return true, если действие инициализировано на форме инлайн редактирования
     */
    boolean isListInlineEditForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на отдельной странице со списком по ссылке
     * @return true, если действие инициализировано на отдельной странице со списком по ссылке
     */
    boolean isStandaloneList();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме смены типа
     * @return true, если действие инициализировано на форме смены типа
     */
    boolean isChangeCaseForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме смены ответственного
     * @return true, если действие инициализировано на форме смены ответственного
     */
    boolean isChangeResponsibleForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме смены статуса
     * @return true, если действие инициализировано на форме смены статуса
     */
    boolean isChangeStateForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме смены привязки
     * @return true, если действие инициализировано на форме смены привязки
     */
    boolean isChangeAssociationForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме массового редактирования
     * @return true, если действие инициализировано на форме массового редактирования
     */
    boolean isMassEditForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме массовой смены статуса
     * @return true, если действие инициализировано на форме массовой смены статуса
     */
    boolean isMassChangeStateForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме добавления связи
     * @return true, если действие инициализировано на форме добавления связи
     */
    boolean isLinksAddForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме добавления комментария
     * @return true, если действие инициализировано на форме добавления комментария
     */
    boolean isCommentAddForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме редактирования комментария
     * @return true, если действие инициализировано на форме редактирования комментария
     */
    boolean isCommentEditForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме добавления файла
     * @return true, если действие инициализировано на форме добавления файла
     */
    boolean isFileAddForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме редактирования файла
     * @return true, если действие инициализировано на форме редактирования файла
     */
    boolean isFileEditForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме массовости запроса
     * @return true, если действие инициализировано на форме массовости запроса
     */
    boolean isServiceCallMassProblemForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме перемещения (смены родителя)
     * @return true, если действие инициализировано на форме перемещения (смены родителя)
     */
    boolean isChangeParentForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме добавления/удаления связи
     * @return true, если действие инициализировано на форме добавления/удаления связи
     */
    boolean isLinksEditForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным на форме ПДПС
     * @return true, если действие инициализировано на форме ПДПС
     */
    boolean isUserEventActionForm();

    /**
     * Проверяет, является ли выполняющееся действие инициированным из rest-а
     * @return true, если действие инициализировано из rest-а
     */
    boolean isCommonRest();

    /**
     * Проверяет, является ли выполняющееся действие инициированным из rest-а портала
     * @return true, если действие инициализировано из rest-а портала
     */
    boolean isPortalRest();

    /**
     * Проверяет, является ли выполняющееся действие инициированным из rest-а ВП
     * @return true, если действие инициализировано из rest-а ВП
     */
    boolean isEmbeddedApplicationRest();
}
