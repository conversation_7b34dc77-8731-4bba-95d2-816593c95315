package ru.naumen.dynamicfield.core.server.filter;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.dynamicfield.core.server.criteria.JsonFieldPath;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Фабрика отдельных условий для фильтрации и сортировки динамических полей.
 * <AUTHOR>
 * @since Apr 04, 2024
 */
public interface DynamicAttributeRestrictionFactory
{
    /**
     * Строит критерий равенства динамических полей.
     * @param column колонка с динамическими полями
     * @param attribute атрибут, представляющий динамическое поле
     * @param path путь к полю
     * @param value значение для сравнения
     * @return построенный критерий фильтрации
     */
    HCriterion eq(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object value);

    /**
     * Строит критерий неравенства динамических полей.
     * @param column колонка с динамическими полями
     * @param attribute атрибут, представляющий динамическое поле
     * @param path путь к полю
     * @param value значение для сравнения
     * @return построенный критерий фильтрации
     */
    HCriterion notEq(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object value);

    /**
     * Строит критерий «меньше» для динамических полей.
     * @param column колонка с динамическими полями
     * @param attribute атрибут, представляющий динамическое поле
     * @param path путь к полю
     * @param value значение для сравнения
     * @return построенный критерий фильтрации
     */
    HCriterion less(HColumn column, Attribute attribute, JsonFieldPath path, Object value);

    /**
     * Строит критерий «больше» для динамических полей.
     * @param column колонка с динамическими полями
     * @param attribute атрибут, представляющий динамическое поле
     * @param path путь к полю
     * @param value значение для сравнения
     * @return построенный критерий фильтрации
     */
    HCriterion grater(HColumn column, Attribute attribute, JsonFieldPath path, Object value);

    /**
     * Строит критерий «меньше или равно» для динамических полей.
     * @param column колонка с динамическими полями
     * @param attribute атрибут, представляющий динамическое поле
     * @param path путь к полю
     * @param value значение для сравнения
     * @return построенный критерий фильтрации
     */
    HCriterion lessOrEqual(HColumn column, Attribute attribute, JsonFieldPath path, Object value);

    /**
     * Строит критерий «больше или равно» для динамических полей.
     * @param column колонка с динамическими полями
     * @param attribute атрибут, представляющий динамическое поле
     * @param path путь к полю
     * @param value значение для сравнения
     * @return построенный критерий фильтрации
     */
    HCriterion greaterOrEqual(HColumn column, Attribute attribute, JsonFieldPath path, Object value);

    /**
     * Строит критерий «с ... по» для динамических полей.
     * @param column колонка с динамическими полями
     * @param attribute атрибут, представляющий динамическое поле
     * @param path путь к полю
     * @param begin начальное значение
     * @param end конечное значение
     * @return построенный критерий фильтрации
     */
    HCriterion between(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object begin,
            @Nullable Object end);

    /**
     * Строит критерий «входит в множество» для динамических полей.
     * @param column колонка с динамическими полями
     * @param attribute атрибут, представляющий динамическое поле
     * @param path путь к полю
     * @param value множество для сравнения
     * @return построенный критерий фильтрации
     */
    HCriterion in(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object value);

    /**
     * Строит критерий «содержит» для строковых динамических полей.
     * @param column колонка с динамическими полями
     * @param attribute атрибут, представляющий динамическое поле
     * @param path путь к полю
     * @param substring подстрока для поиска
     * @param ignoreCase <code>true</code>, если сравнение нужно производить без учета регистра,
     * иначе <code>false</code>
     * @return построенный критерий фильтрации
     */
    HCriterion contains(HColumn column, Attribute attribute, JsonFieldPath path, String substring, boolean ignoreCase);

    /**
     * Строит выражение для получения первого значения по определённому пути.
     * @param column колонка с динамическими полями
     * @param attribute атрибут, представляющий динамическое поле
     * @param path путь к полю
     * @param valueFunction имя функции для доступа к значению внутри JSON (зависит от типа извлекаемого значения)
     * @return столбец с выражением для извлечения значения
     */
    HColumn singleValue(HColumn column, Attribute attribute, JsonFieldPath path, String valueFunction);
}
