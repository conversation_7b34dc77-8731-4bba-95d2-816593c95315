package ru.naumen.dynamicfield.admin.client.catalog.form;

import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormConstants;

/**
 * <AUTHOR>
 * @since 11.10.2022
 */
public class DynamicFieldFormConstants extends CatalogItemFormConstants<DynamicFieldFormContext>
{
    // @formatter:off
    private static final String[] CODES = {
            CatalogItem.ITEM_TITLE,
            CatalogItem.ITEM_CODE,
            CatalogItem.ITEM_COLOR,
            CatalogItem.ITEM_ICON,
            CatalogItem.SETTINGS_SET
    };
    // @formatter:on

    @Override
    public String[] propertyCodes()
    {
        return CODES;
    }
}