package ru.naumen.ui;

import static ru.naumen.ui.migrators.page.UIPagesMigrator.PAGE_TEMPLATE_ID;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import ru.naumen.ui.UIMigratorConstants.UIContent.MenuType;
import ru.naumen.ui.migrators.UIClassesMigrator;
import ru.naumen.ui.migrators.UIHomePagesMigrator;
import ru.naumen.ui.migrators.surface.UISurfacesMigrator;
import ru.naumen.ui.migrators.datasource.UIDataSourcesMigrator;
import ru.naumen.ui.migrators.UIIconMigrator;
import ru.naumen.ui.migrators.UISearchMigrator;
import ru.naumen.ui.migrators.UIThemesMigrator;
import ru.naumen.ui.migrators.menu.UIMenuMigrator;
import ru.naumen.ui.migrators.page.UIPagesMigrator;
import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.ui.settings.UISettingsModificationService;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.core.UISettings;
import ru.naumen.ui.settings.entity.menu.UIMenuItemSettings;
import ru.naumen.ui.settings.entity.menu.UIMenuSettings;
import ru.naumen.ui.settings.entity.pages.UIErrorPageSettings;
import ru.naumen.ui.settings.entity.search.UISearchPageSettings;

/**
 * Сервис для миграции старых настроек в настройки UI интерфейса
 */
@Component
public class UIMigrationService
{
    private static final Logger LOG = LoggerFactory.getLogger(UIMigrationService.class);

    private final UIClassesMigrator classesMigrator;
    private final UIPagesMigrator pagesMigrator;
    private final UIMenuMigrator menuMigrator;
    private final UIHomePagesMigrator homePageMigrator;
    private final UISearchMigrator searchMigrator;
    private final UISettingsService settingsService;
    private final UISettingsModificationService uiSettingsModificationService;
    private final UIThemesMigrator themesMigrator;
    private final UIDataSourcesMigrator dataSourcesMigrator;
    private final UISurfacesMigrator surfacesMigrator;
    private final UIIconMigrator iconMigrator;

    public UIMigrationService(UIClassesMigrator classesMigrator,
            UIPagesMigrator pagesMigrator,
            UIMenuMigrator menuMigrator,
            UIHomePagesMigrator uiHomePagesMigrator,
            UISearchMigrator searchMigrator,
            UISettingsService settingsService,
            UISettingsModificationService settingsModificationService,
            UIThemesMigrator themesMigrator,
            UIDataSourcesMigrator dataSourcesMigrator,
            UISurfacesMigrator surfacesMigrator,
            UIIconMigrator iconMigrator)
    {
        this.classesMigrator = classesMigrator;
        this.pagesMigrator = pagesMigrator;
        this.menuMigrator = menuMigrator;
        this.homePageMigrator = uiHomePagesMigrator;
        this.searchMigrator = searchMigrator;
        this.settingsService = settingsService;
        this.uiSettingsModificationService = settingsModificationService;
        this.themesMigrator = themesMigrator;
        this.dataSourcesMigrator = dataSourcesMigrator;
        this.surfacesMigrator = surfacesMigrator;
        this.iconMigrator = iconMigrator;
    }

    /**
     * Миграция настроек приложения. Если приложение с данным идентификатором уже существует, старое приложение удалится
     * @param id идентификатор нового приложения
     * @param path URL путь к новому приложению
     */
    @Transactional(readOnly = true)
    public void migrateSettings(String id, String path)
    {
        long migrationStartTime = 0;
        if (LOG.isInfoEnabled())
        {
            LOG.info("UI settings migration started. Application: %s, applicationPath: %s".formatted(id, path));
            migrationStartTime = System.currentTimeMillis();
        }
        iconMigrator.migrateIcons();
        UIApplicationSettings migratedSettings = migrate(id, path);

        UISettings uiSettings = settingsService.getUISettings().doClone();
        Map<String, UIApplicationSettings> applicationSettings = uiSettings.getApplicationSettings();
        applicationSettings.put(id, migratedSettings);
        uiSettings.setApplicationSettings(applicationSettings);

        uiSettingsModificationService.setUISettings(uiSettings);

        if (LOG.isInfoEnabled())
        {
            long migrationEndTime = System.currentTimeMillis();
            long migrationTime = migrationEndTime - migrationStartTime;
            LOG.info(
                    ("UI settings migration finished successfully. Application: %s, applicationPath: %s. Took %s "
                            + "milliseconds").formatted(id, path, migrationTime));
        }
    }

    /**
     * Миграция настроек приложения
     * @param applicationId идентификатор нового приложения
     * @param applicationPath URL путь нового приложения
     * @return Промигрированные настройки приложения
     */
    private UIApplicationSettings migrate(String applicationId, String applicationPath)
    {
        UISearchPageSettings searchPageSettings = searchMigrator.migrateSearchPageSettings(applicationId);
        UIErrorPageSettings errorPageSettings = new UIErrorPageSettings(applicationId, PAGE_TEMPLATE_ID);
        UIApplicationSettings newApplication = new UIApplicationSettings(applicationId, applicationId, applicationPath,
                searchPageSettings, errorPageSettings);
        Map<String, UIMenuSettings> menus = menuMigrator.migrateMenus(applicationId);
        UIMenuSettings sideMenu = menus.get(MenuType.SIDE_BAR_MENU.getId());
        List<UIMenuItemSettings> sideMenuItems = sideMenu == null ? List.of() : sideMenu.getItems();
        newApplication
                .setClassSettings(classesMigrator.migrateClasses(applicationId))
                .setPageTemplates(pagesMigrator.migratePageTemplate(applicationId, sideMenuItems))
                .setPageSettings(pagesMigrator.migratePages(applicationId))
                .setMenus(menus)
                .setHomePage(homePageMigrator.migrateHomePages(applicationId))
                .setDataSources(dataSourcesMigrator.migrateDataSources(applicationId))
                .setSurfaces(surfacesMigrator.migrateSurfaces(applicationId));

        themesMigrator.migrateLogo(newApplication);

        return newApplication;
    }
}
