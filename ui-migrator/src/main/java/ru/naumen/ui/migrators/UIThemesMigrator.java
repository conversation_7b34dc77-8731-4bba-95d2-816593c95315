package ru.naumen.ui.migrators;

import static ru.naumen.ui.UIMigratorConstants.Theme.DEFAULT_LOGO_SOURCE_THEME;
import static ru.naumen.ui.UIMigratorConstants.Theme.DEFAULT_THEME_ID;

import java.io.IOException;
import java.io.InputStream;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileContentStorage;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.interfacesettings.InterfaceSettings;
import ru.naumen.core.shared.interfacesettings.ThemeLogoSettings;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.ui.settings.entity.common.UIAsset;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;

/**
 * Мигратор тем
 */
@Component
public class UIThemesMigrator
{
    private final MetainfoService metainfoService;
    private final FileContentStorage fileStorage;
    private final IPrefixObjectLoaderService objectLoader;

    public UIThemesMigrator(MetainfoService metainfoService,
            FileContentStorage fileStorage,
            IPrefixObjectLoaderService objectLoader)
    {
        this.metainfoService = metainfoService;
        this.fileStorage = fileStorage;
        this.objectLoader = objectLoader;
    }

    /**
     * Миграция логотипа из настроек старой темы
     * @param applicationSettings промигрированное приложение
     */
    public void migrateLogo(UIApplicationSettings applicationSettings)
    {
        InterfaceSettings interfaceSettings = metainfoService.getInterfaceSettings();
        if (interfaceSettings != null)
        {
            ThemeLogoSettings themeLogoSettings = interfaceSettings
                    .getThemeLogoSettings()
                    .get(DEFAULT_LOGO_SOURCE_THEME);
            if (themeLogoSettings != null && !themeLogoSettings.isLogoStandart())
            {
                String assetId = themeLogoSettings.getLogoUuid();
                applicationSettings.addLogo(DEFAULT_THEME_ID, assetId);
                File logo = objectLoader.getSafe(themeLogoSettings.getLogoUuid());
                byte[] fileContent;
                try (InputStream stream = fileStorage.getContent(logo))
                {
                    fileContent = stream == null ? new byte[0] : stream.readAllBytes();
                }
                catch (IOException e)
                {
                    throw new FxException(e);
                }
                UIAsset logoAsset = new UIAsset(assetId, null, assetId, logo.getMimeType(), fileContent);
                applicationSettings.addAsset(assetId, logoAsset);
            }
        }
    }
}
