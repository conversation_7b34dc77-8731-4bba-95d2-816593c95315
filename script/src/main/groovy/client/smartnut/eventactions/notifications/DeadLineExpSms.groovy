//Автор: <PERSON>
//Дата создания: 27.12.2012
//Назначение:
/**
 * SMS оповещение ответственного о просроченности заявки
 *
 * Шаблон: SmartNut:jhs23kds::0,:,0,SmartNut,0:${notification.scriptParams['phone']}:${notification.scriptParams['message']}
 */
//Версия: 4.0
//Категория: Кастомизация оповещения

//ПАРАМЕТРЫ------------------------------------------------------------

def ENCODING = 'UTF-8';

def MAX_LEN = 201;
def NAME_MAX_LEN = 10;

//ФУНКЦИИ--------------------------------------------------------------

def max<PERSON>en(def str, def len)
{
    return (str.length() <= len) ? str : str.substring(0, len);
}

//ОСНОВНОЙ БЛОК--------------------------------------------------------

notification.parameters.characterEncoding = ENCODING;

def description = api.string.htmlToText(subject.description);

notification.scriptParams['phone'] = (subject.responsibleEmployee?.mobilePhoneNumber) ?: '';

notification.scriptParams['message'] = maxLen('№' + subject.title + ' просрочена: ' + description, MAX_LEN);