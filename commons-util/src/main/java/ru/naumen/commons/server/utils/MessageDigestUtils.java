package ru.naumen.commons.server.utils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.security.MessageDigest;
import java.util.List;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ArrayUtils;

import com.google.common.base.Preconditions;

import ru.naumen.commons.shared.FxException;

/**
 * Вспомогательные методы для расчета hash-sum
 * <AUTHOR>
 */
public class MessageDigestUtils
{
    /**
     * Соль для усложнения "подделки" клиентом hash-суммы.
     * В случае изменения исправить sha256.sh 
     */
    private static final String SALT = "{66d4d9e9390e090854a95dc54e85d27c1eeae80f45083758e379dd34b5a7a83e}\n";

    /**
     * Соль, используемая при шифровке и расшифровке встроенных приложений
     */
    private static final byte[] BYTE_SALT = { 87, -20, 69, 9, 60, -45, -5, -101, 91, 101, 118, 89, 114, 41, -122, -24,
            -34, 50, 16, -69, -55, -63, 120, -27, -108, -47, 96, -15, 14, -105, 54, 72, -26, 66, -101, -121, 63, -96,
            -73, -53, 89, -111, 62, 80, -28, -34, -47, -36, -60, 53, -40, -118, -34, -116, 111, -25, 83, 84, -47, -97 };

    private static final String ZERO_STR = "0";
    private static final char ZERO_CHAR = '0';

    public static byte[] md5(byte[] content)
    {
        return DigestUtils.md5(content);
    }

    public static byte[] md5WithSalt(byte[] content)
    {
        return md5(addSalt(content, BYTE_SALT));
    }

    public static String sha256(String msg)
    {
        return bytesToHexString(sha256Bytes(msg));
    }

    public static String sha256(byte[] bytes)
    {
        return bytesToHexString(DigestUtils.sha256(bytes));
    }

    public static byte[] sha256Bytes(String msg)
    {
        Preconditions.checkNotNull(msg, "Message can't be null");
        return DigestUtils.sha256(msg);
    }

    /**
     * Вычисляет хэш SHA-256 со "стандартной" солью (bin/sha256.sh)
     */
    public static String sha256WithStandardSalt(String msg)
    {
        return sha256(msg + SALT);
    }

    /**
     * Вычисляет хэш SHA-256 со "стандартной" солью (bin/sha256.sh)
     */
    public static String sha256WithStandardSalt(byte[] bytes)
    {
        return sha256(addSalt(bytes, SALT.getBytes(StandardCharsets.UTF_8)));
    }

    public static String md5HashForFiles(List<Path> files)
    {
        if (files.isEmpty())
        {
            return null;
        }
        try
        {
            MessageDigest md = MessageDigest.getInstance("MD5");
            files.forEach(f -> updateMd5(md, f.toFile()));
            return DigestUtils.md5Hex(md.digest());
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    private static void updateMd5(MessageDigest md, File file)
    {
        try
        {
            md.update(FileUtils.readFileToByteArray(file));
        }
        catch (Exception e)
        {
            throw new FxException("On-start settings migration failed. Can't read file: " + file.getName());
        }
    }

    /**
     * Конвертирует набор байт в 16-ричное число в виде строки.<br>
     * Работает в 150 раз быстрее, чем
     * <p><code>new BigInteger(1, hash).toString(16);</code></p>
     * Обеспечивает обратную совместимость, удаляя нули в начале.
     */
    static String bytesToHexString(byte[] bytes)
    {
        return removeLeadingZeroes(Hex.encodeHexString(bytes));
    }

    /**
     * Удалить нули в начале 16-ричного числа
     */
    static String removeLeadingZeroes(String hex)
    {
        if (hex.isEmpty())
        {
            return ZERO_STR;
        }
        int start;
        for (start = 0; start < hex.length() - 1; start++)
        {
            if (hex.charAt(start) != ZERO_CHAR)
            {
                break;
            }
        }
        return hex.substring(start);
    }

    private static byte[] addSalt(byte[] content, byte[] salt)
    {
        return ArrayUtils.addAll(content, salt);
    }
}
