package ru.naumen.ui.api.delegates;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.springframework.http.ResponseEntity;

import ru.naumen.core.bo.CoreFile;
import ru.naumen.generated.model.BOFileDto;
import ru.naumen.generated.model.EmployeeDto;
import ru.naumen.metainfo.shared.elements.CoreAttributeGroup;
import ru.naumen.metainfo.shared.elements.CoreMetaClass;
import ru.naumen.ui.InitMocksBeforeEachBaseJdkTest;
import ru.naumen.ui.api.helpers.UIMetaInfoHelper;
import ru.naumen.ui.api.mapper.UIBoMapperGroup.UIBoListMapper;
import ru.naumen.ui.api.mapper.UIBoMapperGroup.UIBoMapper;
import ru.naumen.ui.api.mapper.UIBoMapperGroup.UICommentListMapper;
import ru.naumen.ui.api.mapper.UIBoMapperGroup.UIFileListMapper;
import ru.naumen.ui.api.services.ObjectsService;
import ru.naumen.ui.api.services.UIValidationService;
import ru.naumen.ui.api.utils.UIDateUtils;
import ru.naumen.ui.services.common.UIAttributeService;
import ru.naumen.ui.services.surface.UISurfaceService;
import ru.naumen.ui.settings.entity.contents.datasource.list.system.UISysObjectListDS;
import ru.naumen.ui.utils.ObjectUtils;
import ru.naumen.ui.utils.UITestUtils;

/**
 * Тестирование делегата для работы с файлами {@link FilesApiDelegateImpl}
 */
public class DataApiDelegateTest extends InitMocksBeforeEachBaseJdkTest
{
    private DataApiDelegateImpl delegate;

    @Mock
    private UIBoMapper boMapper;
    @Mock
    private UIBoListMapper boListMapper;
    @Mock
    private UICommentListMapper commentMapper;
    @Mock
    private UIFileListMapper fileMapper;
    @Mock
    private ObjectsService objectsService;
    @Mock
    private UIAttributeService attributeService;
    @Mock
    private UIValidationService validationService;
    @Mock
    private UIMetaInfoHelper metaInfoHelper;

    @Before
    public void setUp()
    {
        delegate = new DataApiDelegateImpl(
                objectsService,
                boMapper,
                attributeService,
                boListMapper,
                commentMapper,
                fileMapper,
                validationService,
                metaInfoHelper);
    }

    /**
     * Тестирование получения содержимого из списка БО файл
     */
    @Test
    public void testGetFileList()
    {
        String applicationId = UITestUtils.randomString(10);
        String pageId = UITestUtils.randomString(10);
        String objectId = UITestUtils.randomString(10);
        int limit = UITestUtils.randomInt(100);
        int offset = UITestUtils.randomInt();
        String orderingAttr = null;
        String order = UITestUtils.randomString(10);
        final int countFiles = 3;
        String objectUUID = ObjectUtils.pageIdToUUID(pageId, objectId);

        String dataSourceId = UITestUtils.randomString(10);
        UISysObjectListDS dataSource = mock(UISysObjectListDS.class);
        when(dataSource.getId()).thenReturn(dataSourceId);
        when(validationService.getDataSourceWithIdValidate(dataSourceId)).thenReturn(dataSource);

        CoreMetaClass metaClass = mock(CoreMetaClass.class);
        CoreAttributeGroup coreAttributeGroup = mock(CoreAttributeGroup.class);
        when(metaClass.getAttributeGroup(dataSource.getAttrGroupCode())).thenReturn(coreAttributeGroup);

        CoreFile coreFile = FilesApiDelegateJdkTest.getMockCoreFile();
        Optional<OffsetDateTime> optionalCreationDate = UIDateUtils.toOffsetDateTime(coreFile.getCreationDate());
        OffsetDateTime creationDate = optionalCreationDate.orElseGet(OffsetDateTime::now);
        BOFileDto fileDto = new BOFileDto()
                .author(new EmployeeDto().title(coreFile.getAuthor().getTitle()))
                .creationDate(creationDate)
                .mimeType(coreFile.getMimeType())
                .size((int)coreFile.getFileSize());

        List<CoreFile> files = new ArrayList<>();
        List<BOFileDto> filesDto = new ArrayList<>();
        for (int ind = 0; ind < countFiles; ind ++)
        {
            files.add(coreFile);
            filesDto.add(fileDto);
        }
        when(fileMapper.toDto(eq(coreFile), anyString(), ArgumentMatchers.any())).thenReturn(fileDto);
        when(objectsService.listFiles(dataSource, objectUUID, offset, limit, orderingAttr, order))
                .thenReturn(files);

        ResponseEntity<List<BOFileDto>> actual =
                delegate.getFileList(dataSource.getId(), objectUUID, limit, offset, applicationId, orderingAttr,
                        order);
        List<BOFileDto> actualBody = actual.getBody();
        Assert.assertEquals(filesDto, actualBody);
    }
}
