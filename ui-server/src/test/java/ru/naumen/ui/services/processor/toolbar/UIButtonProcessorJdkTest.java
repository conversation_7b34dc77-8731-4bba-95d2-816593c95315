package ru.naumen.ui.services.processor.toolbar;

import static org.mockito.Mockito.when;
import static ru.naumen.ui.utils.UITestUtils.randomString;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;

import ru.naumen.core.services.CoreIconService;
import ru.naumen.ui.models.toolbar.UIButton;
import ru.naumen.ui.models.toolbar.UIButtonType;
import ru.naumen.ui.models.toolbar.UIButtonView;
import ru.naumen.ui.models.toolbar.action.UIAction;
import ru.naumen.ui.models.toolbar.action.UIActionStub;
import ru.naumen.ui.services.filter.DataFilterService;
import ru.naumen.ui.services.processor.AbstractUIProcessorJdkTest;
import ru.naumen.ui.settings.entity.common.UILocalizedString;
import ru.naumen.ui.settings.entity.toolbar.UIButtonSettings;
import ru.naumen.ui.settings.entity.toolbar.action.UIActionSettings;
import ru.naumen.ui.settings.entity.toolbar.action.UIOpenPageActionSettings;
import ru.naumen.ui.settings.entity.toolbar.action.UIToolActionSettings.AppliesTo;

/**
 * Тесты на процессинг кнопок панели инструментов
 *
 * <AUTHOR>
 * @since 02.05.2024
 */
public class UIButtonProcessorJdkTest extends AbstractUIProcessorJdkTest
{
    private UIButtonProcessor buttonProcessor;
    @Mock
    private DataFilterService filterService;

    private final String pageId = randomString();
    private final String buttonId = randomString();
    private final String actionId = randomString();
    private final String caption = randomString();
    private final String iconCode = randomString();
    private final List<UILocalizedString> localizedCaption12 =
            List.of(new UILocalizedString(caption, "ru"));
    @Mock
    private CoreIconService iconService;
    @Before
    public void setUp()
    {
        super.setUp();
        when(iconService.getFileId(iconCode)).thenReturn(iconCode);
        buttonProcessor = new UIButtonProcessor(l10nUtils, iconService);
        buttonProcessor.init(processingService, authRuleService, filterService);
    }

    /**
     * Тест процессинга кнопки
     */
    @Test
    public void testProcessButton()
    {
        UIActionSettings actionSettings = new UIOpenPageActionSettings(actionId, AppliesTo.CURRENT_OBJECT, pageId);
        UIButtonSettings buttonSettings = new UIButtonSettings(buttonId,
                localizedCaption12, UIButtonType.LINK, UIButtonView.TEXT_AND_ICON, actionSettings);
        buttonSettings.setIconCode(iconCode);

        UIAction action = new UIActionStub(actionId);
        when(processingService.process(actionSettings, context)).thenReturn(action);
        when(l10nUtils.getLocalizedValue(localizedCaption12)).thenReturn(caption);

        UIButton button = buttonProcessor.process(buttonSettings, context);

        Assert.assertNotNull(button);
        Assert.assertEquals(buttonId, button.getId());
        Assert.assertEquals(caption, button.getText());
        Assert.assertEquals(UIButtonType.LINK, button.getType());
        Assert.assertEquals(UIButtonView.TEXT_AND_ICON, button.getView());
        Assert.assertEquals(iconCode, button.getIconFileId());
        UIAction buttonAction = button.getAction();
        Assert.assertEquals(actionId, buttonAction.getId());
    }
}