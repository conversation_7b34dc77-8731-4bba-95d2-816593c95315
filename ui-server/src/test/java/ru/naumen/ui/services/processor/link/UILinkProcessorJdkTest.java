package ru.naumen.ui.services.processor.link;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static ru.naumen.ui.utils.BoTestUtils.mockBO;
import static ru.naumen.ui.utils.BoTestUtils.mockEmployee;
import static ru.naumen.ui.utils.MetaClassesTestUtils.mockClassFqn;
import static ru.naumen.ui.utils.UITestUtils.randomInt;
import static ru.naumen.ui.utils.UITestUtils.randomString;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

import ru.naumen.authorization.AuthorizationService;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.bo.CoreEmployee;
import ru.naumen.core.server.CoreAppContextService;
import ru.naumen.core.services.CoreAuthenticationService;
import ru.naumen.core.services.CoreBusinessObjectService;
import ru.naumen.metainfo.CoreMetaInfoService;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.CoreMetaClass;
import ru.naumen.ui.InitMocksBeforeEachBaseJdkTest;
import ru.naumen.ui.models.link.UILinkToPage;
import ru.naumen.ui.models.link.UILinkToUrl;
import ru.naumen.ui.services.page.UIPageService;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.ui.settings.entity.common.UIAttributeReference;
import ru.naumen.ui.settings.entity.common.UIClassFqn;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.link.UILinkToUrlSettings;
import ru.naumen.ui.settings.entity.link.UIRelativePageLinkSettings;
import ru.naumen.ui.settings.entity.link.UITypeOfPage;
import ru.naumen.ui.utils.UILinkTestUtils;
import ru.naumen.ui.utils.UITestUtils;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * Тестирование процессоров ссылок
 *
 * <AUTHOR>
 * @since 03.10.2023
 */
public class UILinkProcessorJdkTest extends InitMocksBeforeEachBaseJdkTest
{
    private final String PAGE_ID_INCORRECT = "Идентификатор страницы не совпал с ожидаемым";
    private final String OBJECT_ID_INCORRECT = "Идентификатор объекта не совпал с ожидаемым";
    private final String PAGE_LINK_EMPTY = "Пустая ссылка на конкретную страницу";

    private UIContext context;

    @Mock
    private CoreAuthenticationService authenticationService;
    @Mock
    private CoreBusinessObjectService businessObjectService;
    @Mock
    private UIPageService pageService;
    @Mock
    private CoreMetaInfoService coreMetainfoService;
    @Mock
    private AuthorizationService authorizationService;
    @Mock
    private CoreAppContextService coreAppContextService;
    @Mock
    private UISettingsService settingsService;

    private UIRelativePageLinkProcessor relativePageLinkProcessor;

    @Before
    public void setUp()
    {
        context = new UIContext(randomString());
        UIRelativePageLinkProcessorHelper processorHelper = new UIRelativePageLinkProcessorHelper(
                authenticationService,
                businessObjectService,
                coreMetainfoService,
                authorizationService
        );
        relativePageLinkProcessor = new UIRelativePageLinkProcessor(
                pageService, coreAppContextService, processorHelper, settingsService);

        Mockito.when(settingsService.getApplicationSettings(context.getApplicationId()))
                .thenReturn(new UIApplicationSettings(
                        context.getApplicationId(),
                        context.getApplicationId(),
                        context.getApplicationId(),
                        null,
                        null
                ));
    }

    /**
     * Тестирование построения произвольной ссылки
     */
    @Test
    public void testUrlLinkProcessing()
    {
        UILinkToUrlSettings settings = UILinkTestUtils.createUrlLinkSettings(context.getApplicationId());
        UILinkToUrl link = new UILinkToUrlProcessor().process(settings, context);
        Assert.assertNotNull("Пустая произвольная ссылка", link);
        Assert.assertEquals("Адрес ссылки не соответствует ожидаемому", settings.getUrl(), link.url());
    }

    /**
     * Тестирование построения ссылки на конкретную страницу
     */
    @Test
    public void testPageLinkProcessing()
    {
        ru.naumen.ui.settings.entity.link.UILinkToPageSettings settings = UILinkTestUtils.createPageLinkSettings(context.getApplicationId());
        UILinkToPage link = new UILinkToPageProcessor(coreAppContextService, settingsService).process(settings,
                context);
        Assert.assertNotNull(PAGE_LINK_EMPTY, link);
        Assert.assertEquals("Страница не соответствует ожидаемой", settings.getPageId(), link.getPageId());
        Assert.assertEquals("Объект не соответствует ожидаемому", settings.getObjectId(), link.getObjectId());
    }

    /**
     * Тестирование построения ссылки на компанию
     */
    @Test
    public void testRootPageProcessing()
    {
        // настраиваем ссылку
        UIRelativePageLinkSettings settings =
                new UIRelativePageLinkSettings(UITestUtils.randomString(), UITypeOfPage.ROOT);

        String clazz = randomString();
        CoreClassFqn classFqn = mockClassFqn(clazz, null);
        long objectId = UniqueNumbersGenerator.nextLong();
        CoreBusinessObject root = mockBO(classFqn, objectId);

        Mockito.when(businessObjectService.getRoot()).thenReturn(root);
        String pageId = setDefaultPageToClass(classFqn);

        UILinkToPage pageLink = relativePageLinkProcessor.process(settings, context);
        // проверяем результат построения ссылки
        Assert.assertNotNull(PAGE_LINK_EMPTY, pageLink);
        Assert.assertEquals(PAGE_ID_INCORRECT, pageId, pageLink.getPageId());
        Assert.assertEquals(OBJECT_ID_INCORRECT, String.valueOf(objectId), pageLink.getObjectId());
    }

    /**
     * Тестирование построения ссылки на объект, связанный с компанией
     */
    @Test
    public void testReferenceToRootPageProcessing()
    {
        // настраиваем ссылку
        UIRelativePageLinkSettings settings =
                new UIRelativePageLinkSettings(UITestUtils.randomString(), UITypeOfPage.REFERENCE_TO_ROOT);

        String clazz = randomString();
        CoreClassFqn classFqn = mockClassFqn(clazz, null);
        long objectId = UniqueNumbersGenerator.nextLong();
        CoreBusinessObject root = mockBO(classFqn, objectId);
        Mockito.when(businessObjectService.getRoot()).thenReturn(root);

        // создаем связь и связанный объект
        String relationAttrId = randomString();
        UIAttributeReference attrReference = new UIAttributeReference(new UIClassFqn(clazz), relationAttrId);
        settings.setRelationAttributeChain(List.of(attrReference));

        CoreMetaClass metaClass = Mockito.mock(CoreMetaClass.class);
        Mockito.when(coreMetainfoService.getMetaClass(classFqn)).thenReturn(metaClass);
        Mockito.when(metaClass.hasAttribute(relationAttrId)).thenReturn(true);
        Mockito.when(authorizationService.hasAttrPermission(root, relationAttrId, false)).thenReturn(true);

        UIClassFqn relatedClassFqn = new UIClassFqn(randomString());
        CoreBusinessObject relatedBo = mockBO(relatedClassFqn, randomInt());
        Mockito.when(businessObjectService.getAttributeValue(root, relationAttrId)).thenReturn(relatedBo);
        String relatedPageId = setDefaultPageToClass(relatedClassFqn);

        UILinkToPage pageLink = relativePageLinkProcessor.process(settings, context);
        // проверяем результат построения ссылки
        Assert.assertNotNull(PAGE_LINK_EMPTY, pageLink);
        Assert.assertEquals(PAGE_ID_INCORRECT, relatedPageId, pageLink.getPageId());
        Assert.assertEquals(OBJECT_ID_INCORRECT, String.valueOf(relatedBo.getId()), pageLink.getObjectId());

        // проверяем случай когда у пользователя нет прав на атрибут связи
        Mockito.when(authorizationService.hasAttrPermission(root, relationAttrId, false)).thenReturn(false);
        pageLink = relativePageLinkProcessor.process(settings, context);
        Assert.assertNull("Ссылка доступна!", pageLink);
        Mockito.when(authorizationService.hasAttrPermission(root, relationAttrId, false)).thenReturn(true);

        // проверяем случай когда объект связи не заполнен
        Mockito.when(businessObjectService.getAttributeValue(root, relationAttrId)).thenReturn(null);
        pageLink = relativePageLinkProcessor.process(settings, context);
        Assert.assertNull("Ссылка доступна!", pageLink);
    }

    /**
     * Тестирование построения ссылки на текущего пользователя
     */
    @Test
    public void testCurrentUserPageProcessing()
    {
        // настраиваем ссылку
        UIRelativePageLinkSettings settings =
                new UIRelativePageLinkSettings(UITestUtils.randomString(), UITypeOfPage.CURRENT_USER);

        CoreEmployee employee = setCurrentEmployee();
        String pageId = setDefaultPageToClass(employee.getMetaClass());

        UILinkToPage pageLink = relativePageLinkProcessor.process(settings, context);

        // проверяем результат построения ссылки
        Assert.assertNotNull(PAGE_LINK_EMPTY, pageLink);
        Assert.assertEquals(PAGE_ID_INCORRECT, pageId, pageLink.getPageId());
        Assert.assertEquals(OBJECT_ID_INCORRECT, String.valueOf(employee.getId()), pageLink.getObjectId());
    }

    /**
     * Тестирование построения ссылки на отдел текущего пользователя
     */
    @Test
    public void testCurrentUserOUPageProcessing()
    {
        // настраиваем ссылку
        UIRelativePageLinkSettings settings =
                new UIRelativePageLinkSettings(UITestUtils.randomString(), UITypeOfPage.CURRENT_USER_OU);

        CoreEmployee employee = setCurrentEmployee();
        String pageId = setDefaultPageToClass(employee.getParent().getMetaClass());

        UILinkToPage pageLink = relativePageLinkProcessor.process(settings, context);

        // проверяем результат построения ссылки
        Assert.assertNotNull(PAGE_LINK_EMPTY, pageLink);
        Assert.assertEquals(PAGE_ID_INCORRECT, pageId, pageLink.getPageId());
        Assert.assertEquals(OBJECT_ID_INCORRECT, String.valueOf(employee.getParent().getId()), pageLink.getObjectId());
    }

    private String setDefaultPageToClass(CoreClassFqn classFqn)
    {
        String pageId = randomString();
        Mockito.when(pageService.getDefaultPageIdByClass(eq(classFqn), anyString())).thenReturn(pageId);
        return pageId;
    }

    private CoreEmployee setCurrentEmployee()
    {
        String clazz = randomString();
        CoreClassFqn classFqn = mockClassFqn(clazz, null);
        long objectId = UniqueNumbersGenerator.nextLong();

        CoreEmployee employee = mockEmployee(classFqn, objectId);
        Mockito.when(authenticationService.getCurrentEmployee()).thenReturn(employee);
        return employee;
    }
}